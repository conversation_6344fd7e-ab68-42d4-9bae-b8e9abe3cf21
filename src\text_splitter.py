from typing import List, Dict, Any
import re
import unicodedata

from config.config import MAX_LENGTH, CHUNK_OVERLAP

class TextSplitter:
    def __init__(self, max_length: int = MAX_LENGTH, overlap: int = CHUNK_OVERLAP):
        """
        初始化文本分割器
        :param max_length: 每个文本块的最大长度
        :param overlap: 相邻文本块的重叠长度
        """
        self.max_length = max_length
        self.overlap = overlap

        # 繁体字标点符号
        self.traditional_punctuation = "。，、；：？！""''（）【】《》〈〉「」『』〔〕"

        # 简体字标点符号
        self.simplified_punctuation = "。，,.:;!?！？"

        # 财报常见分割标记
        self.financial_markers = ["附註", "附注", "註", "注", "表", "圖", "图", "第", "章", "節", "节"]

    def split_text(self, text: str) -> List[str]:
        """
        将长文本分割成较小的块
        :param text: 输入文本
        :return: 文本块列表
        """
        if not text or len(text) <= self.max_length:
            return [text] if text else []

        print(f"    📝 文本长度: {len(text)} 字符，开始分割...")

        # 按句子分割文本
        sentences = self._split_into_sentences(text)
        print(f"    📝 分割为 {len(sentences)} 个句子")

        chunks = []
        current_chunk = []
        current_length = 0

        for i, sentence in enumerate(sentences):
            if i % 100 == 0 and i > 0:
                print(f"    📝 处理进度: {i}/{len(sentences)} 句子")
            sentence_length = len(sentence)

            # 如果单个句子超过最大长度，则需要进一步分割
            if sentence_length > self.max_length:
                if current_chunk:
                    chunks.append("".join(current_chunk))
                    current_chunk = []
                    current_length = 0

                # 分割长句子
                print(f"    📝 分割长句子（{sentence_length} 字符）...")
                sub_chunks = self._split_long_sentence(sentence)
                chunks.extend(sub_chunks)
                continue

            # 如果添加当前句子后超过最大长度，则开始新的chunk
            if current_length + sentence_length > self.max_length:
                chunks.append("".join(current_chunk))
                
                # 保留重叠部分
                if self.overlap > 0:
                    overlap_text = "".join(current_chunk[-self.overlap:])
                    current_chunk = [overlap_text]
                    current_length = len(overlap_text)
                else:
                    current_chunk = []
                    current_length = 0

            current_chunk.append(sentence)
            current_length += sentence_length

        # 添加最后一个chunk
        if current_chunk:
            chunks.append("".join(current_chunk))

        print(f"    ✅ 文本分割完成，生成 {len(chunks)} 个文本块")
        return chunks

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        将文本分割成句子（支持繁体字）
        """
        # 检测文本类型
        is_traditional = self._is_traditional_chinese(text)

        if is_traditional:
            # 繁体中文句子分割模式
            pattern = r'([。！？\?!]+"?|[」』】〉》】])'
        else:
            # 简体中文句子分割模式
            pattern = r'([。！？\?!]+"?)'

        sentences = []
        start = 0

        # 先按财报标记分割（如果是财报文档）
        if self._is_financial_document(text):
            text = self._preprocess_financial_text(text)

        for match in re.finditer(pattern, text):
            end = match.end()
            sentence = text[start:end]
            if sentence.strip():
                sentences.append(sentence)
            start = end

        # 处理最后一部分
        if start < len(text):
            last_sentence = text[start:]
            if last_sentence.strip():
                sentences.append(last_sentence)

        return sentences

    def _is_traditional_chinese(self, text: str) -> bool:
        """
        检测是否为繁体中文
        """
        # 简单的繁体字检测
        traditional_chars = "繁體財報營業額淨利潤資產負債權益現金流動資本"
        simplified_chars = "繁体财报营业额净利润资产负债权益现金流动资本"

        traditional_count = sum(1 for char in text if char in traditional_chars)
        simplified_count = sum(1 for char in text if char in simplified_chars)

        # 如果繁体字符明显多于简体字符，判断为繁体
        return traditional_count > simplified_count and traditional_count > 10

    def _is_financial_document(self, text: str) -> bool:
        """
        检测是否为财报文档
        """
        financial_keywords = ["財務報表", "财务报表", "損益表", "损益表", "資產負債表", "资产负债表",
                             "現金流量表", "现金流量表", "股東權益", "股东权益", "營業收入", "营业收入"]

        return any(keyword in text for keyword in financial_keywords)

    def _preprocess_financial_text(self, text: str) -> str:
        """
        预处理财报文本，在关键位置添加分割标记
        """
        # 在财报常见标记前添加换行，便于分割
        for marker in self.financial_markers:
            # 为表格、图表、附注等添加分割标记
            pattern = f"({marker}[0-9一二三四五六七八九十]+[：:])"
            text = re.sub(pattern, r"\n\1", text)

        return text

    def _split_long_sentence(self, sentence: str) -> List[str]:
        """
        分割过长的句子
        """
        chunks = []
        start = 0
        while start < len(sentence):
            # 确保不会在字符中间截断
            end = start + self.max_length
            if end < len(sentence):
                # 向后查找一个合适的分割点
                while end > start and not self._is_valid_break_point(sentence[end]):
                    end -= 1
                if end == start:
                    end = start + self.max_length  # 如果找不到合适的分割点，就强制分割

            chunks.append(sentence[start:end])

            # 修复死循环问题：确保start总是前进
            if self.overlap > 0 and end > start + self.overlap:
                start = end - self.overlap
            else:
                start = end

            # 安全检查：如果start没有前进，强制前进
            if start <= (end - self.max_length):
                start = end

        return chunks

    def _is_valid_break_point(self, char: str) -> bool:
        """
        判断是否是合适的分割点（支持繁体字标点）
        """
        # 支持简体和繁体标点符号
        all_punctuation = self.simplified_punctuation + self.traditional_punctuation
        return char in all_punctuation

    def split_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分割文档列表中的文本内容
        :param documents: 文档列表，每个文档包含content字段
        :return: 分割后的文档列表
        """
        split_docs = []
        for doc in documents:
            if doc["type"] == "text":
                content = doc["content"]
                chunks = self.split_text(content)
                
                for i, chunk in enumerate(chunks):
                    split_doc = doc.copy()
                    split_doc["content"] = chunk
                    split_doc["chunk_id"] = i + 1
                    split_docs.append(split_doc)
            else:
                # 对于非文本类型（如表格），直接添加
                doc["chunk_id"] = 1
                split_docs.append(doc)
                
        return split_docs

    def split_large_document(self, document: Dict[str, Any], max_chunks_per_batch: int = 100) -> List[Dict[str, Any]]:
        """
        分割大型文档（如财报PDF），支持分批处理

        Args:
            document: 文档对象
            max_chunks_per_batch: 每批最大块数

        Returns:
            List[Dict[str, Any]]: 分割后的文档块列表
        """
        if document["type"] != "text":
            document["chunk_id"] = 1
            return [document]

        content = document["content"]
        print(f"    📄 处理大型文档，内容长度: {len(content)} 字符")

        # 检测文档类型
        is_financial = self._is_financial_document(content)
        is_traditional = self._is_traditional_chinese(content)

        print(f"    📊 文档类型: {'财报' if is_financial else '普通'}, {'繁体' if is_traditional else '简体'}")

        # 分割文本
        chunks = self.split_text(content)
        print(f"    ✂️ 分割完成，生成 {len(chunks)} 个文本块")

        # 创建文档块
        split_docs = []
        for i, chunk in enumerate(chunks):
            split_doc = document.copy()
            split_doc["content"] = chunk
            split_doc["chunk_id"] = i + 1

            # 添加文档类型元数据
            split_doc["metadata"] = {
                "is_financial": is_financial,
                "is_traditional": is_traditional,
                "chunk_index": i,
                "total_chunks": len(chunks),
                "content_length": len(chunk)
            }

            split_docs.append(split_doc)

        return split_docs