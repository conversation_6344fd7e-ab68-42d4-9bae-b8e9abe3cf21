import os
from typing import List, Dict, Any
from pathlib import Path
from docx import Document
from PyPDF2 import PdfReader
import markdown

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

from config.config import SUPPORTED_EXTENSIONS
from .pdf_detector import PDFDetector
from .ocr_processor import OCRProcessor
from .text_converter import TextConverter
from .table_row_processor import TableRowProcessor

class DocumentProcessor:
    def __init__(self):
        """初始化文档处理器"""
        self.pdf_detector = PDFDetector()
        self.ocr_processor = OCRProcessor(use_gpu=True)
        self.text_converter = TextConverter()
        self.table_row_processor = TableRowProcessor()

        features = ["表格识别", "繁简转换", "行级向量化"]
        if self.ocr_processor.is_available():
            features.append("GPU加速OCR")

        print(f"✓ 文档处理器初始化成功（支持{', '.join(features)}）")

    def process_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        处理单个文件，返回处理后的文档片段列表
        每个片段包含：文本内容、页码、来源文件等信息
        """
        file_path = Path(file_path) if not isinstance(file_path, Path) else file_path
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 获取文件类型
        file_type = self._get_file_type(file_path)
        
        # 根据文件类型调用相应的处理方法
        if file_type == "pdf":
            return self._process_pdf(file_path)
        elif file_type == "word":
            return self._process_word(file_path)
        elif file_type == "markdown":
            return self._process_markdown(file_path)
        elif file_type == "text":
            return self._process_text(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {file_path.suffix}")

    def _get_file_type(self, file_path: Path) -> str:
        """根据文件扩展名判断文件类型"""
        suffix = file_path.suffix.lower()
        for file_type, extensions in SUPPORTED_EXTENSIONS.items():
            if suffix in extensions:
                return file_type
        raise ValueError(f"不支持的文件类型: {suffix}")

    def _process_pdf(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理PDF文件（智能检测PDF类型）"""
        chunks = []

        try:
            # 统一处理：不论PDF类型，都进行表格检测和处理
            print(f"    📊 使用统一表格检测处理...")
            chunks = self._process_pdf_with_unified_table_detection(file_path)

        except Exception as e:
            print(f"    ❌ PDF智能处理失败: {e}")
            # 回退到标准文本提取
            chunks = self._process_pdf_with_text_extraction(file_path)

        return chunks

    def _process_pdf_with_unified_table_detection(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        统一PDF处理：不论文本型还是图像型，都进行表格检测

        Args:
            file_path: PDF文件路径

        Returns:
            List[Dict[str, Any]]: 处理后的文档片段列表
        """
        chunks = []

        try:
            print(f"    🔍 开始统一表格检测处理...")

            # 第一步：表格检测（使用pdfplumber）
            tables_by_page = {}
            if self.ocr_processor.table_detection_enabled:
                print(f"    📊 检测PDF中的表格...")

                # 检测所有页面的表格
                import fitz
                doc = fitz.open(str(file_path))
                total_pages = len(doc)
                total_tables = 0

                for page_num in range(total_pages):
                    tables = self.ocr_processor.table_detector.detect_tables_in_page(str(file_path), page_num)
                    if tables:
                        tables_by_page[page_num] = tables
                        total_tables += len(tables)
                        print(f"      第{page_num + 1}页: 检测到 {len(tables)} 个表格")

                doc.close()
                print(f"    📊 总计检测到 {total_tables} 个表格")

            # 第二步：根据表格检测结果选择处理策略
            if tables_by_page:
                print(f"    ✅ 发现表格，使用表格优化处理")
                chunks = self._process_pdf_with_table_optimization(file_path, tables_by_page)
            else:
                print(f"    📝 未发现表格，使用标准处理")
                # 先尝试文本提取，如果效果不好再用OCR
                detection_result = self.pdf_detector.detect_pdf_type(file_path)
                recommendation = detection_result.get('recommendation', 'use_text_extraction')

                if recommendation == 'use_text_extraction':
                    chunks = self._process_pdf_with_text_extraction(file_path)
                else:
                    if self.ocr_processor.is_available():
                        chunks = self._process_pdf_with_ocr(file_path)
                    else:
                        chunks = self._process_pdf_with_text_extraction(file_path)

            return chunks

        except Exception as e:
            print(f"    ❌ 统一表格检测处理失败: {e}")
            # 回退到标准文本提取
            return self._process_pdf_with_text_extraction(file_path)

    def _process_pdf_with_table_optimization(self, file_path: Path, tables_by_page: dict) -> List[Dict[str, Any]]:
        """
        表格优化处理：混合使用表格检测和文本提取

        Args:
            file_path: PDF文件路径
            tables_by_page: 按页面分组的表格信息

        Returns:
            List[Dict[str, Any]]: 处理后的文档片段列表
        """
        chunks = []
        chunk_id = 0

        try:
            print(f"    🔧 开始表格优化处理...")

            import fitz
            doc = fitz.open(str(file_path))
            total_pages = len(doc)

            for page_num in range(total_pages):
                print(f"    🔄 处理第{page_num + 1}/{total_pages}页...")

                page_chunks = []

                # 检查该页是否有表格
                if page_num in tables_by_page:
                    tables = tables_by_page[page_num]
                    print(f"      📊 处理 {len(tables)} 个表格...")

                    # 处理表格
                    for table in tables:
                        # 转换表格为Markdown
                        markdown_table = self.ocr_processor.table_detector.convert_table_to_markdown(
                            table['data'], table['table_id']
                        )

                        if markdown_table.strip():
                            # 繁简转换
                            original_markdown = markdown_table
                            converted_markdown = self.text_converter.convert_traditional_to_simplified(markdown_table)
                            has_conversion = original_markdown != converted_markdown

                            # 创建完整表格片段
                            table_chunk = {
                                "content": converted_markdown,
                                "page": page_num + 1,
                                "source": str(file_path),
                                "type": "table",
                                "chunk_id": chunk_id,
                                "table_processed": True,
                                "traditional_converted": has_conversion,
                                "original_content": original_markdown if has_conversion else None,
                                "table_info": {
                                    "table_id": table['table_id'],
                                    "rows": table['rows'],
                                    "cols": table['cols'],
                                    "bbox": table['bbox']
                                }
                            }
                            page_chunks.append(table_chunk)
                            chunk_id += 1

                            # 生成行级向量片段
                            row_chunks = self.table_row_processor.process_table_chunk(table_chunk)
                            page_chunks.extend(row_chunks)
                            chunk_id += len(row_chunks)

                            conversion_info = "（已转换繁体）" if has_conversion else ""
                            print(f"      ✅ 表格 {table['table_id']} 转换完成{conversion_info}，生成 {len(row_chunks)} 个行级片段")

                    # 处理非表格区域的文本
                    non_table_regions = self.ocr_processor.table_detector.extract_non_table_regions(
                        str(file_path), page_num, tables
                    )

                    if non_table_regions:
                        # 对非表格区域进行文本提取或OCR
                        page = doc[page_num]
                        for region_bbox in non_table_regions:
                            # 先尝试文本提取
                            try:
                                rect = fitz.Rect(region_bbox)
                                region_text = page.get_text(clip=rect).strip()

                                if not region_text or len(region_text) < 10:
                                    # 文本提取效果不好，使用OCR
                                    if self.ocr_processor.is_available():
                                        region_text = self.ocr_processor._ocr_region(page, region_bbox)

                                if region_text.strip():
                                    # 繁简转换
                                    original_text = region_text
                                    converted_text = self.text_converter.convert_traditional_to_simplified(region_text)
                                    has_conversion = original_text != converted_text

                                    page_chunks.append({
                                        "content": converted_text,
                                        "page": page_num + 1,
                                        "source": str(file_path),
                                        "type": "text",
                                        "chunk_id": chunk_id,
                                        "table_processed": False,
                                        "traditional_converted": has_conversion,
                                        "original_content": original_text if has_conversion else None
                                    })
                                    chunk_id += 1

                            except Exception as e:
                                print(f"      ⚠ 区域文本提取失败: {e}")
                                continue
                else:
                    # 该页无表格，使用标准文本提取
                    page = doc[page_num]
                    page_text = page.get_text().strip()

                    if page_text:
                        # 繁简转换
                        original_text = page_text
                        converted_text = self.text_converter.convert_traditional_to_simplified(page_text)
                        has_conversion = original_text != converted_text

                        page_chunks.append({
                            "content": converted_text,
                            "page": page_num + 1,
                            "source": str(file_path),
                            "type": "text",
                            "chunk_id": chunk_id,
                            "table_processed": False,
                            "traditional_converted": has_conversion,
                            "original_content": original_text if has_conversion else None
                        })
                        chunk_id += 1

                # 添加页面片段到总列表
                chunks.extend(page_chunks)

                # 统计信息
                table_count = len([c for c in page_chunks if c['type'] == 'table'])
                text_count = len([c for c in page_chunks if c['type'] == 'text'])
                total_chars = sum(len(c['content']) for c in page_chunks)

                print(f"      ✅ 第{page_num + 1}页完成: 表格{table_count}个, 文本{text_count}个, 共{total_chars}字符")

            doc.close()

            print(f"    ✅ 表格优化处理完成，共生成 {len(chunks)} 个片段")
            return chunks

        except Exception as e:
            print(f"    ❌ 表格优化处理失败: {e}")
            import traceback
            traceback.print_exc()
            # 回退到标准处理
            return self._process_pdf_with_text_extraction(file_path)

    def _process_pdf_with_text_extraction(self, file_path: Path) -> List[Dict[str, Any]]:
        """使用文本提取处理PDF（原有方法）"""
        chunks = []
        all_tables = []

        try:
            print(f"    📖 正在读取PDF文件...")
            # 设置当前PDF路径供pdfplumber使用
            self._current_pdf_path = file_path

            reader = PdfReader(file_path)
            total_pages = len(reader.pages)
            print(f"    📄 PDF共 {total_pages} 页，开始文本提取...")

            if PDFPLUMBER_AVAILABLE:
                print(f"    ✅ pdfplumber可用作备选提取方法")

            chunk_id = 0
            for page_num, page in enumerate(reader.pages, 1):
                if page_num % 10 == 0 or page_num == total_pages:
                    print(f"    📄 处理进度: {page_num}/{total_pages} 页")

                try:
                    # 尝试多种文本提取方法
                    text = self._extract_text_from_page(page, page_num)
                    if text.strip():
                        # 检测表格
                        page_tables = self.table_processor.detect_tables(text, page_num)
                        all_tables.extend(page_tables)

                        # 处理文本内容
                        processed_text = self._process_text_with_tables(text, page_tables)

                        chunks.append({
                            "content": processed_text,
                            "page": page_num,
                            "source": str(file_path),
                            "type": "text",
                            "chunk_id": chunk_id,
                            "tables": page_tables  # 添加表格信息
                        })
                        chunk_id += 1

                        # 为每个表格创建单独的块
                        for table in page_tables:
                            table_markdown = self.table_processor.convert_to_markdown(table)
                            chunks.append({
                                "content": table_markdown,
                                "page": page_num,
                                "source": str(file_path),
                                "type": "table",
                                "chunk_id": chunk_id,
                                "table_info": table
                            })
                            chunk_id += 1

                except Exception as e:
                    print(f"    ⚠ 跳过第{page_num}页（处理失败）: {e}")
                    continue

            print(f"    ✅ PDF处理完成，提取了 {len(chunks)} 个文本块")
            if all_tables:
                print(f"    📊 识别了 {len(all_tables)} 个表格")

        except Exception as e:
            raise Exception(f"处理PDF文件时出错: {str(e)}")
        return chunks

    def _process_word(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Word文件"""
        chunks = []
        try:
            doc = Document(file_path)
            current_page = 1  # Word文档需要估算页码
            chunk_id = 0

            # 合并所有段落为一个文档
            all_text = []
            for para in doc.paragraphs:
                if para.text.strip():
                    all_text.append(para.text)

            if all_text:
                chunks.append({
                    "content": "\n".join(all_text),
                    "page": current_page,
                    "source": str(file_path),
                    "type": "text",
                    "chunk_id": chunk_id
                })
        except Exception as e:
            raise Exception(f"处理Word文件时出错: {str(e)}")
        return chunks



    def _process_markdown(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()

            # 直接使用原始Markdown内容，不转换为HTML
            if md_content.strip():
                return [{
                    "content": md_content,
                    "page": 1,
                    "source": str(file_path),
                    "type": "text",
                    "chunk_id": 0
                }]
            return []
        except Exception as e:
            raise Exception(f"处理Markdown文件时出错: {str(e)}")

    def _process_text(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理纯文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if content.strip():
                return [{
                    "content": content,
                    "page": 1,
                    "source": str(file_path),
                    "type": "text",
                    "chunk_id": 0
                }]
            return []
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                if content.strip():
                    return [{
                        "content": content,
                        "page": 1,
                        "source": str(file_path),
                        "type": "text",
                        "chunk_id": 0
                    }]
                return []
            except Exception as e:
                raise Exception(f"文本文件读取失败: {str(e)}")
        except Exception as e:
            raise Exception(f"文本文件处理失败: {str(e)}")

    def _process_text_with_tables(self, text: str, tables: List[Dict[str, Any]]) -> str:
        """
        处理包含表格的文本，在表格位置添加标记并进行繁简转换

        Args:
            text: 原始文本
            tables: 检测到的表格列表

        Returns:
            str: 处理后的文本
        """
        # 先进行繁简转换
        original_text = text
        converted_text = self.text_converter.convert_traditional_to_simplified(text)

        # 检查是否进行了转换
        has_conversion = original_text != converted_text
        if has_conversion:
            conversion_count = sum(1 for i, (o, c) in enumerate(zip(original_text, converted_text)) if o != c)
            print(f"    ✅ 繁简转换: 转换了 {conversion_count} 个字符")

        processed_text = converted_text

        if not tables:
            return processed_text

        # 为每个表格添加引用标记
        for table in tables:
            table_title = table['title']
            # 表格标题也需要转换
            converted_title = self.text_converter.convert_traditional_to_simplified(table_title)
            table_id = table['metadata'].table_id

            # 在表格标题后添加引用标记
            reference_mark = f"\n\n[表格引用: {table_id}]\n"
            processed_text = processed_text.replace(converted_title, converted_title + reference_mark)

        return processed_text

    def _process_pdf_with_ocr(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        使用OCR处理PDF

        Args:
            file_path: PDF文件路径

        Returns:
            List[Dict[str, Any]]: 处理后的文档片段列表
        """
        try:
            print(f"    🔍 开始OCR处理...")

            # 使用OCR处理器提取文本（支持表格检测）
            ocr_chunks = self.ocr_processor.extract_text_from_pdf_with_tables(file_path)

            if not ocr_chunks:
                print(f"    ⚠ OCR未提取到内容，回退到文本提取")
                return self._process_pdf_with_text_extraction(file_path)

            # 处理OCR结果
            processed_chunks = []
            for chunk in ocr_chunks:
                content = chunk.get('content', '')

                if content.strip():
                    # 检测表格（OCR文本中的表格）
                    page_tables = self.table_processor.detect_tables(content, chunk.get('page', 1))

                    # 处理文本内容
                    processed_text = self._process_text_with_tables(content, page_tables)

                    # 添加处理后的文本块
                    processed_chunks.append({
                        "content": processed_text,
                        "page": chunk.get('page', 1),
                        "source": str(file_path),
                        "type": "text",
                        "chunk_id": chunk.get('chunk_id', 0),
                        "ocr_processed": True,
                        "tables": page_tables
                    })

                    # 为每个表格创建单独的块
                    for table in page_tables:
                        table_markdown = self.table_processor.convert_to_markdown(table)
                        processed_chunks.append({
                            "content": table_markdown,
                            "page": chunk.get('page', 1),
                            "source": str(file_path),
                            "type": "table",
                            "chunk_id": len(processed_chunks),
                            "ocr_processed": True,
                            "table_info": table
                        })

            print(f"    ✅ OCR处理完成，生成 {len(processed_chunks)} 个文档片段")
            return processed_chunks

        except Exception as e:
            print(f"    ❌ OCR处理失败: {e}")
            # 回退到文本提取
            return self._process_pdf_with_text_extraction(file_path)

    def _extract_text_from_page(self, page, page_num: int):
        """
        改进的PDF页面文本提取方法

        Args:
            page: PDF页面对象
            page_num: 页码

        Returns:
            str: 提取的文本
        """
        try:
            # 方法1: 标准PyPDF2提取
            text = page.extract_text()

            # 检查是否有乱码
            if self._is_garbled_text(text):
                print(f"    ⚠ 第{page_num}页检测到乱码，尝试pdfplumber...")

                # 方法2: 使用pdfplumber
                if PDFPLUMBER_AVAILABLE:
                    try:
                        text = self._extract_with_pdfplumber(page_num)
                        if text and not self._is_garbled_text(text):
                            print(f"    ✅ pdfplumber成功提取第{page_num}页")
                            return text
                    except Exception as e:
                        print(f"    ⚠ pdfplumber提取失败: {e}")

                # 方法3: 清理乱码文本
                try:
                    text = self._clean_garbled_text(text)
                except:
                    pass

            return text

        except Exception as e:
            print(f"    ⚠ 文本提取失败: {e}")
            return ""

    def _extract_with_pdfplumber(self, page_num: int) -> str:
        """
        使用pdfplumber提取指定页面的文本

        Args:
            page_num: 页码（从1开始）

        Returns:
            str: 提取的文本
        """
        if not PDFPLUMBER_AVAILABLE:
            return ""

        try:
            # 获取当前处理的PDF文件路径
            # 这里需要传递文件路径，暂时使用一个简化的方法
            pdf_path = getattr(self, '_current_pdf_path', None)
            if not pdf_path:
                return ""

            with pdfplumber.open(pdf_path) as pdf:
                if page_num <= len(pdf.pages):
                    page = pdf.pages[page_num - 1]  # pdfplumber使用0索引
                    text = page.extract_text()
                    return text or ""

            return ""

        except Exception as e:
            print(f"    ⚠ pdfplumber提取第{page_num}页失败: {e}")
            return ""

    def _is_garbled_text(self, text: str) -> bool:
        """
        检测文本是否为乱码

        Args:
            text: 输入文本

        Returns:
            bool: 是否为乱码
        """
        if not text:
            return False

        # 计算非ASCII字符的比例
        non_ascii_count = sum(1 for char in text if ord(char) > 127)
        total_chars = len(text)

        if total_chars == 0:
            return False

        non_ascii_ratio = non_ascii_count / total_chars

        # 如果非ASCII字符比例过高，且包含大量特殊Unicode字符，可能是乱码
        if non_ascii_ratio > 0.8:
            # 检查是否包含大量私用区字符或控制字符
            special_chars = sum(1 for char in text if ord(char) > 0xE000 or ord(char) < 32)
            if special_chars / total_chars > 0.3:
                return True

        return False

    def _clean_garbled_text(self, text: str) -> str:
        """
        清理乱码文本

        Args:
            text: 输入文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return text

        # 移除明显的乱码字符
        cleaned_chars = []
        for char in text:
            char_code = ord(char)
            # 保留基本的中文、英文、数字和标点符号
            if (
                (0x4E00 <= char_code <= 0x9FFF) or  # 中文字符
                (0x3000 <= char_code <= 0x303F) or  # 中文标点
                (0xFF00 <= char_code <= 0xFFEF) or  # 全角字符
                (32 <= char_code <= 126) or         # ASCII可打印字符
                char in '\n\r\t '                   # 基本空白字符
            ):
                cleaned_chars.append(char)
            else:
                # 用空格替换乱码字符
                cleaned_chars.append(' ')

        # 清理多余的空格
        cleaned_text = ''.join(cleaned_chars)
        cleaned_text = ' '.join(cleaned_text.split())

        return cleaned_text