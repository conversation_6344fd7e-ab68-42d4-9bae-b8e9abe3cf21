#!/usr/bin/env python3
"""
MinerU OCR处理器
使用MinerU进行高质量的PDF解析和OCR处理
"""

import os
import time
import subprocess
import tempfile
import json
from pathlib import Path
from typing import List, Dict, Any

# 检查MinerU是否可用
def check_mineru():
    try:
        result = subprocess.run(['mineru', '--version'],
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

MINERU_AVAILABLE = check_mineru()


class SimpleOCRProcessor:
    """MinerU OCR处理器 - 使用MinerU进行高质量PDF解析"""

    def __init__(self, use_gpu: bool = True):
        """
        初始化MinerU OCR处理器

        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu
        self.device = "cpu"  # 暂时使用CPU模式避免GPU问题

        if MINERU_AVAILABLE:
            print(f"✓ MinerU OCR处理器初始化成功 (设备: {self.device})")
        else:
            print("❌ MinerU未安装或不可用")
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return MINERU_AVAILABLE
    
    def extract_text_from_pdf_images(self, file_path: str) -> List[Dict[str, Any]]:
        """
        使用MinerU进行高质量PDF解析和OCR处理

        Args:
            file_path: PDF文件路径

        Returns:
            List[Dict[str, Any]]: 处理结果
        """
        if not self.is_available():
            print("❌ MinerU不可用，无法进行OCR处理")
            return []

        try:
            print(f"📄 开始MinerU PDF解析: {Path(file_path).name}")
            start_time = time.time()

            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_dir = Path(temp_dir) / "mineru_output"
                output_dir.mkdir(exist_ok=True)

                # 构建MinerU命令（使用正确的参数格式）
                abs_file_path = os.path.abspath(file_path)
                cmd = [
                    'mineru',
                    '-p', abs_file_path,  # 使用 -p 参数指定输入文件
                    '-o', str(output_dir)  # 使用 -o 参数指定输出目录
                ]

                print(f"🔧 执行MinerU命令: {' '.join(cmd)}")

                # 执行MinerU命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=600,  # 10分钟超时
                    cwd=temp_dir
                )

                processing_time = time.time() - start_time
                print(f"⏱️  MinerU处理耗时: {processing_time:.2f}秒")

                if result.returncode != 0:
                    print(f"❌ MinerU执行失败:")
                    print(f"   stdout: {result.stdout}")
                    print(f"   stderr: {result.stderr}")
                    return []

                print(f"✅ MinerU执行成功:")
                print(f"   完整stdout: {result.stdout}")
                print(f"   完整stderr: {result.stderr}")

                # 查看输出目录内容
                print(f"📁 输出目录内容: {output_dir}")
                all_files = list(output_dir.glob("**/*"))
                print(f"   所有文件: {[str(f) for f in all_files]}")

                # 查找MinerU输出文件（优先查找auto目录下的文件）
                auto_dir = output_dir / Path(file_path).stem / "auto"
                if auto_dir.exists():
                    print(f"📁 找到MinerU auto目录: {auto_dir}")
                    markdown_files = list(auto_dir.glob("*.md"))
                    json_files = list(auto_dir.glob("*_content_list.json"))
                else:
                    # 回退到原始查找方式
                    markdown_files = list(output_dir.glob("**/*.md"))
                    json_files = list(output_dir.glob("**/*.json"))

                txt_files = list(output_dir.glob("**/*.txt"))

                print(f"   Markdown文件: {markdown_files}")
                print(f"   JSON文件: {json_files}")
                print(f"   TXT文件: {txt_files}")

                if not markdown_files and not json_files and not txt_files:
                    print(f"❌ 未找到任何输出文件")
                    return []

                # 读取输出文件（优先Markdown，然后JSON，最后TXT）
                if markdown_files:
                    output_file = markdown_files[0]
                    print(f"📄 读取Markdown文件: {output_file.name}")
                    with open(output_file, 'r', encoding='utf-8') as f:
                        markdown_content = f.read()
                elif json_files:
                    output_file = json_files[0]
                    print(f"📄 读取JSON文件: {output_file.name}")
                    with open(output_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                        # 尝试从JSON中提取文本内容
                        if isinstance(json_data, dict):
                            markdown_content = json_data.get('content', str(json_data))
                        else:
                            markdown_content = str(json_data)
                elif txt_files:
                    output_file = txt_files[0]
                    print(f"📄 读取TXT文件: {output_file.name}")
                    with open(output_file, 'r', encoding='utf-8') as f:
                        markdown_content = f.read()
                else:
                    markdown_content = "MinerU处理完成，但未找到可读取的输出文件"

                # 统计信息
                char_count = len(markdown_content)
                word_count = len(markdown_content.split())
                line_count = len(markdown_content.split('\n'))

                print(f"✅ MinerU解析完成:")
                print(f"   📝 总字符数: {char_count}")
                print(f"   📝 总单词数: {word_count}")
                print(f"   📝 总行数: {line_count}")

                # 显示内容预览
                preview = markdown_content[:500] + "..." if len(markdown_content) > 500 else markdown_content
                print(f"\n📋 内容预览:")
                print(f"   {preview}")

                # 返回统一格式的结果
                return [{
                    'page': 'all',
                    'ocr_text': markdown_content,
                    'confidence': 0.95,  # MinerU高质量解析，置信度设为0.95
                    'word_count': word_count,
                    'char_count': char_count,
                    'line_count': line_count,
                    'processing_time': processing_time,
                    'processing_method': 'mineru_pipeline',
                    'device': self.device,
                    'output_file': str(markdown_file)
                }]

        except subprocess.TimeoutExpired:
            processing_time = time.time() - start_time
            print(f"❌ MinerU处理超时 ({processing_time:.2f}秒)")
            return []
        except Exception as e:
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            print(f"❌ MinerU处理失败 ({processing_time:.2f}秒): {e}")
            return []
    
    def extract_text_from_image(self, file_path: str) -> Dict[str, Any]:
        """
        使用MinerU从图片文件提取文字

        Args:
            file_path: 图片文件路径

        Returns:
            Dict[str, Any]: OCR结果
        """
        if not self.is_available():
            print("❌ MinerU不可用，无法进行图片OCR")
            return {'text': '', 'confidence': 0.0}

        try:
            print(f"🖼️  开始MinerU图片OCR: {Path(file_path).name}")
            start_time = time.time()

            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_dir = Path(temp_dir) / "mineru_output"
                output_dir.mkdir(exist_ok=True)

                # 构建MinerU命令（图片处理，使用绝对路径）
                abs_file_path = os.path.abspath(file_path)
                cmd = [
                    'mineru',
                    '--path', abs_file_path,
                    '--output', str(output_dir),
                    '--method', 'ocr',  # 强制使用OCR方法
                    '--backend', 'pipeline',
                    '--lang', 'ch',
                    '--device', self.device,
                ]

                # 执行MinerU命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5分钟超时
                    cwd=temp_dir
                )

                processing_time = time.time() - start_time

                if result.returncode != 0:
                    print(f"❌ MinerU图片OCR失败: {result.stderr}")
                    return {'text': '', 'confidence': 0.0}

                # 查找输出文件
                markdown_files = list(output_dir.glob("**/*.md"))
                if markdown_files:
                    with open(markdown_files[0], 'r', encoding='utf-8') as f:
                        text_content = f.read()
                else:
                    text_content = f"MinerU图片OCR结果: {Path(file_path).name}"

                print(f"✅ 图片OCR完成 ({processing_time:.2f}秒)")

                return {
                    'text': text_content,
                    'confidence': 0.90,
                    'processing_method': 'mineru_image_ocr',
                    'processing_time': processing_time
                }

        except Exception as e:
            print(f"❌ 图片OCR失败: {e}")
            return {'text': '', 'confidence': 0.0}
    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]],
                                     file_name: str) -> str:
        """
        将MinerU OCR结果格式化为Markdown

        Args:
            ocr_results: MinerU OCR结果列表
            file_name: 文件名

        Returns:
            str: Markdown格式的内容
        """
        if not ocr_results:
            return f"# {Path(file_name).stem}\n\n无法处理此文件。"

        # MinerU直接返回高质量的markdown内容
        result = ocr_results[0]
        markdown_content = result.get('ocr_text', '')

        # 添加处理信息头部
        header_lines = [
            f"# {Path(file_name).stem} (MinerU解析结果)\n",
            f"**处理方法**: {result.get('processing_method', 'mineru')}",
            f"**处理时间**: {result.get('processing_time', 0):.2f}秒",
            f"**置信度**: {result.get('confidence', 0):.2f}",
            f"**字符数**: {result.get('char_count', 0)}",
            f"**单词数**: {result.get('word_count', 0)}",
            f"**设备**: {result.get('device', 'unknown')}\n",
            "---\n"
        ]

        return "\n".join(header_lines) + markdown_content
