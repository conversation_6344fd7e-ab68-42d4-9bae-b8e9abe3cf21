#!/usr/bin/env python3
"""
简化OCR处理器
避免复杂依赖，提供基本的OCR功能演示
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Any


class SimpleOCRProcessor:
    """简化OCR处理器"""
    
    def __init__(self, use_gpu: bool = True):
        """
        初始化简化OCR处理器
        
        Args:
            use_gpu: 是否使用GPU加速（演示用）
        """
        self.use_gpu = use_gpu
        print(f"✓ 简化OCR处理器初始化成功 (GPU演示: {use_gpu})")
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return True
    
    def extract_text_from_pdf_images(self, file_path: str) -> List[Dict[str, Any]]:
        """
        简化PDF文本提取（演示版本）
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            List[Dict[str, Any]]: 处理结果
        """
        try:
            print(f"📄 开始简化OCR处理: {Path(file_path).name}")
            start_time = time.time()
            
            # 模拟OCR处理时间
            print("🔧 模拟OCR识别过程...")
            time.sleep(3)  # 模拟处理耗时
            
            processing_time = time.time() - start_time
            print(f"⏱️  OCR处理耗时: {processing_time:.2f}秒")
            
            # 创建模拟的OCR结果
            mock_content = f"""# {Path(file_path).stem} (OCR识别结果)

**处理信息**: {processing_time:.2f}秒
**字符数**: 1500
**单词数**: 300
**行数**: 50

---

## 文档内容 (模拟OCR结果)

### 公司基本信息
- 公司名称: 示例公司
- 报告期间: 2024年中期
- 报告类型: 中期财务报告

### 主要财务数据
| 项目 | 金额 (万元) | 同比变化 |
|------|-------------|----------|
| 营业收入 | 1,234,567 | +15.2% |
| 净利润 | 234,567 | +12.8% |
| 总资产 | 5,678,901 | +8.5% |

### 业务概述
本报告期内，公司继续专注于主营业务发展，
通过技术创新和市场拓展，实现了稳健增长。

**注意**: 这是简化OCR演示版本。
实际部署时需要配置完整的OCR环境（如marker-pdf、PaddleOCR等）。

### 技术说明
- 当前版本: 简化演示版
- 建议配置: marker-pdf 或 PaddleOCR
- GPU加速: {self.use_gpu}
- 处理方式: 模拟OCR识别

### 下一步优化
1. 配置完整的OCR环境
2. 解决numpy版本冲突
3. 启用GPU加速
4. 提高识别准确率
"""
            
            # 统计信息
            char_count = len(mock_content)
            word_count = len(mock_content.split())
            line_count = len(mock_content.split('\n'))
            
            print(f"✅ OCR处理完成:")
            print(f"   📝 字符数: {char_count}")
            print(f"   📝 单词数: {word_count}")
            print(f"   📝 行数: {line_count}")
            print(f"   🎯 置信度: 0.85 (模拟)")
            
            # 返回统一格式的结果
            return [{
                'page': 'all',
                'ocr_text': mock_content,
                'confidence': 0.85,  # 模拟置信度
                'word_count': word_count,
                'char_count': char_count,
                'line_count': line_count,
                'processing_time': processing_time,
                'processing_method': 'simplified_ocr_demo'
            }]
            
        except Exception as e:
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            print(f"❌ OCR处理失败 ({processing_time:.2f}秒): {e}")
            return []
    
    def extract_text_from_image(self, file_path: str) -> Dict[str, Any]:
        """
        从图片文件提取文字（简化版）
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Dict[str, Any]: OCR结果
        """
        print(f"🖼️  简化图片OCR: {Path(file_path).name}")
        
        return {
            'text': f'图片文件OCR结果: {Path(file_path).name}\n这是简化版本的OCR演示。',
            'confidence': 0.80,
            'processing_method': 'simplified_image_ocr'
        }
    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]], 
                                     file_name: str) -> str:
        """
        将OCR结果格式化为Markdown
        
        Args:
            ocr_results: OCR结果列表
            file_name: 文件名
            
        Returns:
            str: Markdown格式的内容
        """
        if not ocr_results:
            return f"# {Path(file_name).stem}\n\n无法处理此文件。"
        
        # 直接返回OCR识别的markdown内容
        result = ocr_results[0]
        markdown_content = result.get('ocr_text', '')
        
        return markdown_content
