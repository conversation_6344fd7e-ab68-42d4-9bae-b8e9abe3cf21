#!/usr/bin/env python3
"""
统一日志系统
记录项目中所有关键操作的耗时和状态
"""

import os
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

class MyRAGLogger:
    """MyRAG项目统一日志器"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        """
        初始化日志器
        
        Args:
            log_dir: 日志目录
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志级别
        self.log_level = getattr(logging, log_level.upper())
        
        # 创建日志器
        self.logger = logging.getLogger('MyRAG')
        self.logger.setLevel(self.log_level)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 性能统计
        self.performance_stats = {}
        self.operation_stack = []
        
        print(f"📝 日志系统初始化成功，日志目录: {self.log_dir}")
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器 - 详细日志
        log_file = self.log_dir / f"myrag_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 简化输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter('%(levelname)s - %(message)s')
        
        file_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(self._format_message(message, **kwargs))
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(self._format_message(message, **kwargs))
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(self._format_message(message, **kwargs))
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self.logger.error(self._format_message(message, **kwargs))
    
    def _format_message(self, message: str, **kwargs) -> str:
        """格式化日志消息"""
        if kwargs:
            extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            return f"{message} | {extra_info}"
        return message
    
    @contextmanager
    def timer(self, operation: str, **context):
        """
        计时上下文管理器
        
        Args:
            operation: 操作名称
            **context: 上下文信息
        """
        start_time = time.time()
        operation_id = f"{operation}_{int(start_time * 1000)}"
        
        # 记录开始
        self.operation_stack.append({
            'id': operation_id,
            'operation': operation,
            'start_time': start_time,
            'context': context
        })
        
        self.info(f"🔄 开始 {operation}", **context)
        
        try:
            yield operation_id
        except Exception as e:
            # 记录异常
            elapsed = time.time() - start_time
            self.error(f"❌ {operation} 失败", 
                      elapsed_time=f"{elapsed:.3f}s", 
                      error=str(e), 
                      **context)
            
            # 更新统计
            self._update_stats(operation, elapsed, False, str(e))
            raise
        else:
            # 记录成功
            elapsed = time.time() - start_time
            self.info(f"✅ {operation} 完成", 
                     elapsed_time=f"{elapsed:.3f}s", 
                     **context)
            
            # 更新统计
            self._update_stats(operation, elapsed, True)
        finally:
            # 清理栈
            if self.operation_stack and self.operation_stack[-1]['id'] == operation_id:
                self.operation_stack.pop()
    
    def _update_stats(self, operation: str, elapsed: float, success: bool, error: str = None):
        """更新性能统计"""
        if operation not in self.performance_stats:
            self.performance_stats[operation] = {
                'total_count': 0,
                'success_count': 0,
                'total_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'errors': []
            }
        
        stats = self.performance_stats[operation]
        stats['total_count'] += 1
        stats['total_time'] += elapsed
        stats['min_time'] = min(stats['min_time'], elapsed)
        stats['max_time'] = max(stats['max_time'], elapsed)
        
        if success:
            stats['success_count'] += 1
        elif error:
            stats['errors'].append({
                'timestamp': datetime.now().isoformat(),
                'error': error,
                'elapsed': elapsed
            })
    
    def log_system_info(self, component: str, info: Dict[str, Any]):
        """记录系统信息"""
        self.info(f"🔧 {component} 系统信息", **info)
    
    def log_file_processing(self, file_path: str, file_size: int, file_type: str):
        """记录文件处理信息"""
        self.info("📄 开始处理文件", 
                 file_path=file_path, 
                 file_size=f"{file_size:,} bytes", 
                 file_type=file_type)
    
    def log_ocr_result(self, method: str, char_count: int, processing_time: float, confidence: float = None):
        """记录OCR结果"""
        self.info("🔍 OCR处理完成", 
                 method=method, 
                 char_count=char_count, 
                 processing_time=f"{processing_time:.3f}s",
                 confidence=confidence)
    
    def log_embedding_result(self, text_count: int, vector_count: int, processing_time: float):
        """记录嵌入结果"""
        self.info("🔤 向量嵌入完成", 
                 text_count=text_count, 
                 vector_count=vector_count, 
                 processing_time=f"{processing_time:.3f}s")
    
    def log_search_result(self, query: str, result_count: int, processing_time: float, method: str = None):
        """记录搜索结果"""
        self.info("🔍 搜索完成", 
                 query=query[:50] + "..." if len(query) > 50 else query,
                 result_count=result_count, 
                 processing_time=f"{processing_time:.3f}s",
                 method=method)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能统计摘要"""
        summary = {}
        for operation, stats in self.performance_stats.items():
            avg_time = stats['total_time'] / stats['total_count'] if stats['total_count'] > 0 else 0
            success_rate = stats['success_count'] / stats['total_count'] if stats['total_count'] > 0 else 0
            
            summary[operation] = {
                'total_count': stats['total_count'],
                'success_count': stats['success_count'],
                'success_rate': f"{success_rate:.1%}",
                'avg_time': f"{avg_time:.3f}s",
                'min_time': f"{stats['min_time']:.3f}s" if stats['min_time'] != float('inf') else "N/A",
                'max_time': f"{stats['max_time']:.3f}s",
                'total_time': f"{stats['total_time']:.3f}s",
                'error_count': len(stats['errors'])
            }
        
        return summary
    
    def save_performance_report(self, file_name: str = None):
        """保存性能报告"""
        if not file_name:
            file_name = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_file = self.log_dir / file_name
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': self.get_performance_summary(),
            'detailed_stats': self.performance_stats
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.info(f"📊 性能报告已保存: {report_file}")
        return report_file
    
    def print_performance_summary(self):
        """打印性能统计摘要"""
        summary = self.get_performance_summary()
        
        print("\n📊 性能统计摘要")
        print("=" * 60)
        
        for operation, stats in summary.items():
            print(f"\n🔧 {operation}:")
            print(f"   总次数: {stats['total_count']}")
            print(f"   成功率: {stats['success_rate']}")
            print(f"   平均耗时: {stats['avg_time']}")
            print(f"   最短耗时: {stats['min_time']}")
            print(f"   最长耗时: {stats['max_time']}")
            print(f"   总耗时: {stats['total_time']}")
            if stats['error_count'] > 0:
                print(f"   错误次数: {stats['error_count']}")


# 全局日志器实例
_global_logger = None

def get_logger() -> MyRAGLogger:
    """获取全局日志器实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = MyRAGLogger()
    return _global_logger

def init_logger(log_dir: str = "logs", log_level: str = "INFO") -> MyRAGLogger:
    """初始化全局日志器"""
    global _global_logger
    _global_logger = MyRAGLogger(log_dir, log_level)
    return _global_logger
