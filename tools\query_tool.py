#!/usr/bin/env python3
"""
查询工具
用于从知识库中进行检索和问答
"""

import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.query_engine import QueryEngine


async def interactive_query(query_engine: QueryEngine):
    """交互式查询模式"""
    print("💬 交互式查询模式 (输入 'quit' 或 'exit' 退出)")
    print("=" * 50)
    
    chat_history = []
    
    while True:
        try:
            query = input("\n🤔 请输入您的问题: ").strip()
            if query.lower() in ('quit', 'exit', 'q', '退出'):
                print("👋 再见！")
                break
            
            if not query:
                continue
            
            print("\n🔍 正在搜索相关文档...")
            print("💭 生成回答中...\n")
            
            # 使用流式输出
            response_text = ""
            response_generator = await query_engine.query(
                query=query,
                chat_history=chat_history,
                stream=True
            )
            
            async for chunk in response_generator:
                print(chunk, end='', flush=True)
                response_text += chunk
            
            print("\n" + "-" * 40)
            
            # 更新聊天历史
            chat_history.append({"role": "user", "content": query})
            chat_history.append({"role": "assistant", "content": response_text})
            
            # 保持聊天历史在合理范围内
            if len(chat_history) > 8:  # 保留最近4轮对话
                chat_history = chat_history[-8:]
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 查询处理出错: {str(e)}")


async def single_query(query_engine: QueryEngine, query: str, stream: bool = True):
    """单次查询模式"""
    print(f"🤔 查询: {query}")
    print("🔍 正在搜索相关文档...")
    print("💭 生成回答中...\n")
    
    try:
        if stream:
            # 流式输出
            response_generator = await query_engine.query(
                query=query,
                chat_history=[],
                stream=True
            )
            
            async for chunk in response_generator:
                print(chunk, end='', flush=True)
            print()
        else:
            # 非流式输出
            response = await query_engine.query(
                query=query,
                chat_history=[],
                stream=False
            )
            print(response)
            
    except Exception as e:
        print(f"❌ 查询处理出错: {str(e)}")


def similarity_search(query_engine: QueryEngine, query: str, top_k: int = 10):
    """相似度搜索模式"""
    print(f"🔍 相似度搜索: {query}")
    print(f"📊 返回前 {top_k} 个相似文档\n")
    
    try:
        results = query_engine.similarity_search(query=query, top_k=top_k)
        
        if not results:
            print("❌ 未找到相关文档")
            return
        
        for i, doc in enumerate(results, 1):
            print(f"📄 文档 {i}:")
            print(f"   内容: {doc['content'][:200]}...")
            print(f"   来源: {doc['source']}")
            print(f"   页码: {doc['page']}")
            print(f"   相似度分数: {doc.get('score', 0):.4f}")
            print()
            
    except Exception as e:
        print(f"❌ 搜索失败: {str(e)}")


def show_stats(query_engine: QueryEngine):
    """显示查询引擎统计信息"""
    print("📊 查询引擎统计信息:")
    
    try:
        stats = query_engine.get_stats()
        print(f"  向量数量: {stats['vector_count']}")
        print(f"  向量维度: {stats['vector_dim']}")
        print(f"  嵌入模型: {stats['embedding_model']}")
        print(f"  重排序: {'启用' if stats['rerank_enabled'] else '禁用'}")
        print(f"  默认检索数量: {stats['top_k']}")
        print(f"  相关性阈值: {stats['score_threshold']}")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MyRAG 查询工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 交互式查询
  python tools/query_tool.py
  
  # 单次查询
  python tools/query_tool.py --query "什么是人工智能？"
  
  # 相似度搜索
  python tools/query_tool.py --search "机器学习" --top-k 5
  
  # 显示统计信息
  python tools/query_tool.py --stats
        """
    )
    
    parser.add_argument('--query', '-q', help='单次查询文本')
    parser.add_argument('--search', '-s', help='相似度搜索文本')
    parser.add_argument('--top-k', '-k', type=int, default=10, help='搜索返回的文档数量')
    parser.add_argument('--no-stream', action='store_true', help='禁用流式输出')
    parser.add_argument('--stats', action='store_true', help='显示查询引擎统计信息')
    
    args = parser.parse_args()
    
    try:
        # 初始化查询引擎
        print("🔧 初始化查询引擎...")
        query_engine = QueryEngine()
        
        # 检查知识库
        if not query_engine.check_knowledge_base():
            print("请先使用知识库管理工具添加文档:")
            print("python tools/kb_manager.py add data/documents/")
            return
        
        # 执行操作
        if args.stats:
            show_stats(query_engine)
        elif args.search:
            similarity_search(query_engine, args.search, args.top_k)
        elif args.query:
            asyncio.run(single_query(query_engine, args.query, not args.no_stream))
        else:
            # 默认进入交互式模式
            asyncio.run(interactive_query(query_engine))
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
