#!/usr/bin/env python3
"""
并发知识库管理工具
支持多线程/多进程并发处理多个文档
"""

import sys
import argparse
import time
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.knowledge_base import KnowledgeBase
from src.concurrent_document_processor import ConcurrentDocumentProcessor
from src.logger import init_logger


def add_documents_concurrent(kb: KnowledgeBase, paths: List[str], 
                           max_workers: Optional[int] = None,
                           use_processes: bool = False,
                           recursive: bool = True):
    """并发添加文档到知识库"""
    print("🚀 并发添加文档到知识库...")
    
    # 初始化并发处理器
    processor = ConcurrentDocumentProcessor(max_workers=max_workers, use_processes=use_processes)
    
    # 收集所有文件
    all_files = []
    for path_str in paths:
        path = Path(path_str)
        
        if path.is_file():
            all_files.append(str(path))
        elif path.is_dir():
            # 收集目录中的文件
            from config.config import SUPPORTED_EXTENSIONS
            supported_exts = []
            for exts in SUPPORTED_EXTENSIONS.values():
                supported_exts.extend(exts)
            
            files = []
            if recursive:
                for ext in supported_exts:
                    files.extend(path.rglob(f"*{ext}"))
            else:
                for ext in supported_exts:
                    files.extend(path.glob(f"*{ext}"))
            
            all_files.extend([str(f) for f in files if f.is_file()])
        else:
            print(f"❌ 路径不存在: {path}")
    
    if not all_files:
        print("❌ 没有找到可处理的文档")
        return
    
    print(f"📊 找到 {len(all_files)} 个文档，开始并发处理...")
    print(f"⚙️  配置: {processor.max_workers} 个工作器 ({'多进程' if use_processes else '多线程'})")
    
    # 自定义进度回调
    def progress_callback(completed: int, total: int, current_file: str):
        progress = (completed / total) * 100
        file_name = Path(current_file).name
        print(f"📊 进度: {completed}/{total} ({progress:.1f}%) - 完成: {file_name}")
    
    # 并发处理文档
    start_time = time.time()
    results = processor.process_documents_concurrent(
        all_files, 
        save_results=True, 
        progress_callback=progress_callback
    )
    processing_time = time.time() - start_time
    
    # 统计处理结果
    success_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    print(f"\n📄 文档处理完成:")
    print(f"   ✅ 成功: {len(success_results)} 个")
    print(f"   ❌ 失败: {len(failed_results)} 个")
    print(f"   ⏱️  总耗时: {processing_time:.2f} 秒")
    print(f"   📊 平均耗时: {processing_time/len(all_files):.2f} 秒/文档")
    
    if failed_results:
        print(f"\n❌ 处理失败的文档:")
        for result in failed_results[:5]:  # 只显示前5个失败的
            print(f"   • {Path(result['file_path']).name}: {result['error']}")
        if len(failed_results) > 5:
            print(f"   ... 还有 {len(failed_results) - 5} 个失败的文档")
    
    # 添加成功处理的文档到知识库
    if success_results:
        print(f"\n📚 添加文档到知识库...")
        kb_start_time = time.time()
        
        kb_stats = {"success": 0, "failed": 0, "skipped": 0}
        
        for result in success_results:
            file_path = result['file_path']
            try:
                if kb.add_document(file_path):
                    kb_stats["success"] += 1
                else:
                    kb_stats["failed"] += 1
            except Exception as e:
                print(f"❌ 添加到知识库失败 {Path(file_path).name}: {e}")
                kb_stats["failed"] += 1
        
        kb_time = time.time() - kb_start_time
        
        print(f"\n📊 知识库添加结果:")
        print(f"   ✅ 成功: {kb_stats['success']} 个")
        print(f"   ❌ 失败: {kb_stats['failed']} 个")
        print(f"   ⏱️  耗时: {kb_time:.2f} 秒")
        
        total_time = processing_time + kb_time
        print(f"\n🎉 总体完成:")
        print(f"   📄 文档处理: {processing_time:.2f} 秒")
        print(f"   📚 知识库构建: {kb_time:.2f} 秒")
        print(f"   ⏱️  总耗时: {total_time:.2f} 秒")
        
        # 计算性能提升
        estimated_sequential_time = sum(r['processing_time'] for r in results)
        speedup = estimated_sequential_time / processing_time if processing_time > 0 else 1
        print(f"   🚀 并发加速比: {speedup:.1f}x")


def process_directory_only(paths: List[str], 
                          max_workers: Optional[int] = None,
                          use_processes: bool = False,
                          recursive: bool = True):
    """仅处理文档，不添加到知识库"""
    print("📄 并发处理文档（不添加到知识库）...")
    
    processor = ConcurrentDocumentProcessor(max_workers=max_workers, use_processes=use_processes)
    
    total_stats = {"success": 0, "failed": 0, "total_time": 0}
    
    for path_str in paths:
        path = Path(path_str)
        
        if path.is_dir():
            print(f"\n📁 处理目录: {path}")
            result = processor.process_directory_concurrent(
                str(path), 
                recursive=recursive, 
                save_results=True
            )
            
            total_stats["success"] += result["success"]
            total_stats["failed"] += result["failed"]
            total_stats["total_time"] += result["total_time"]
            
            print(f"   ✅ 成功: {result['success']} 个")
            print(f"   ❌ 失败: {result['failed']} 个")
            print(f"   📊 总片段: {result['total_chunks']} 个")
            print(f"   ⏱️  耗时: {result['total_time']:.2f} 秒")
            
        elif path.is_file():
            print(f"\n📄 处理文件: {path}")
            results = processor.process_documents_concurrent([str(path)], save_results=True)
            
            if results:
                result = results[0]
                if result['success']:
                    total_stats["success"] += 1
                    print(f"   ✅ 成功: 生成 {result['chunk_count']} 个片段")
                else:
                    total_stats["failed"] += 1
                    print(f"   ❌ 失败: {result['error']}")
                
                total_stats["total_time"] += result['processing_time']
        else:
            print(f"❌ 路径不存在: {path}")
            total_stats["failed"] += 1
    
    print(f"\n📊 处理完成:")
    print(f"   ✅ 成功: {total_stats['success']} 个")
    print(f"   ❌ 失败: {total_stats['failed']} 个")
    print(f"   ⏱️  总耗时: {total_stats['total_time']:.2f} 秒")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="并发知识库管理工具")
    parser.add_argument("command", choices=["add", "process"], 
                       help="命令: add=添加到知识库, process=仅处理文档")
    parser.add_argument("paths", nargs="+", help="文档路径（文件或目录）")
    parser.add_argument("--workers", "-w", type=int, default=None,
                       help="工作器数量（默认为CPU核心数的2倍）")
    parser.add_argument("--processes", "-p", action="store_true",
                       help="使用多进程而不是多线程")
    parser.add_argument("--no-recursive", action="store_true",
                       help="不递归处理子目录")
    parser.add_argument("--log-level", default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="日志级别")
    
    args = parser.parse_args()
    
    # 初始化日志系统
    logger = init_logger(log_level=args.log_level)
    
    print("🚀 并发知识库管理工具")
    print("=" * 50)
    print(f"命令: {args.command}")
    print(f"路径: {args.paths}")
    print(f"工作器数量: {args.workers or '自动检测'}")
    print(f"执行模式: {'多进程' if args.processes else '多线程'}")
    print(f"递归处理: {not args.no_recursive}")
    print("=" * 50)
    
    try:
        if args.command == "add":
            # 添加到知识库
            kb = KnowledgeBase()
            add_documents_concurrent(
                kb, 
                args.paths,
                max_workers=args.workers,
                use_processes=args.processes,
                recursive=not args.no_recursive
            )
        
        elif args.command == "process":
            # 仅处理文档
            process_directory_only(
                args.paths,
                max_workers=args.workers,
                use_processes=args.processes,
                recursive=not args.no_recursive
            )
        
        # 保存性能报告
        logger.save_performance_report()
        logger.print_performance_summary()
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
