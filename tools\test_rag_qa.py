#!/usr/bin/env python3
"""
小米集团财报RAG问答测试
测试基于MinerU解析结果的问答效果
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any

class XiaomiRAGTester:
    """小米财报RAG问答测试器"""
    
    def __init__(self, chunks_file: str):
        self.chunks_file = chunks_file
        self.chunks = []
        self.load_chunks()
    
    def load_chunks(self):
        """加载文档片段"""
        with open(self.chunks_file, 'r', encoding='utf-8') as f:
            self.chunks = json.load(f)
        print(f"✅ 加载了 {len(self.chunks)} 个文档片段")
    
    def search_chunks(self, query: str, top_k: int = 5) -> List[Dict]:
        """改进的中文关键词搜索"""
        # 提取查询中的关键词
        keywords = []
        if "收入" in query:
            keywords.extend(["收入", "總收入", "营业额", "營業額"])
        if "利润" in query:
            keywords.extend(["利润", "利潤", "淨利潤", "净利润"])
        if "智能手机" in query or "手机" in query:
            keywords.extend(["智能手機", "智能手机", "手機", "手机"])
        if "电动汽车" in query or "汽车" in query:
            keywords.extend(["電動汽車", "电动汽车", "汽車", "汽车", "SU7"])
        if "IoT" in query:
            keywords.extend(["IoT", "生活消費", "生活消费"])
        if "毛利率" in query:
            keywords.extend(["毛利率", "毛利"])
        if "增长" in query:
            keywords.extend(["增長", "增长", "同比"])

        # 如果没有特定关键词，使用原始查询词
        if not keywords:
            keywords = [word for word in query if len(word) > 1]

        scored_chunks = []

        for chunk in self.chunks:
            content = chunk['content']
            score = 0

            # 计算关键词匹配分数
            for keyword in keywords:
                if keyword in content:
                    score += content.count(keyword) * len(keyword)  # 长词权重更高

            # 额外加分：如果包含数字
            if re.search(r'\d+[,，]?\d*\.?\d*', content):
                score += 5

            # 额外加分：如果包含表格
            if '<table' in content:
                score += 10

            if score > 0:
                scored_chunks.append({
                    'chunk': chunk,
                    'score': score,
                    'preview': content[:300] + '...',
                    'matched_keywords': [kw for kw in keywords if kw in content]
                })

        # 按分数排序并返回top_k
        scored_chunks.sort(key=lambda x: x['score'], reverse=True)
        return scored_chunks[:top_k]
    
    def extract_answer(self, chunks: List[Dict], question: str) -> str:
        """从相关片段中提取答案"""
        if not chunks:
            return "抱歉，没有找到相关信息。"
        
        # 合并相关内容
        combined_content = "\n\n".join([chunk['chunk']['content'] for chunk in chunks])
        
        # 根据问题类型提取答案
        if "收入" in question or "营业额" in question:
            # 查找收入相关数据
            revenue_patterns = [
                r'總收入.*?(\d{1,4}[,，]?\d{3}[,，]?\d*\.?\d*)',
                r'收入.*?(\d{1,4}[,，]?\d{3})',
                r'(\d{1,4}[,，]?\d{3}).*?億元'
            ]
            for pattern in revenue_patterns:
                matches = re.findall(pattern, combined_content)
                if matches:
                    return f"根据财报数据，相关收入为：{matches[0]}（单位：百万元或亿元）"
        
        elif "利润" in question:
            # 查找利润相关数据
            profit_patterns = [
                r'淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)',
                r'利润.*?(\d{1,3}[,，]?\d*\.?\d*)',
                r'經調整淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)'
            ]
            for pattern in profit_patterns:
                matches = re.findall(pattern, combined_content)
                if matches:
                    return f"根据财报数据，相关利润为：{matches[0]}（单位：百万元或亿元）"
        
        elif "增长" in question or "同比" in question:
            # 查找增长率数据
            growth_patterns = [
                r'同比.*?(\d{1,3}\.?\d*)%',
                r'增長.*?(\d{1,3}\.?\d*)%',
                r'(\d{1,3}\.?\d*)%.*?增長'
            ]
            for pattern in growth_patterns:
                matches = re.findall(pattern, combined_content)
                if matches:
                    return f"根据财报数据，相关增长率为：{matches[0]}%"
        
        # 如果没有找到特定数据，返回最相关的片段摘要
        best_chunk = chunks[0]['chunk']['content']
        if len(best_chunk) > 500:
            best_chunk = best_chunk[:500] + "..."
        
        return f"根据财报内容：{best_chunk}"
    
    def test_qa_scenarios(self):
        """测试问答场景"""
        test_questions = [
            {
                'question': '小米2024年上半年的总收入是多少？',
                'expected_keywords': ['1,644', '164,394', '总收入', '收入'],
                'category': '财务数据'
            },
            {
                'question': '小米智能手机业务的收入情况如何？',
                'expected_keywords': ['智能手机', '930', '92,996', '手机'],
                'category': '业务分部'
            },
            {
                'question': '小米的净利润增长情况怎样？',
                'expected_keywords': ['净利润', '127', '51.3%', '增长'],
                'category': '盈利能力'
            },
            {
                'question': '小米电动汽车SU7的交付量是多少？',
                'expected_keywords': ['SU7', '27,367', '交付', '电动汽车'],
                'category': '新业务'
            },
            {
                'question': '小米IoT业务的表现如何？',
                'expected_keywords': ['IoT', '471', '生活消费', '20.6%'],
                'category': '业务分部'
            },
            {
                'question': '小米的毛利率水平如何？',
                'expected_keywords': ['毛利率', '21.4%', '毛利'],
                'category': '盈利能力'
            }
        ]
        
        results = []
        
        print("🤖 开始RAG问答测试")
        print("=" * 50)
        
        for i, qa in enumerate(test_questions, 1):
            print(f"\n❓ 问题 {i}: {qa['question']}")
            
            # 搜索相关片段
            relevant_chunks = self.search_chunks(qa['question'])
            print(f"   🔍 找到 {len(relevant_chunks)} 个相关片段")
            
            # 生成答案
            answer = self.extract_answer(relevant_chunks, qa['question'])
            print(f"   💬 答案: {answer[:200]}...")
            
            # 评估答案质量
            answer_lower = answer.lower()
            matched_keywords = [kw for kw in qa['expected_keywords'] if kw.lower() in answer_lower]
            accuracy = len(matched_keywords) / len(qa['expected_keywords'])
            
            print(f"   ✅ 匹配关键词: {matched_keywords}")
            print(f"   📊 准确率: {accuracy:.1%}")
            
            results.append({
                'question': qa['question'],
                'category': qa['category'],
                'answer': answer,
                'relevant_chunks_count': len(relevant_chunks),
                'matched_keywords': matched_keywords,
                'expected_keywords': qa['expected_keywords'],
                'accuracy': accuracy,
                'status': '✅' if accuracy >= 0.5 else '❌'
            })
        
        return results
    
    def generate_qa_report(self):
        """生成问答测试报告"""
        results = self.test_qa_scenarios()
        
        # 计算总体统计
        total_questions = len(results)
        successful_answers = sum(1 for r in results if r['accuracy'] >= 0.5)
        avg_accuracy = sum(r['accuracy'] for r in results) / total_questions
        
        # 按类别统计
        categories = {}
        for result in results:
            cat = result['category']
            if cat not in categories:
                categories[cat] = {'total': 0, 'successful': 0, 'accuracy_sum': 0}
            categories[cat]['total'] += 1
            categories[cat]['accuracy_sum'] += result['accuracy']
            if result['accuracy'] >= 0.5:
                categories[cat]['successful'] += 1
        
        print(f"\n📊 RAG问答测试总结:")
        print(f"   📝 总问题数: {total_questions}")
        print(f"   ✅ 成功回答: {successful_answers}")
        print(f"   📈 成功率: {successful_answers/total_questions:.1%}")
        print(f"   🎯 平均准确率: {avg_accuracy:.1%}")
        
        print(f"\n📋 分类别统计:")
        for cat, stats in categories.items():
            cat_accuracy = stats['accuracy_sum'] / stats['total']
            print(f"   {cat}: {stats['successful']}/{stats['total']} ({cat_accuracy:.1%})")
        
        # 保存详细报告
        report = {
            'summary': {
                'total_questions': total_questions,
                'successful_answers': successful_answers,
                'success_rate': successful_answers / total_questions,
                'average_accuracy': avg_accuracy
            },
            'category_stats': {
                cat: {
                    'total': stats['total'],
                    'successful': stats['successful'],
                    'accuracy': stats['accuracy_sum'] / stats['total']
                }
                for cat, stats in categories.items()
            },
            'detailed_results': results
        }
        
        return report

def main():
    """主函数"""
    chunks_file = "parsing_results/小米集团2024年中期报告_20250719_232130_chunks.json"
    
    if not Path(chunks_file).exists():
        print("❌ 片段文件不存在")
        return
    
    tester = XiaomiRAGTester(chunks_file)
    report = tester.generate_qa_report()
    
    # 保存报告
    report_file = "parsing_results/xiaomi_rag_qa_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 RAG问答测试报告已保存: {report_file}")

if __name__ == "__main__":
    main()
