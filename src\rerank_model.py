import os
from typing import List, Dict, Any
from http import HTTPStatus

try:
    import dashscope
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False
    print("⚠ dashscope未安装，重排序功能将使用简单排序")

from config.config import (
    USE_RERANK,
    USE_LOCAL_RERANK,
    DASHSCOPE_API_KEY,
    ALI_RERANK_MODEL,
    RERANK_TOP_K
)

class RerankModel:
    def __init__(self):
        """初始化重排序模型"""
        self.use_rerank = USE_RERANK
        self.use_local = USE_LOCAL_RERANK
        self.api_key = DASHSCOPE_API_KEY
        self.model_name = ALI_RERANK_MODEL

        # 初始化状态
        if not self.use_rerank:
            print("✓ 重排序功能已禁用，将使用简单的分数过滤")
            self.use_fallback = True
        elif self.use_local:
            print("✓ 本地重排序模型初始化（暂未实现）")
            self.use_fallback = True
        elif self.api_key and DASHSCOPE_AVAILABLE:
            # 设置API密钥
            dashscope.api_key = self.api_key
            print(f"✓ 阿里云重排序API初始化成功 (模型: {self.model_name})")
            self.use_fallback = False
        else:
            if not DASHSCOPE_AVAILABLE:
                print("⚠ dashscope未安装，重排序将使用简单分数排序")
            else:
                print("⚠ 未设置DASHSCOPE_API_KEY，重排序将使用简单分数排序")
            self.use_fallback = True

    def rerank(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        对检索结果进行重排序

        Args:
            query: 查询文本
            documents: 待重排序的文档列表
            top_k: 返回的top k个结果

        Returns:
            List[Dict[str, Any]]: 重排序后的文档列表
        """
        if not documents:
            return []

        if not self.use_rerank or self.use_fallback:
            return self._simple_rerank(documents, top_k)

        if self.use_local:
            return self._local_rerank(query, documents, top_k)
        else:
            return self._api_rerank(query, documents, top_k)

    def _simple_rerank(self, documents: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """
        简单重排序（使用原始检索分数）

        Args:
            documents: 文档列表
            top_k: 返回数量

        Returns:
            List[Dict[str, Any]]: 排序后的文档列表
        """
        # 使用原始检索分数作为重排序分数
        for doc in documents:
            doc["rerank_score"] = doc.get("score", 0.0)

        # 按分数排序并返回top k结果
        sorted_docs = sorted(documents, key=lambda x: x["rerank_score"], reverse=True)
        return sorted_docs[:top_k]

    def _local_rerank(self, query: str, documents: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """
        本地重排序（暂未实现）

        Args:
            query: 查询文本
            documents: 文档列表
            top_k: 返回数量

        Returns:
            List[Dict[str, Any]]: 重排序后的文档列表
        """
        print("⚠ 本地重排序模型暂未实现，使用简单排序")
        return self._simple_rerank(documents, top_k)

    def _api_rerank(self, query: str, documents: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """
        使用阿里云API进行重排序

        Args:
            query: 查询文本
            documents: 文档列表
            top_k: 返回数量

        Returns:
            List[Dict[str, Any]]: 重排序后的文档列表
        """
        if not DASHSCOPE_AVAILABLE:
            print("    ⚠ dashscope未安装，使用简单排序")
            return self._simple_rerank(documents, top_k)

        try:
            # 准备文档文本
            doc_texts = []
            for doc in documents:
                content = doc.get("content", "")
                # 截断过长的文档内容
                if len(content) > 2000:
                    content = content[:2000] + "..."
                doc_texts.append(content)

            print(f"    🔄 调用重排序API，处理 {len(documents)} 个文档...")

            # 使用dashscope.TextReRank.call方法
            resp = dashscope.TextReRank.call(
                model=self.model_name,
                query=query,
                documents=doc_texts,
                top_n=min(top_k, len(documents)),
                return_documents=True
            )

            if resp.status_code == HTTPStatus.OK:
                # 解析结果
                reranked_docs = []

                if hasattr(resp.output, 'results'):
                    for item in resp.output.results:
                        doc_index = item.index
                        rerank_score = item.relevance_score

                        if 0 <= doc_index < len(documents):
                            doc = documents[doc_index].copy()
                            doc["rerank_score"] = rerank_score
                            reranked_docs.append(doc)

                print(f"    ✅ 重排序完成，返回 {len(reranked_docs)} 个文档")
                return reranked_docs[:top_k]
            else:
                print(f"    ⚠ 重排序API调用失败: {resp.status_code}")
                print(f"    📄 错误信息: {resp.message}")
                return self._simple_rerank(documents, top_k)

        except Exception as e:
            print(f"    ⚠ 重排序API调用异常: {e}, 使用简单排序")
            return self._simple_rerank(documents, top_k)