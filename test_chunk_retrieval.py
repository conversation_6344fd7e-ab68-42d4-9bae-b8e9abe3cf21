#!/usr/bin/env python3
"""
测试chunk检索问题
"""

from src.hybrid_vector_store import HybridVectorStore
from src.embedding_model import EmbeddingModel
import numpy as np

def test_chunk_retrieval():
    """测试chunk检索"""
    store = HybridVectorStore()
    embedding_model = EmbeddingModel()

    # 检查chunk 1的内容
    chunk_1 = store.metadata[1]
    print('Chunk 1内容:')
    print(chunk_1.get('content', '')[:500])
    print()

    # 检查chunk 1是否在伊利集团筛选范围内
    document_info = chunk_1.get('document_info', '')
    print(f'Document info: {document_info}')
    print(f'包含伊利公司名称: {"内蒙古伊利实业集团股份有限公司" in document_info}')
    print()

    # 测试向量检索
    query = "稀释每股收益"
    query_vectors = embedding_model.encode_batch([query])
    query_vector = np.array(query_vectors[0]).reshape(1, -1)

    results = store.search(query_vector, limit=10)
    print(f'"{query}"查询结果:')
    
    chunk_1_found = False
    for i, result in enumerate(results):
        vector_id = result.get('vector_id', -1)
        score = result.get('score', 0)
        content = result.get('content', '')[:100]
        
        if vector_id == 1:
            chunk_1_found = True
            print(f'  ✅ 排名{i+1}: vector_id={vector_id}, score={score:.4f}, 内容={content}...')
        else:
            print(f'  排名{i+1}: vector_id={vector_id}, score={score:.4f}, 内容={content}...')
    
    if not chunk_1_found:
        print('  ❌ Chunk 1没有在前10个结果中')

    # 测试总资产查询
    print(f'\n"总资产"查询结果:')
    query = "总资产"
    query_vectors = embedding_model.encode_batch([query])
    query_vector = np.array(query_vectors[0]).reshape(1, -1)

    results = store.search(query_vector, limit=10)
    
    chunk_1_found = False
    for i, result in enumerate(results):
        vector_id = result.get('vector_id', -1)
        score = result.get('score', 0)
        content = result.get('content', '')[:100]
        
        if vector_id == 1:
            chunk_1_found = True
            print(f'  ✅ 排名{i+1}: vector_id={vector_id}, score={score:.4f}, 内容={content}...')
        else:
            print(f'  排名{i+1}: vector_id={vector_id}, score={score:.4f}, 内容={content}...')
    
    if not chunk_1_found:
        print('  ❌ Chunk 1没有在前10个结果中')

if __name__ == "__main__":
    test_chunk_retrieval()
