#!/usr/bin/env python3
"""
知识库管理工具
用于构建、管理和维护FAISS知识库
"""

import sys
import argparse
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.knowledge_base import KnowledgeBase


def add_documents(kb: KnowledgeBase, paths: List[str], recursive: bool = True, concurrent: bool = False):
    """添加文档到知识库"""
    if concurrent:
        print("🚀 使用并发模式添加文档到知识库...")
        print("💡 提示: 使用 concurrent_kb_manager.py 获得更好的并发控制")

        from src.concurrent_document_processor import ConcurrentDocumentProcessor
        processor = ConcurrentDocumentProcessor()

        # 收集所有文件
        all_files = []
        for path_str in paths:
            path = Path(path_str)

            if path.is_file():
                all_files.append(str(path))
            elif path.is_dir():
                from config.config import SUPPORTED_EXTENSIONS
                supported_exts = []
                for exts in SUPPORTED_EXTENSIONS.values():
                    supported_exts.extend(exts)

                files = []
                if recursive:
                    for ext in supported_exts:
                        files.extend(path.rglob(f"*{ext}"))
                else:
                    for ext in supported_exts:
                        files.extend(path.glob(f"*{ext}"))

                all_files.extend([str(f) for f in files if f.is_file()])

        if all_files:
            # 并发处理文档
            results = processor.process_documents_concurrent(all_files, save_results=True)

            # 添加到知识库
            total_stats = {"success": 0, "failed": 0, "skipped": 0}
            for result in results:
                if result['success']:
                    if kb.add_document(result['file_path']):
                        total_stats["success"] += 1
                    else:
                        total_stats["failed"] += 1
                else:
                    total_stats["failed"] += 1
        else:
            total_stats = {"success": 0, "failed": 0, "skipped": 0}
    else:
        print("📚 添加文档到知识库...")

        total_stats = {"success": 0, "failed": 0, "skipped": 0}

        for path_str in paths:
            path = Path(path_str)

            if path.is_file():
                # 单个文件
                if kb.add_document(path):
                    total_stats["success"] += 1
                else:
                    total_stats["failed"] += 1
            elif path.is_dir():
                # 目录
                stats = kb.add_documents_from_directory(path, recursive)
                total_stats["success"] += stats["success"]
                total_stats["failed"] += stats["failed"]
                total_stats["skipped"] += stats["skipped"]
            else:
                print(f"❌ 路径不存在: {path}")
                total_stats["failed"] += 1

    print(f"\n📊 总计: 成功 {total_stats['success']}, 失败 {total_stats['failed']}, 跳过 {total_stats['skipped']}")


def list_documents(kb: KnowledgeBase):
    """列出知识库中的文档"""
    print("📋 知识库文档列表:")
    
    documents = kb.list_documents()
    if not documents:
        print("  知识库为空")
        return
    
    for i, doc in enumerate(documents, 1):
        print(f"  {i}. {doc['name']}")
        print(f"     路径: {doc['path']}")
        print(f"     片段数: {doc['chunks']}")
        print(f"     大小: {doc['size']} 字节")
        print(f"     添加时间: {doc['added_at']}")
        print()


def show_stats(kb: KnowledgeBase):
    """显示知识库统计信息"""
    print("📊 知识库统计信息:")
    
    stats = kb.get_stats()
    print(f"  文档数量: {stats['documents_count']}")
    print(f"  文档片段总数: {stats['total_chunks']}")
    print(f"  向量数量: {stats['vector_count']}")
    print(f"  向量维度: {stats['vector_dim']}")
    print(f"  创建时间: {stats['created_at']}")
    print(f"  最后更新: {stats['last_updated'] or '从未更新'}")


def remove_document(kb: KnowledgeBase, file_path: str):
    """从知识库中移除文档"""
    print(f"🗑️ 移除文档: {file_path}")
    
    if kb.remove_document(Path(file_path)):
        print("✅ 文档已移除")
    else:
        print("❌ 移除失败")


def rebuild_index(kb: KnowledgeBase):
    """重建知识库索引"""
    print("🔄 重建知识库索引...")
    
    confirm = input("这将清空现有索引并重新处理所有文档，确认吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    kb.rebuild_index()
    print("✅ 索引重建完成")


def clear_knowledge_base(kb: KnowledgeBase):
    """清空知识库"""
    print("🗑️ 清空知识库...")
    
    confirm = input("这将删除所有文档和向量数据，确认吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    kb.clear()
    print("✅ 知识库已清空")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MyRAG 知识库管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 添加单个文档
  python tools/kb_manager.py add document.pdf
  
  # 添加整个目录
  python tools/kb_manager.py add data/documents/
  
  # 列出所有文档
  python tools/kb_manager.py list
  
  # 显示统计信息
  python tools/kb_manager.py stats
  
  # 移除文档
  python tools/kb_manager.py remove document.pdf
  
  # 重建索引
  python tools/kb_manager.py rebuild
  
  # 清空知识库
  python tools/kb_manager.py clear
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 添加文档命令
    add_parser = subparsers.add_parser('add', help='添加文档到知识库')
    add_parser.add_argument('paths', nargs='+', help='文档路径（文件或目录）')
    add_parser.add_argument('--no-recursive', action='store_true', help='不递归处理子目录')
    add_parser.add_argument('--concurrent', '-c', action='store_true', help='使用并发处理（推荐用于多文档）')
    
    # 列出文档命令
    subparsers.add_parser('list', help='列出知识库中的文档')
    
    # 统计信息命令
    subparsers.add_parser('stats', help='显示知识库统计信息')
    
    # 移除文档命令
    remove_parser = subparsers.add_parser('remove', help='从知识库中移除文档')
    remove_parser.add_argument('file_path', help='要移除的文档路径')
    
    # 重建索引命令
    subparsers.add_parser('rebuild', help='重建知识库索引')
    
    # 清空知识库命令
    subparsers.add_parser('clear', help='清空整个知识库')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        # 初始化知识库
        print("🔧 初始化知识库...")
        kb = KnowledgeBase()
        
        # 执行命令
        if args.command == 'add':
            add_documents(kb, args.paths, not args.no_recursive, args.concurrent)
        elif args.command == 'list':
            list_documents(kb)
        elif args.command == 'stats':
            show_stats(kb)
        elif args.command == 'remove':
            remove_document(kb, args.file_path)
        elif args.command == 'rebuild':
            rebuild_index(kb)
        elif args.command == 'clear':
            clear_knowledge_base(kb)
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
