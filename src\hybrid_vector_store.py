#!/usr/bin/env python3
"""
混合向量存储
使用IVFFlat和HNSW进行混合检索，提升检索效果和性能
"""

import os
import pickle
import faiss
import numpy as np
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from config.config import FAISS_INDEX_PATH, VECTOR_DIM


class HybridVectorStore:
    """混合向量存储 - IVFFlat + HNSW"""
    
    def __init__(self):
        """初始化混合FAISS向量存储"""
        # 确保索引目录存在
        FAISS_INDEX_PATH.mkdir(parents=True, exist_ok=True)
        
        # 文件路径
        self.ivf_index_file = FAISS_INDEX_PATH / "faiss_ivf.index"
        self.hnsw_index_file = FAISS_INDEX_PATH / "faiss_hnsw.index"
        self.metadata_file = FAISS_INDEX_PATH / "metadata.pkl"
        
        # 初始化索引
        self.ivf_index = None
        self.hnsw_index = None
        self.metadata = []
        
        # IVF参数
        self.nlist = 100  # 聚类中心数量，建议sqrt(N)
        self.nprobe = 10  # 搜索的聚类数量
        
        # HNSW参数
        self.hnsw_m = 16  # 每个节点的连接数
        self.hnsw_ef_construction = 200  # 构建时的搜索范围
        self.hnsw_ef_search = 100  # 搜索时的范围
        
        # 加载已有的索引和元数据
        self._load_indexes()
        
        print(f"✓ 混合向量存储初始化成功")
        print(f"  - IVF索引: {self.ivf_index.ntotal if self.ivf_index else 0} 个向量")
        print(f"  - HNSW索引: {self.hnsw_index.ntotal if self.hnsw_index else 0} 个向量")
    
    def _load_indexes(self):
        """加载已有的FAISS索引和元数据"""
        try:
            # 加载IVF索引
            if self.ivf_index_file.exists():
                self.ivf_index = faiss.read_index(str(self.ivf_index_file))
                print(f"✓ 加载IVF索引: {self.ivf_index.ntotal} 个向量")
            
            # 加载HNSW索引
            if self.hnsw_index_file.exists():
                self.hnsw_index = faiss.read_index(str(self.hnsw_index_file))
                print(f"✓ 加载HNSW索引: {self.hnsw_index.ntotal} 个向量")
            
            # 加载元数据
            if self.metadata_file.exists():
                with open(self.metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)
                print(f"✓ 加载元数据: {len(self.metadata)} 条记录")
                
        except Exception as e:
            print(f"⚠ 加载索引时出错: {e}")
            self._initialize_empty_indexes()
    
    def _initialize_empty_indexes(self):
        """初始化空索引"""
        # 创建IVF索引
        quantizer = faiss.IndexFlatIP(VECTOR_DIM)
        self.ivf_index = faiss.IndexIVFFlat(quantizer, VECTOR_DIM, self.nlist)
        self.ivf_index.nprobe = self.nprobe
        
        # 创建HNSW索引
        self.hnsw_index = faiss.IndexHNSWFlat(VECTOR_DIM, self.hnsw_m)
        self.hnsw_index.hnsw.efConstruction = self.hnsw_ef_construction
        self.hnsw_index.hnsw.efSearch = self.hnsw_ef_search
        
        self.metadata = []
        print("✓ 初始化空索引")
    
    def add_vectors(self, vectors: np.ndarray, documents: List[Dict[str, Any]]):
        """
        添加向量到混合索引
        
        Args:
            vectors: 向量数组 (n, dim)
            documents: 对应的文档元数据列表
        """
        if len(vectors) == 0:
            return
        
        # 确保向量是float32类型并进行L2归一化
        vectors = vectors.astype(np.float32)
        faiss.normalize_L2(vectors)
        
        # 如果是第一次添加向量，需要训练IVF索引
        if self.ivf_index is None or self.ivf_index.ntotal == 0:
            self._initialize_empty_indexes()
            
            # 训练IVF索引（需要足够的数据）
            if len(vectors) >= self.nlist:
                print(f"🔄 训练IVF索引，使用 {len(vectors)} 个向量...")
                self.ivf_index.train(vectors)
                print("✓ IVF索引训练完成")
            else:
                print(f"⚠ 向量数量 ({len(vectors)}) 少于聚类数量 ({self.nlist})，调整参数...")
                # 动态调整nlist
                self.nlist = max(1, len(vectors) // 10)
                self.nprobe = min(self.nprobe, self.nlist)
                
                # 重新创建IVF索引
                quantizer = faiss.IndexFlatIP(VECTOR_DIM)
                self.ivf_index = faiss.IndexIVFFlat(quantizer, VECTOR_DIM, self.nlist)
                self.ivf_index.nprobe = self.nprobe
                self.ivf_index.train(vectors)
                print(f"✓ 调整后的IVF索引训练完成 (nlist={self.nlist}, nprobe={self.nprobe})")
        
        # 添加向量到两个索引
        start_id = len(self.metadata)
        
        # 添加到IVF索引
        if self.ivf_index.is_trained:
            self.ivf_index.add(vectors)
            print(f"✓ 添加 {len(vectors)} 个向量到IVF索引")
        
        # 添加到HNSW索引
        self.hnsw_index.add(vectors)
        print(f"✓ 添加 {len(vectors)} 个向量到HNSW索引")
        
        # 更新元数据
        for i, doc in enumerate(documents):
            doc_with_id = doc.copy()
            doc_with_id['vector_id'] = start_id + i
            self.metadata.append(doc_with_id)
        
        # 保存索引
        self._save_indexes()
    
    def search(self, query_vector: np.ndarray, limit: int = 10) -> List[Dict[str, Any]]:
        """
        混合检索：结合IVF和HNSW的结果（带详细耗时统计）

        Args:
            query_vector: 查询向量
            limit: 返回结果数量

        Returns:
            List[Dict[str, Any]]: 检索结果
        """
        search_start = time.time()
        print(f"🔍 开始混合向量检索 (目标: {limit} 个结果)")

        if len(self.metadata) == 0:
            print("❌ 向量库为空")
            return []

        # 确保查询向量格式正确
        prep_start = time.time()
        query_vector = query_vector.astype(np.float32).reshape(1, -1)
        faiss.normalize_L2(query_vector)
        prep_time = time.time() - prep_start
        print(f"⏱️  向量预处理耗时: {prep_time:.4f}秒")

        # 从两个索引分别检索
        search_limit = limit * 2
        print(f"📊 每个索引检索 {search_limit} 个候选结果")

        ivf_start = time.time()
        ivf_results = self._search_ivf(query_vector, search_limit)
        ivf_time = time.time() - ivf_start
        print(f"⏱️  IVF检索耗时: {ivf_time:.4f}秒 (找到 {len(ivf_results)} 个结果)")

        hnsw_start = time.time()
        hnsw_results = self._search_hnsw(query_vector, search_limit)
        hnsw_time = time.time() - hnsw_start
        print(f"⏱️  HNSW检索耗时: {hnsw_time:.4f}秒 (找到 {len(hnsw_results)} 个结果)")

        # 混合结果
        merge_start = time.time()
        hybrid_results = self._merge_results(ivf_results, hnsw_results, limit)
        merge_time = time.time() - merge_start
        print(f"⏱️  结果合并耗时: {merge_time:.4f}秒 (最终 {len(hybrid_results)} 个结果)")

        total_time = time.time() - search_start
        print(f"⏱️  混合检索总耗时: {total_time:.4f}秒")
        print(f"   - 向量预处理: {prep_time:.4f}秒 ({prep_time/total_time*100:.1f}%)")
        print(f"   - IVF检索: {ivf_time:.4f}秒 ({ivf_time/total_time*100:.1f}%)")
        print(f"   - HNSW检索: {hnsw_time:.4f}秒 ({hnsw_time/total_time*100:.1f}%)")
        print(f"   - 结果合并: {merge_time:.4f}秒 ({merge_time/total_time*100:.1f}%)")

        return hybrid_results
    
    def _search_ivf(self, query_vector: np.ndarray, k: int) -> List[Tuple[int, float]]:
        """IVF索引检索"""
        try:
            if self.ivf_index and self.ivf_index.is_trained and self.ivf_index.ntotal > 0:
                k = min(k, self.ivf_index.ntotal)
                scores, indices = self.ivf_index.search(query_vector, k)
                
                results = []
                for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                    if idx >= 0 and idx < len(self.metadata):
                        results.append((idx, float(score)))
                
                return results
        except Exception as e:
            print(f"⚠ IVF检索失败: {e}")
        
        return []
    
    def _search_hnsw(self, query_vector: np.ndarray, k: int) -> List[Tuple[int, float]]:
        """HNSW索引检索"""
        try:
            if self.hnsw_index and self.hnsw_index.ntotal > 0:
                k = min(k, self.hnsw_index.ntotal)
                scores, indices = self.hnsw_index.search(query_vector, k)
                
                results = []
                for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                    if idx >= 0 and idx < len(self.metadata):
                        results.append((idx, float(score)))
                
                return results
        except Exception as e:
            print(f"⚠ HNSW检索失败: {e}")
        
        return []
    
    def _merge_results(self, ivf_results: List[Tuple[int, float]], 
                      hnsw_results: List[Tuple[int, float]], 
                      limit: int) -> List[Dict[str, Any]]:
        """
        合并IVF和HNSW的检索结果
        
        Args:
            ivf_results: IVF检索结果 [(idx, score), ...]
            hnsw_results: HNSW检索结果 [(idx, score), ...]
            limit: 最终返回数量
            
        Returns:
            List[Dict[str, Any]]: 合并后的结果
        """
        # 使用字典去重并合并分数
        merged_scores = {}
        
        # IVF结果权重
        ivf_weight = 0.6
        for idx, score in ivf_results:
            merged_scores[idx] = merged_scores.get(idx, 0) + score * ivf_weight
        
        # HNSW结果权重
        hnsw_weight = 0.4
        for idx, score in hnsw_results:
            merged_scores[idx] = merged_scores.get(idx, 0) + score * hnsw_weight
        
        # 按分数排序
        sorted_results = sorted(merged_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建最终结果
        final_results = []
        for idx, score in sorted_results[:limit]:
            if idx < len(self.metadata):
                doc = self.metadata[idx].copy()
                doc['score'] = score
                doc['search_method'] = 'hybrid_ivf_hnsw'

                # 确保包含必需字段
                if 'page' not in doc:
                    doc['page'] = 0  # 默认页码
                if 'source' not in doc:
                    doc['source'] = 'unknown'
                if 'content' not in doc:
                    doc['content'] = ''

                final_results.append(doc)
        
        return final_results
    
    def _save_indexes(self):
        """保存索引和元数据"""
        try:
            # 保存IVF索引
            if self.ivf_index and self.ivf_index.is_trained:
                faiss.write_index(self.ivf_index, str(self.ivf_index_file))
            
            # 保存HNSW索引
            if self.hnsw_index:
                faiss.write_index(self.hnsw_index, str(self.hnsw_index_file))
            
            # 保存元数据
            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)
                
        except Exception as e:
            print(f"❌ 保存索引失败: {e}")
    
    def clear(self):
        """清空所有索引和元数据"""
        try:
            # 删除索引文件
            if self.ivf_index_file.exists():
                self.ivf_index_file.unlink()
            if self.hnsw_index_file.exists():
                self.hnsw_index_file.unlink()
            if self.metadata_file.exists():
                self.metadata_file.unlink()
            
            # 重新初始化
            self._initialize_empty_indexes()
            
            print("✅ 清空混合向量存储完成")
            
        except Exception as e:
            print(f"❌ 清空向量存储失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        return {
            'total_vectors': len(self.metadata),
            'ivf_vectors': self.ivf_index.ntotal if self.ivf_index else 0,
            'hnsw_vectors': self.hnsw_index.ntotal if self.hnsw_index else 0,
            'ivf_trained': self.ivf_index.is_trained if self.ivf_index else False,
            'ivf_nlist': self.nlist,
            'ivf_nprobe': self.nprobe,
            'hnsw_m': self.hnsw_m,
            'hnsw_ef_search': self.hnsw_ef_search
        }
