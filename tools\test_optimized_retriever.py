#!/usr/bin/env python3
"""
测试优化检索器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.hybrid_vector_store import HybridVectorStore
from src.embedding_model import EmbeddingModel
from src.optimized_retriever import OptimizedRetriever


def test_optimized_retriever():
    """测试优化检索器"""
    print("🔧 初始化优化检索器...")
    
    # 初始化组件
    vector_store = HybridVectorStore()
    embedding_model = EmbeddingModel()
    retriever = OptimizedRetriever(vector_store, embedding_model)
    
    print(f"✅ 知识库加载完成，共 {len(vector_store.metadata)} 个文档")
    
    # 获取公司统计信息
    company_stats = retriever.get_company_stats()
    print(f"\n📊 公司文档统计:")
    for company, stats in company_stats.items():
        print(f"   {company} ({stats['full_name']}): {stats['document_count']} 个文档")
        for i, sample in enumerate(stats['sample_documents'], 1):
            print(f"      样本{i}: chunk_id={sample['chunk_id']}, 内容={sample['content_preview']}")
    
    # 测试查询
    test_queries = [
        ("伊利集团2025年第一季度营业收入是多少？", "伊利"),
        ("美的集团2025年第一季度营业收入", "美的"),
        ("营业收入 127,838,538", "美的"),  # 测试美的集团的具体数值
        ("净利润", "伊利"),
        ("营业收入", None),  # 不指定公司
    ]
    
    for query, company in test_queries:
        print(f"\n{'='*80}")
        print(f"🔍 测试查询: {query}")
        if company:
            print(f"🏢 目标公司: {company}")
        
        # 使用优化检索
        results = retriever.optimized_search(
            query=query,
            company_name=company,
            top_k=5,
            use_rerank=False  # 先不使用重排序测试
        )
        
        print(f"📊 检索结果: {len(results)} 个文档")
        
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            source = result.get('source', '')
            vector_score = result.get('vector_score', 0)
            keyword_score = result.get('keyword_score', 0)
            combined_score = result.get('combined_score', 0)
            doc_info = result.get('document_info', '')
            method = result.get('retrieval_method', '')
            
            print(f"\n📄 结果 {i}:")
            print(f"   📁 来源: {source}")
            print(f"   🔍 检索方法: {method}")
            print(f"   📊 向量分数: {vector_score:.4f}")
            print(f"   🔑 关键词分数: {keyword_score:.2f}")
            print(f"   🎯 组合分数: {combined_score:.4f}")
            print(f"   🏷️  文档信息: {doc_info}")
            print(f"   📝 内容: {content[:200]}...")
        
        print("-" * 80)


def test_specific_company_data():
    """测试特定公司数据检索"""
    print(f"\n{'='*80}")
    print("🔍 测试特定公司数据检索")
    
    # 初始化
    vector_store = HybridVectorStore()
    embedding_model = EmbeddingModel()
    retriever = OptimizedRetriever(vector_store, embedding_model)
    
    # 测试美的集团数据（您选择的数据）
    print(f"\n📊 测试美的集团营业收入数据:")
    
    # 查找包含美的集团营业收入数据的文档
    target_number = "127,838,538"  # 美的集团营业收入（千元）
    found_docs = []
    
    for i, doc in enumerate(vector_store.metadata):
        content = doc.get('content', '')
        document_info = doc.get('document_info', '')
        
        if target_number in content and "美的集团" in document_info:
            found_docs.append((i, doc))
    
    print(f"找到 {len(found_docs)} 个包含美的集团营业收入数据的文档:")
    for i, (idx, doc) in enumerate(found_docs):
        print(f"   文档{i+1}: 索引={idx}, chunk_id={doc.get('chunk_id', '')}")
        print(f"           document_info={doc.get('document_info', '')}")
        print(f"           内容预览={doc.get('content', '')[:150]}...")
        print()
    
    # 测试检索这些数据
    test_queries = [
        "美的集团营业收入",
        "美的集团2025年第一季度营业收入",
        "营业收入 127,838,538",
        "127,838,538 千元"
    ]
    
    for query in test_queries:
        print(f"\n🔍 查询: {query}")
        results = retriever.optimized_search(query, "美的", top_k=3, use_rerank=False)
        
        print(f"📊 结果: {len(results)} 个")
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            combined_score = result.get('combined_score', 0)
            
            # 检查是否包含目标数据
            contains_target = target_number in content
            print(f"   结果{i}: 分数={combined_score:.4f}, 包含目标数据={'✅' if contains_target else '❌'}")
            if contains_target:
                print(f"          内容={content[:200]}...")


if __name__ == "__main__":
    test_optimized_retriever()
    test_specific_company_data()
