#!/usr/bin/env python3
"""
小米集团财报深度分析测试
更精确地测试MinerU解析效果
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any

class XiaomiDeepAnalyzer:
    """小米财报深度分析器"""
    
    def __init__(self, chunks_file: str, content_file: str):
        self.chunks_file = chunks_file
        self.content_file = content_file
        self.chunks = []
        self.content = ""
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        with open(self.chunks_file, 'r', encoding='utf-8') as f:
            self.chunks = json.load(f)
        with open(self.content_file, 'r', encoding='utf-8') as f:
            self.content = f.read()
    
    def search_financial_data(self):
        """搜索财务数据"""
        print("🔍 深度搜索财务数据")
        print("=" * 50)
        
        # 搜索关键财务数据
        patterns = {
            '总收入': [
                r'總收入.*?(\d{1,3}[,，]\d{3}[,，]?\d*\.?\d*)',
                r'收入.*?(\d{1,3}[,，]\d{3})',
                r'1[,，]644',
                r'164[,，]394'
            ],
            '净利润': [
                r'淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)',
                r'經調整淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)',
                r'12[,，]666',
                r'127億'
            ],
            '智能手机': [
                r'智能手機.*?收入.*?(\d{1,3}[,，]?\d*)',
                r'手機.*?收入.*?(\d{1,3}[,，]?\d*)',
                r'92[,，]996',
                r'930億'
            ],
            '出货量': [
                r'出貨量.*?(\d{1,3}\.?\d*)',
                r'82\.8.*?百萬台',
                r'82\.8.*?million'
            ]
        }
        
        results = {}
        for category, pattern_list in patterns.items():
            matches = []
            for pattern in pattern_list:
                found = re.findall(pattern, self.content, re.IGNORECASE)
                if found:
                    matches.extend(found)
            
            # 也搜索片段
            chunk_matches = []
            for chunk in self.chunks:
                content = chunk['content']
                for pattern in pattern_list:
                    found = re.findall(pattern, content, re.IGNORECASE)
                    if found:
                        chunk_matches.append({
                            'chunk_id': chunk['metadata']['chunk_id'],
                            'matches': found,
                            'context': content[:300] + '...'
                        })
            
            results[category] = {
                'content_matches': matches,
                'chunk_matches': chunk_matches,
                'total_matches': len(matches) + len(chunk_matches)
            }
            
            print(f"\n📊 {category}:")
            print(f"   内容匹配: {len(matches)} 个")
            print(f"   片段匹配: {len(chunk_matches)} 个")
            if matches:
                print(f"   找到数据: {matches[:5]}")  # 显示前5个
        
        return results
    
    def analyze_business_segments(self):
        """分析业务分部"""
        print("\n🏢 业务分部深度分析")
        print("=" * 50)
        
        segments = {
            '手机×AIoT': ['手機.*?AloT', '手机.*?AIoT', '1[,，]580億', '157[,，]999'],
            '智能手机': ['智能手機', '手機業務', '930億', '92[,，]996'],
            'IoT生活消费': ['IoT.*?生活消費', 'IoT.*?生活消费', '471億', '47[,，]133'],
            '互联网服务': ['互聯網服務', '互联网服务', '163億', '16[,，]314'],
            '智能电动汽车': ['智能電動汽車', '智能电动汽车', 'SU7', '64億', '6[,，]394']
        }
        
        results = {}
        for segment, patterns in segments.items():
            matches = []
            relevant_chunks = []
            
            for pattern in patterns:
                # 搜索内容
                content_matches = re.findall(pattern, self.content, re.IGNORECASE)
                matches.extend(content_matches)
                
                # 搜索相关片段
                for chunk in self.chunks:
                    if re.search(pattern, chunk['content'], re.IGNORECASE):
                        relevant_chunks.append({
                            'chunk_id': chunk['metadata']['chunk_id'],
                            'preview': chunk['content'][:200] + '...'
                        })
            
            results[segment] = {
                'matches': matches,
                'relevant_chunks': relevant_chunks[:3],  # 只保留前3个
                'coverage': len(relevant_chunks)
            }
            
            print(f"\n📈 {segment}:")
            print(f"   匹配数据: {len(matches)} 个")
            print(f"   相关片段: {len(relevant_chunks)} 个")
            if matches:
                print(f"   示例: {matches[:3]}")
        
        return results
    
    def check_table_quality(self):
        """检查表格质量"""
        print("\n📋 表格质量深度检查")
        print("=" * 50)
        
        # 提取所有表格
        tables = re.findall(r'<table.*?</table>', self.content, re.DOTALL)
        
        table_analysis = {
            'total_tables': len(tables),
            'financial_tables': [],
            'revenue_tables': [],
            'segment_tables': [],
            'quality_issues': []
        }
        
        for i, table in enumerate(tables):
            # 分析表格内容
            has_financial_data = bool(re.search(r'[收入|利潤|毛利|億元|百萬]', table))
            has_revenue_data = bool(re.search(r'[2024|2023|同比|增長]', table))
            has_segment_data = bool(re.search(r'[分部|業務|手機|IoT|汽車]', table))
            
            if has_financial_data:
                table_analysis['financial_tables'].append(i)
            if has_revenue_data:
                table_analysis['revenue_tables'].append(i)
            if has_segment_data:
                table_analysis['segment_tables'].append(i)
            
            # 检查表格质量问题
            if len(table) < 100:
                table_analysis['quality_issues'].append(f"表格{i}: 内容过短")
            if table.count('<td>') < 4:
                table_analysis['quality_issues'].append(f"表格{i}: 单元格过少")
        
        print(f"📊 表格统计:")
        print(f"   总表格数: {table_analysis['total_tables']}")
        print(f"   财务表格: {len(table_analysis['financial_tables'])}")
        print(f"   收入表格: {len(table_analysis['revenue_tables'])}")
        print(f"   分部表格: {len(table_analysis['segment_tables'])}")
        print(f"   质量问题: {len(table_analysis['quality_issues'])}")
        
        # 显示一个示例表格
        if tables:
            print(f"\n📋 示例表格 (前200字符):")
            print(tables[0][:200] + "...")
        
        return table_analysis
    
    def find_key_metrics(self):
        """查找关键指标"""
        print("\n🎯 关键指标精确查找")
        print("=" * 50)
        
        # 定义关键指标及其可能的表达方式
        key_metrics = {
            '2024年上半年总收入': {
                'patterns': [
                    r'2024年.*?上半年.*?收入.*?(\d{1,4}[,，]?\d{3})',
                    r'收入.*?2024年.*?(\d{1,4}[,，]?\d{3})',
                    r'164[,，]394\.7',
                    r'1[,，]644.*?億'
                ],
                'expected': '1,644亿元'
            },
            '经调整净利润': {
                'patterns': [
                    r'經調整淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)',
                    r'调整.*?净利润.*?(\d{1,3}[,，]?\d*\.?\d*)',
                    r'12[,，]666\.4',
                    r'127.*?億'
                ],
                'expected': '127亿元'
            },
            '智能手机出货量': {
                'patterns': [
                    r'智能手機.*?出貨量.*?(\d{1,3}\.?\d*)',
                    r'出货量.*?(\d{1,3}\.?\d*).*?百萬台',
                    r'82\.8.*?百萬台',
                    r'82\.8.*?million'
                ],
                'expected': '82.8百万台'
            },
            'SU7交付量': {
                'patterns': [
                    r'SU7.*?交付.*?(\d{1,3}[,，]?\d{3})',
                    r'交付.*?SU7.*?(\d{1,3}[,，]?\d{3})',
                    r'27[,，]367',
                    r'27367'
                ],
                'expected': '27,367辆'
            }
        }
        
        results = {}
        for metric, info in key_metrics.items():
            found_values = []
            found_contexts = []
            
            # 在内容中搜索
            for pattern in info['patterns']:
                matches = re.findall(pattern, self.content, re.IGNORECASE)
                if matches:
                    found_values.extend(matches)
                
                # 查找上下文
                contexts = re.findall(f'.{{0,100}}{pattern}.{{0,100}}', self.content, re.IGNORECASE)
                found_contexts.extend(contexts)
            
            # 在片段中搜索
            chunk_matches = []
            for chunk in self.chunks:
                for pattern in info['patterns']:
                    if re.search(pattern, chunk['content'], re.IGNORECASE):
                        chunk_matches.append({
                            'chunk_id': chunk['metadata']['chunk_id'],
                            'content': chunk['content'][:300] + '...'
                        })
                        break
            
            results[metric] = {
                'expected': info['expected'],
                'found_values': list(set(found_values)),  # 去重
                'contexts': found_contexts[:2],  # 只保留前2个上下文
                'chunk_matches': chunk_matches[:2],  # 只保留前2个片段
                'success': len(found_values) > 0 or len(chunk_matches) > 0
            }
            
            status = '✅' if results[metric]['success'] else '❌'
            print(f"\n{status} {metric}:")
            print(f"   期望值: {info['expected']}")
            print(f"   找到值: {found_values[:3] if found_values else '无'}")
            print(f"   匹配片段: {len(chunk_matches)} 个")
        
        return results
    
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("🧪 小米集团财报深度分析测试")
        print("=" * 60)
        
        # 执行所有分析
        financial_data = self.search_financial_data()
        business_segments = self.analyze_business_segments()
        table_quality = self.check_table_quality()
        key_metrics = self.find_key_metrics()
        
        # 计算评分
        financial_score = sum(1 for v in financial_data.values() if v['total_matches'] > 0) / len(financial_data)
        segments_score = sum(1 for v in business_segments.values() if v['coverage'] > 0) / len(business_segments)
        table_score = min(1.0, table_quality['total_tables'] / 50)  # 期望至少50个表格
        metrics_score = sum(1 for v in key_metrics.values() if v['success']) / len(key_metrics)
        
        overall_score = (financial_score + segments_score + table_score + metrics_score) / 4
        
        print(f"\n📊 综合评估结果:")
        print(f"   💰 财务数据识别: {financial_score:.1%}")
        print(f"   🏢 业务分部分析: {segments_score:.1%}")
        print(f"   📋 表格提取质量: {table_score:.1%}")
        print(f"   🎯 关键指标准确性: {metrics_score:.1%}")
        print(f"   🏆 综合评分: {overall_score:.1%}")
        
        # 评级
        if overall_score >= 0.9:
            grade = "A+ (优秀)"
        elif overall_score >= 0.8:
            grade = "A (良好)"
        elif overall_score >= 0.7:
            grade = "B (一般)"
        else:
            grade = "C (需改进)"
        
        print(f"   📈 解析质量等级: {grade}")
        
        # 保存详细报告
        report = {
            'overall_score': overall_score,
            'financial_data': financial_data,
            'business_segments': business_segments,
            'table_quality': table_quality,
            'key_metrics': key_metrics,
            'scores': {
                'financial_score': financial_score,
                'segments_score': segments_score,
                'table_score': table_score,
                'metrics_score': metrics_score
            },
            'grade': grade
        }
        
        return report

def main():
    """主函数"""
    chunks_file = "parsing_results/小米集团2024年中期报告_20250719_232130_chunks.json"
    content_file = "parsing_results/小米集团2024年中期报告_20250719_232130_content.md"
    
    if not Path(chunks_file).exists() or not Path(content_file).exists():
        print("❌ 测试文件不存在")
        return
    
    analyzer = XiaomiDeepAnalyzer(chunks_file, content_file)
    report = analyzer.generate_comprehensive_report()
    
    # 保存报告
    report_file = "parsing_results/xiaomi_deep_analysis_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细分析报告已保存: {report_file}")

if __name__ == "__main__":
    main()
