import os
import pickle
import faiss
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
from config.config import FAISS_INDEX_PATH, VECTOR_DIM

class VectorStore:
    def __init__(self):
        """初始化FAISS向量存储"""
        # 确保索引目录存在
        FAISS_INDEX_PATH.mkdir(parents=True, exist_ok=True)

        self.index_file = FAISS_INDEX_PATH / "faiss.index"
        self.metadata_file = FAISS_INDEX_PATH / "metadata.pkl"

        # 初始化FAISS索引
        self.index = faiss.IndexFlatIP(VECTOR_DIM)  # 使用内积相似度
        self.metadata = []  # 存储文档元数据

        # 加载已有的索引和元数据
        self._load_index()

    def _load_index(self):
        """加载已有的FAISS索引和元数据"""
        try:
            if self.index_file.exists():
                self.index = faiss.read_index(str(self.index_file))
                print(f"✓ 加载FAISS索引: {self.index.ntotal} 个向量")

            if self.metadata_file.exists():
                with open(self.metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)
                print(f"✓ 加载元数据: {len(self.metadata)} 条记录")
        except Exception as e:
            print(f"⚠ 加载索引失败，将创建新索引: {e}")
            self.index = faiss.IndexFlatIP(VECTOR_DIM)
            self.metadata = []

    def _save_index(self):
        """保存FAISS索引和元数据"""
        try:
            faiss.write_index(self.index, str(self.index_file))
            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)
        except Exception as e:
            print(f"⚠ 保存索引失败: {e}")

    def add_documents(self, documents: List[Dict[str, Any]], embeddings: np.ndarray):
        """添加文档和对应的向量到索引中"""
        if len(documents) != len(embeddings):
            raise ValueError("文档数量和向量数量不匹配")

        # 标准化向量（对于内积相似度）
        embeddings = embeddings.astype('float32')
        faiss.normalize_L2(embeddings)

        # 添加向量到FAISS索引
        self.index.add(embeddings)

        # 添加元数据
        for doc in documents:
            metadata = {
                "content": str(doc["content"]),
                "source": str(doc["source"]),
                "page": int(doc.get("page", 0)),
                "chunk_id": int(doc.get("chunk_id", 0)),
                "type": doc.get("type", "text")
            }
            self.metadata.append(metadata)

        # 保存索引
        self._save_index()
        print(f"✓ 添加了 {len(documents)} 个文档片段到向量索引")

    def search(self, query_embedding: np.ndarray, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索最相似的文档"""
        if self.index.ntotal == 0:
            return []

        # 确保查询向量是正确的形状和类型
        if query_embedding.ndim == 1:
            query_embedding = query_embedding.reshape(1, -1)

        query_embedding = query_embedding.astype('float32')
        faiss.normalize_L2(query_embedding)

        # 搜索
        scores, indices = self.index.search(query_embedding, min(limit, self.index.ntotal))

        documents = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx == -1:  # FAISS返回-1表示无效结果
                continue

            if idx < len(self.metadata):
                doc = self.metadata[idx].copy()
                doc["score"] = float(score)

                # 增强返回信息：确保包含完整的元数据和Markdown内容
                self._enhance_search_result(doc)

                documents.append(doc)

        return documents

    def _enhance_search_result(self, doc: Dict[str, Any]) -> None:
        """
        增强搜索结果，确保包含完整信息

        Args:
            doc: 文档字典，会被就地修改
        """
        try:
            # 确保包含基础信息
            if 'content' not in doc:
                doc['content'] = ''

            # 对于表格行片段，确保包含完整的上下文信息
            if doc.get('type') in ['table_row', 'table_row_enhanced']:
                # 确保包含Markdown表格内容
                if 'markdown_table' not in doc:
                    doc['markdown_table'] = doc.get('content', '')

                # 确保包含完整的表格信息
                table_info = doc.get('table_info', {})
                if table_info:
                    # 添加表格上下文摘要
                    doc['table_context'] = {
                        'table_id': table_info.get('table_id', 'unknown'),
                        'rows': table_info.get('rows', 0),
                        'cols': table_info.get('cols', 0),
                        'headers': table_info.get('headers', []),
                        'row_data': table_info.get('row_data', []),
                        'semantic_tags': table_info.get('semantic_tags', []),
                        'search_keywords': table_info.get('search_keywords', []),
                        'enhancement_type': table_info.get('enhancement_type', 'basic')
                    }

                # 添加检索相关性信息
                doc['retrieval_info'] = {
                    'financial_relevance': doc.get('financial_relevance', 0.0),
                    'semantic_enhanced': doc.get('semantic_enhanced', False),
                    'parent_table_id': doc.get('parent_table_id', 'unknown'),
                    'chunk_type': doc.get('type', 'unknown')
                }

            # 对于完整表格片段，确保包含表格信息
            elif doc.get('type') == 'table':
                table_info = doc.get('table_info', {})
                if table_info:
                    doc['table_context'] = {
                        'table_id': table_info.get('table_id', 'unknown'),
                        'rows': table_info.get('rows', 0),
                        'cols': table_info.get('cols', 0),
                        'bbox': table_info.get('bbox', [])
                    }

            # 确保包含源信息
            doc['source_info'] = {
                'source': doc.get('source', ''),
                'page': doc.get('page', 0),
                'chunk_id': doc.get('chunk_id', ''),
                'traditional_converted': doc.get('traditional_converted', False)
            }

        except Exception as e:
            print(f"⚠ 搜索结果增强失败: {e}")
            # 确保至少有基本信息
            if 'retrieval_info' not in doc:
                doc['retrieval_info'] = {'chunk_type': doc.get('type', 'unknown')}

    def clear(self):
        """清空向量索引和元数据"""
        self.index = faiss.IndexFlatIP(VECTOR_DIM)
        self.metadata = []

        # 删除索引文件
        if self.index_file.exists():
            self.index_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()

        print("✓ 向量索引已清空")

    def get_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        return {
            "total_vectors": self.index.ntotal,
            "vector_dim": self.index.d,
            "metadata_count": len(self.metadata)
        }