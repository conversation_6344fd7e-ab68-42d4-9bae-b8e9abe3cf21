#!/usr/bin/env python3
"""
FAISS库内容查看器
用于查看和查询FAISS向量库中存储的文档内容
"""

import sys
import pickle
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional
import re

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_faiss_metadata() -> List[Dict[str, Any]]:
    """
    加载FAISS库的元数据
    
    Returns:
        List[Dict[str, Any]]: 文档元数据列表
    """
    try:
        metadata_file = Path("faiss_index/metadata.pkl")
        if not metadata_file.exists():
            print("❌ FAISS元数据文件不存在")
            return []
        
        with open(metadata_file, 'rb') as f:
            documents = pickle.load(f)
        
        print(f"✅ 成功加载 {len(documents)} 个文档片段")
        return documents
        
    except Exception as e:
        print(f"❌ 加载元数据失败: {e}")
        return []

def show_statistics(documents: List[Dict[str, Any]]):
    """
    显示FAISS库统计信息
    
    Args:
        documents: 文档列表
    """
    if not documents:
        print("📊 库为空")
        return
    
    print(f"\n📊 FAISS库统计信息")
    print("=" * 50)
    
    # 基本统计
    print(f"总文档片段数: {len(documents)}")
    
    # 按类型统计
    type_stats = {}
    for doc in documents:
        doc_type = doc.get('type', 'unknown')
        type_stats[doc_type] = type_stats.get(doc_type, 0) + 1
    
    print(f"\n📋 按类型分布:")
    for doc_type, count in sorted(type_stats.items()):
        print(f"  {doc_type}: {count} 个")
    
    # 按来源统计
    source_stats = {}
    for doc in documents:
        source = doc.get('source', 'unknown')
        if source:
            source_name = Path(source).name
            source_stats[source_name] = source_stats.get(source_name, 0) + 1
    
    print(f"\n📄 按来源分布:")
    for source, count in sorted(source_stats.items()):
        print(f"  {source}: {count} 个片段")
    
    # 语义增强统计
    enhanced_count = sum(1 for doc in documents if doc.get('semantic_enhanced', False))
    print(f"\n🤖 语义增强片段: {enhanced_count} 个 ({enhanced_count/len(documents)*100:.1f}%)")
    
    # 财务相关性统计
    financial_docs = [doc for doc in documents if doc.get('financial_relevance', 0) > 0]
    if financial_docs:
        avg_relevance = sum(doc.get('financial_relevance', 0) for doc in financial_docs) / len(financial_docs)
        print(f"💰 财务相关片段: {len(financial_docs)} 个，平均相关性: {avg_relevance:.2f}")

def search_documents(documents: List[Dict[str, Any]], query: str, 
                    search_type: str = "content") -> List[Dict[str, Any]]:
    """
    在文档中搜索内容
    
    Args:
        documents: 文档列表
        query: 搜索查询
        search_type: 搜索类型 (content, source, type)
        
    Returns:
        List[Dict[str, Any]]: 匹配的文档列表
    """
    results = []
    query_lower = query.lower()
    
    for doc in documents:
        match = False
        
        if search_type == "content":
            content = doc.get('content', '').lower()
            match = query_lower in content
        elif search_type == "source":
            source = doc.get('source', '').lower()
            match = query_lower in source
        elif search_type == "type":
            doc_type = doc.get('type', '').lower()
            match = query_lower in doc_type
        elif search_type == "all":
            content = doc.get('content', '').lower()
            source = doc.get('source', '').lower()
            doc_type = doc.get('type', '').lower()
            match = (query_lower in content or 
                    query_lower in source or 
                    query_lower in doc_type)
        
        if match:
            results.append(doc)
    
    return results

def display_document(doc: Dict[str, Any], index: int, show_full: bool = False):
    """
    显示单个文档的详细信息
    
    Args:
        doc: 文档字典
        index: 文档索引
        show_full: 是否显示完整内容
    """
    print(f"\n📄 文档 #{index + 1}")
    print("-" * 40)
    
    # 基本信息
    print(f"类型: {doc.get('type', 'unknown')}")
    print(f"来源: {doc.get('source', 'unknown')}")
    print(f"页面: {doc.get('page', 'unknown')}")
    
    # 特殊属性
    if doc.get('semantic_enhanced'):
        print(f"语义增强: ✅")
    
    if doc.get('financial_relevance', 0) > 0:
        print(f"财务相关性: {doc.get('financial_relevance', 0):.2f}")
    
    if doc.get('table_info'):
        table_info = doc.get('table_info')
        print(f"表格信息: {table_info.get('rows', 0)}行 x {table_info.get('cols', 0)}列")
    
    # 内容
    content = doc.get('content', '')
    if show_full:
        print(f"\n内容:\n{content}")
    else:
        # 显示前200字符
        preview = content[:200] + "..." if len(content) > 200 else content
        print(f"\n内容预览:\n{preview}")

def filter_documents(documents: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    根据条件过滤文档
    
    Args:
        documents: 文档列表
        filters: 过滤条件
        
    Returns:
        List[Dict[str, Any]]: 过滤后的文档列表
    """
    filtered = documents
    
    # 按类型过滤
    if filters.get('type'):
        filtered = [doc for doc in filtered if doc.get('type') == filters['type']]
    
    # 按来源过滤
    if filters.get('source'):
        source_filter = filters['source'].lower()
        filtered = [doc for doc in filtered 
                   if source_filter in doc.get('source', '').lower()]
    
    # 按语义增强过滤
    if filters.get('enhanced') is not None:
        filtered = [doc for doc in filtered 
                   if doc.get('semantic_enhanced', False) == filters['enhanced']]
    
    # 按财务相关性过滤
    if filters.get('min_financial_relevance'):
        min_rel = filters['min_financial_relevance']
        filtered = [doc for doc in filtered 
                   if doc.get('financial_relevance', 0) >= min_rel]
    
    return filtered

def interactive_mode(documents: List[Dict[str, Any]]):
    """
    交互式查看模式
    
    Args:
        documents: 文档列表
    """
    print(f"\n🔍 进入交互式查看模式")
    print("可用命令:")
    print("  search <query>     - 在内容中搜索")
    print("  filter type=<type> - 按类型过滤")
    print("  filter source=<source> - 按来源过滤")
    print("  filter enhanced=true/false - 按语义增强过滤")
    print("  show <index>       - 显示指定文档的完整内容")
    print("  list [start] [end] - 列出文档（可指定范围）")
    print("  stats              - 显示统计信息")
    print("  help               - 显示帮助")
    print("  quit               - 退出")
    
    current_docs = documents
    
    while True:
        try:
            command = input(f"\n[{len(current_docs)} 个文档] > ").strip()
            
            if not command:
                continue
            
            if command == "quit" or command == "q":
                break
            elif command == "help" or command == "h":
                print("可用命令: search, filter, show, list, stats, help, quit")
            elif command == "stats":
                show_statistics(current_docs)
            elif command.startswith("search "):
                query = command[7:]
                results = search_documents(current_docs, query, "content")
                print(f"\n🔍 搜索 '{query}' 找到 {len(results)} 个结果:")
                for i, doc in enumerate(results[:10]):  # 显示前10个
                    display_document(doc, i, False)
                current_docs = results
            elif command.startswith("filter "):
                filter_str = command[7:]
                filters = {}
                
                # 解析过滤条件
                if "type=" in filter_str:
                    filters['type'] = filter_str.split("type=")[1].split()[0]
                if "source=" in filter_str:
                    filters['source'] = filter_str.split("source=")[1].split()[0]
                if "enhanced=true" in filter_str:
                    filters['enhanced'] = True
                elif "enhanced=false" in filter_str:
                    filters['enhanced'] = False
                
                filtered = filter_documents(current_docs, filters)
                print(f"\n📋 过滤后剩余 {len(filtered)} 个文档")
                current_docs = filtered
            elif command.startswith("show "):
                try:
                    index = int(command[5:]) - 1
                    if 0 <= index < len(current_docs):
                        display_document(current_docs[index], index, True)
                    else:
                        print(f"❌ 索引超出范围 (1-{len(current_docs)})")
                except ValueError:
                    print("❌ 请输入有效的数字索引")
            elif command.startswith("list"):
                parts = command.split()
                start = 0
                end = min(10, len(current_docs))
                
                if len(parts) >= 2:
                    try:
                        start = int(parts[1]) - 1
                    except ValueError:
                        pass
                if len(parts) >= 3:
                    try:
                        end = int(parts[2])
                    except ValueError:
                        pass
                
                start = max(0, start)
                end = min(len(current_docs), end)
                
                print(f"\n📋 文档列表 ({start+1}-{end}):")
                for i in range(start, end):
                    display_document(current_docs[i], i, False)
            else:
                print("❌ 未知命令，输入 'help' 查看帮助")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FAISS库内容查看器")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    parser.add_argument("--search", type=str, help="搜索内容")
    parser.add_argument("--type", type=str, help="按类型过滤")
    parser.add_argument("--source", type=str, help="按来源过滤")
    parser.add_argument("--enhanced", action="store_true", help="只显示语义增强片段")
    parser.add_argument("--limit", type=int, default=10, help="显示结果数量限制")
    parser.add_argument("--full", action="store_true", help="显示完整内容")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")
    
    args = parser.parse_args()
    
    print("🔍 FAISS库内容查看器")
    print("=" * 50)
    
    # 加载文档
    documents = load_faiss_metadata()
    if not documents:
        return
    
    # 应用过滤器
    filters = {}
    if args.type:
        filters['type'] = args.type
    if args.source:
        filters['source'] = args.source
    if args.enhanced:
        filters['enhanced'] = True
    
    if filters:
        documents = filter_documents(documents, filters)
        print(f"📋 过滤后剩余 {len(documents)} 个文档")
    
    # 执行操作
    if args.interactive:
        interactive_mode(documents)
    elif args.stats:
        show_statistics(documents)
    elif args.search:
        results = search_documents(documents, args.search, "content")
        print(f"\n🔍 搜索 '{args.search}' 找到 {len(results)} 个结果:")
        for i, doc in enumerate(results[:args.limit]):
            display_document(doc, i, args.full)
    else:
        # 默认显示前几个文档
        print(f"\n📋 显示前 {min(args.limit, len(documents))} 个文档:")
        for i, doc in enumerate(documents[:args.limit]):
            display_document(doc, i, args.full)
        
        if len(documents) > args.limit:
            print(f"\n... 还有 {len(documents) - args.limit} 个文档")
            print("使用 --interactive 进入交互式模式查看更多")

if __name__ == "__main__":
    main()
