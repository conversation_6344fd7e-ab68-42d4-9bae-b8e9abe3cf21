#!/usr/bin/env python3
"""
批量嵌入处理器
使用阿里云百炼平台的BatchTextEmbedding API进行异步批量文本嵌入
"""

import asyncio
import json
import time
import tempfile
import os
from typing import List, Dict, Any, Optional
from pathlib import Path
import numpy as np

try:
    import dashscope
    from dashscope import BatchTextEmbedding
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False
    print("⚠ dashscope未安装，批量嵌入功能不可用")

from config.config import DASHSCOPE_API_KEY, ALI_EMBEDDING_MODEL, VECTOR_DIM


class BatchEmbeddingProcessor:
    """批量嵌入处理器"""
    
    def __init__(self):
        """初始化批量嵌入处理器"""
        self.api_key = DASHSCOPE_API_KEY
        self.model = ALI_EMBEDDING_MODEL
        self.vector_dim = VECTOR_DIM
        
        if DASHSCOPE_AVAILABLE and self.api_key:
            dashscope.api_key = self.api_key
            print("✓ 批量嵌入处理器初始化成功")
        else:
            print("⚠ 批量嵌入处理器初始化失败，将使用回退方案")
    
    def process_texts_batch(self, texts: List[str]) -> np.ndarray:
        """
        批量处理文本嵌入
        
        Args:
            texts: 文本列表
            
        Returns:
            np.ndarray: 嵌入向量数组
        """
        if not DASHSCOPE_AVAILABLE or not self.api_key:
            print("⚠ 批量处理不可用，使用随机向量")
            return self._generate_random_vectors(len(texts))
        
        try:
            return self._process_batch_async(texts)
        except Exception as e:
            print(f"❌ 批量处理失败: {e}")
            print("⚠ 回退到随机向量")
            return self._generate_random_vectors(len(texts))
    
    def _process_batch_async(self, texts: List[str]) -> np.ndarray:
        """
        异步批量处理文本嵌入
        
        Args:
            texts: 文本列表
            
        Returns:
            np.ndarray: 嵌入向量数组
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                temp_file = f.name
                print(f"📄 创建批处理文件: {temp_file}")
                
                # 写入文本数据
                for text in texts:
                    f.write(text.strip() + '\n')
                
                print(f"📊 文件包含 {len(texts)} 个文本")
            
            # 创建异步嵌入任务
            print("🚀 创建异步嵌入任务...")
            task = BatchTextEmbedding.call(
                model=self.model,
                input_file_url=temp_file,
                text_type='document'
            )
            
            if not task or not hasattr(task, 'task_id'):
                raise Exception("任务创建失败")
            
            task_id = task.task_id
            print(f"✅ 任务创建成功，任务ID: {task_id}")
            
            # 等待任务完成
            print("⏳ 等待任务完成...")
            max_wait_time = 300  # 最大等待5分钟
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                status = BatchTextEmbedding.wait(task_id=task_id)
                
                if status.task_status == 'SUCCEEDED':
                    print("✅ 任务完成")
                    break
                elif status.task_status == 'FAILED':
                    raise Exception(f"任务失败: {status}")
                else:
                    print(f"⏳ 任务状态: {status.task_status}")
                    time.sleep(10)
            else:
                raise Exception("任务超时")
            
            # 获取结果
            print("📥 获取嵌入结果...")
            result = BatchTextEmbedding.get_result(task_id=task_id)
            
            if not result or not hasattr(result, 'output'):
                raise Exception("结果获取失败")
            
            # 解析结果
            embeddings = []
            for item in result.output:
                if hasattr(item, 'embedding') and item.embedding:
                    embeddings.append(item.embedding)
                else:
                    # 如果某个文本没有嵌入，使用随机向量
                    embeddings.append(np.random.randn(self.vector_dim).tolist())
            
            print(f"✅ 获取到 {len(embeddings)} 个嵌入向量")
            
            # 清理临时文件
            try:
                os.unlink(temp_file)
                print(f"🗑️ 清理临时文件: {temp_file}")
            except:
                pass
            
            return np.array(embeddings, dtype=np.float32)
            
        except Exception as e:
            print(f"❌ 创建异步任务异常: {e}")
            # 清理临时文件
            try:
                if 'temp_file' in locals():
                    os.unlink(temp_file)
                    print(f"🗑️ 清理临时文件: {temp_file}")
            except:
                pass
            
            raise e
    
    def _generate_random_vectors(self, count: int) -> np.ndarray:
        """
        生成随机向量作为回退方案
        
        Args:
            count: 向量数量
            
        Returns:
            np.ndarray: 随机向量数组
        """
        print(f"⚠ 异步任务创建失败，回退到随机向量")
        return np.random.randn(count, self.vector_dim).astype(np.float32)
    
    def is_available(self) -> bool:
        """
        检查批量处理是否可用
        
        Returns:
            bool: 是否可用
        """
        return DASHSCOPE_AVAILABLE and bool(self.api_key)
    
    def get_batch_size_limit(self) -> int:
        """
        获取批量处理的大小限制
        
        Returns:
            int: 批量大小限制
        """
        return 1000  # 阿里云批量API的限制
    
    def should_use_batch(self, text_count: int) -> bool:
        """
        判断是否应该使用批量处理
        
        Args:
            text_count: 文本数量
            
        Returns:
            bool: 是否使用批量处理
        """
        return (
            self.is_available() and 
            text_count >= 10 and  # 至少10个文本才使用批量
            text_count <= self.get_batch_size_limit()
        )


# 全局批量处理器实例
_batch_processor = None

def get_batch_processor() -> BatchEmbeddingProcessor:
    """
    获取全局批量处理器实例
    
    Returns:
        BatchEmbeddingProcessor: 批量处理器实例
    """
    global _batch_processor
    if _batch_processor is None:
        _batch_processor = BatchEmbeddingProcessor()
    return _batch_processor


def process_texts_batch(texts: List[str]) -> np.ndarray:
    """
    批量处理文本嵌入的便捷函数
    
    Args:
        texts: 文本列表
        
    Returns:
        np.ndarray: 嵌入向量数组
    """
    processor = get_batch_processor()
    return processor.process_texts_batch(texts)


if __name__ == "__main__":
    # 测试批量嵌入处理器
    processor = BatchEmbeddingProcessor()
    
    test_texts = [
        "这是第一个测试文本",
        "这是第二个测试文本",
        "这是第三个测试文本"
    ]
    
    print("🧪 测试批量嵌入处理器")
    print(f"📊 测试文本数量: {len(test_texts)}")
    print(f"🔧 处理器可用性: {processor.is_available()}")
    
    if processor.should_use_batch(len(test_texts)):
        print("✅ 使用批量处理")
        embeddings = processor.process_texts_batch(test_texts)
        print(f"📊 生成嵌入向量: {embeddings.shape}")
    else:
        print("⚠ 不使用批量处理")
