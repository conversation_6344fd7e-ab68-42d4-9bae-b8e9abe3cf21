"""
查询引擎模块
专注于从已构建的知识库中进行检索和问答
"""

from typing import List, Dict, Any, Optional, AsyncGenerator, Union

from .embedding_model import EmbeddingModel
from .hybrid_vector_store import HybridVectorStore
from .rerank_model import RerankModel
from .llm_client import LLMClient
from config.config import TOP_K, SCORE_THRESHOLD


class QueryEngine:
    """查询引擎类"""
    
    def __init__(self, llm_api_key: Optional[str] = None):
        """
        初始化查询引擎
        
        Args:
            llm_api_key: LLM API密钥，如果不提供则从环境变量获取
        """
        self.embedding_model = EmbeddingModel()
        self.vector_store = HybridVectorStore()
        self.rerank_model = RerankModel()
        self.llm_client = LLMClient(api_key=llm_api_key)

        print("✅ 查询引擎初始化成功（混合检索版）")
    
    def check_knowledge_base(self) -> bool:
        """检查知识库是否可用"""
        stats = self.vector_store.get_stats()
        if stats["total_vectors"] == 0:
            print("❌ 知识库为空，请先构建知识库")
            return False

        print(f"✅ 混合向量库可用: {stats['total_vectors']} 个向量")
        print(f"  - IVF索引: {stats['ivf_vectors']} 个向量")
        print(f"  - HNSW索引: {stats['hnsw_vectors']} 个向量")
        return True
    
    def retrieve_documents(
        self, 
        query: str, 
        top_k: int = TOP_K,
        use_rerank: bool = True
    ) -> List[Dict[str, Any]]:
        """
        检索相关文档
        
        Args:
            query: 查询文本
            top_k: 返回的文档数量
            use_rerank: 是否使用重排序
            
        Returns:
            List[Dict[str, Any]]: 检索到的文档列表
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode(query)
            
            # 向量检索（混合检索）
            retrieved_docs = self.vector_store.search(
                query_vector=query_embedding[0],  # 取第一个查询的向量
                limit=top_k * 2 if use_rerank else top_k  # 如果要重排序则检索更多
            )
            
            if not retrieved_docs:
                return []
            
            # 重排序（如果启用）
            if use_rerank:
                reranked_docs = self.rerank_model.rerank(
                    query=query,
                    documents=retrieved_docs,
                    top_k=top_k
                )
            else:
                reranked_docs = retrieved_docs[:top_k]
            
            # 过滤低分文档
            filtered_docs = [
                doc for doc in reranked_docs
                if doc.get("rerank_score", doc.get("score", 0)) > SCORE_THRESHOLD
            ]
            
            return filtered_docs
            
        except Exception as e:
            print(f"❌ 文档检索失败: {str(e)}")
            return []
    
    async def query(
        self,
        query: str,
        chat_history: Optional[List[Dict[str, str]]] = None,
        stream: bool = True,
        top_k: int = TOP_K,
        use_rerank: bool = True
    ) -> Union[AsyncGenerator[str, None], str]:
        """
        执行查询
        
        Args:
            query: 查询文本
            chat_history: 聊天历史
            stream: 是否使用流式响应
            top_k: 检索文档数量
            use_rerank: 是否使用重排序
            
        Returns:
            Union[AsyncGenerator[str, None], str]: 响应文本或异步生成器
        """
        try:
            # 检查知识库
            if not self.check_knowledge_base():
                error_message = "知识库为空，请先使用知识库管理工具添加文档。"
                if stream:
                    async def error_generator():
                        yield error_message
                    return error_generator()
                else:
                    return error_message
            
            # 简化查询处理
            print("🔍 使用简化检索模式")

            # 检索相关文档
            retrieved_docs = self.retrieve_documents(
                query=query,
                top_k=top_k,
                use_rerank=use_rerank
            )

            # 构建上下文
            if not retrieved_docs:
                context = "未找到相关信息。"
            else:
                context = self.llm_client.format_retrieved_context(retrieved_docs)

            # 准备消息
            messages = []
            if chat_history:
                messages.extend(chat_history)
            messages.append({"role": "user", "content": query})

            # 调用LLM
            if stream:
                return self.llm_client.chat_stream(messages=messages, context=context)
            else:
                return await self.llm_client.chat(messages=messages, context=context)
                
        except Exception as e:
            error_message = f"查询处理出错: {str(e)}"
            if stream:
                async def error_generator():
                    yield error_message
                return error_generator()
            else:
                return error_message
    
    def similarity_search(
        self, 
        query: str, 
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """
        相似度搜索（不使用LLM）
        
        Args:
            query: 查询文本
            top_k: 返回的文档数量
            
        Returns:
            List[Dict[str, Any]]: 相似文档列表
        """
        return self.retrieve_documents(
            query=query,
            top_k=top_k,
            use_rerank=False
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取查询引擎统计信息"""
        vector_stats = self.vector_store.get_stats()
        
        return {
            "vector_count": vector_stats["total_vectors"],
            "vector_dim": vector_stats["vector_dim"],
            "embedding_model": "API" if not self.embedding_model.use_local else "Local",
            "rerank_enabled": self.rerank_model.use_rerank,
            "top_k": TOP_K,
            "score_threshold": SCORE_THRESHOLD
        }
