"""
查询引擎模块
专注于从已构建的知识库中进行检索和问答
"""

import time
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

from .embedding_model import EmbeddingModel
from .hybrid_vector_store import HybridVectorStore
from .rerank_model import RerankModel
from .llm_client import LLMClient
from .enhanced_query_processor import EnhancedQueryProcessor
from .financial_data_retriever import FinancialDataRetriever
from .two_stage_retriever import TwoStageRetriever
from config.config import TOP_K, SCORE_THRESHOLD


class QueryEngine:
    """查询引擎类"""
    
    def __init__(self, llm_api_key: Optional[str] = None):
        """
        初始化查询引擎
        
        Args:
            llm_api_key: LLM API密钥，如果不提供则从环境变量获取
        """
        self.embedding_model = EmbeddingModel()
        self.vector_store = HybridVectorStore()
        self.rerank_model = RerankModel()
        self.llm_client = LLMClient(api_key=llm_api_key)
        self.query_processor = EnhancedQueryProcessor()
        self.financial_retriever = FinancialDataRetriever()
        self.two_stage_retriever = TwoStageRetriever(self.vector_store, self.embedding_model)

        print("✅ 查询引擎初始化成功（混合检索版）")
    
    def check_knowledge_base(self) -> bool:
        """检查知识库是否可用"""
        stats = self.vector_store.get_stats()
        if stats["total_vectors"] == 0:
            print("❌ 知识库为空，请先构建知识库")
            return False

        print(f"✅ 混合向量库可用: {stats['total_vectors']} 个向量")
        print(f"  - IVF索引: {stats['ivf_vectors']} 个向量")
        print(f"  - HNSW索引: {stats['hnsw_vectors']} 个向量")
        return True
    
    def retrieve_documents(
        self,
        query: str,
        top_k: int = TOP_K,
        use_rerank: bool = True
    ) -> List[Dict[str, Any]]:
        """
        检索相关文档（带详细耗时统计）

        Args:
            query: 查询文本
            top_k: 返回的文档数量
            use_rerank: 是否使用重排序

        Returns:
            List[Dict[str, Any]]: 检索到的文档列表
        """
        total_start_time = time.time()
        print(f"\n🔍 开始检索流程...")
        print(f"📝 查询内容: {query}")
        print(f"🎯 目标数量: {top_k}")
        print(f"🔄 重排序: {'启用' if use_rerank else '禁用'}")

        try:
            # 1. 生成查询向量
            embedding_start = time.time()
            query_embedding = self.embedding_model.encode(query)
            embedding_time = time.time() - embedding_start
            print(f"⏱️  向量化耗时: {embedding_time:.3f}秒")

            # 2. 向量检索（混合检索）
            search_start = time.time()
            search_limit = top_k * 2 if use_rerank else top_k
            print(f"🔍 开始混合向量检索 (limit={search_limit})...")

            retrieved_docs = self.vector_store.search(
                query_vector=query_embedding[0],  # 取第一个查询的向量
                limit=search_limit
            )
            search_time = time.time() - search_start
            print(f"⏱️  向量检索耗时: {search_time:.3f}秒")
            print(f"📊 检索到 {len(retrieved_docs)} 个候选文档")

            if not retrieved_docs:
                print("❌ 未检索到任何文档")
                return []

            # 打印检索到的原始文档
            self._print_retrieved_docs(retrieved_docs, "向量检索结果")

            # 3. 重排序（如果启用）
            if use_rerank:
                rerank_start = time.time()
                print(f"🔄 开始重排序处理...")

                reranked_docs = self.rerank_model.rerank(
                    query=query,
                    documents=retrieved_docs,
                    top_k=top_k
                )
                rerank_time = time.time() - rerank_start
                print(f"⏱️  重排序耗时: {rerank_time:.3f}秒")
                print(f"📊 重排序后保留 {len(reranked_docs)} 个文档")

                # 打印重排序后的文档
                self._print_retrieved_docs(reranked_docs, "重排序结果")
            else:
                reranked_docs = retrieved_docs[:top_k]
                print(f"⏭️  跳过重排序，直接取前 {top_k} 个结果")

            # 4. 过滤低分文档
            filter_start = time.time()
            filtered_docs = [
                doc for doc in reranked_docs
                if doc.get("rerank_score", doc.get("score", 0)) > SCORE_THRESHOLD
            ]
            filter_time = time.time() - filter_start
            print(f"⏱️  分数过滤耗时: {filter_time:.3f}秒")
            print(f"📊 过滤后保留 {len(filtered_docs)} 个文档 (阈值: {SCORE_THRESHOLD})")

            # 打印最终结果
            if filtered_docs:
                self._print_retrieved_docs(filtered_docs, "最终检索结果")

            # 总耗时统计
            total_time = time.time() - total_start_time
            print(f"\n⏱️  总检索耗时: {total_time:.3f}秒")
            print(f"   - 向量化: {embedding_time:.3f}秒 ({embedding_time/total_time*100:.1f}%)")
            print(f"   - 向量检索: {search_time:.3f}秒 ({search_time/total_time*100:.1f}%)")
            if use_rerank:
                print(f"   - 重排序: {rerank_time:.3f}秒 ({rerank_time/total_time*100:.1f}%)")
            print(f"   - 分数过滤: {filter_time:.3f}秒 ({filter_time/total_time*100:.1f}%)")
            print("=" * 60)

            return filtered_docs

        except Exception as e:
            total_time = time.time() - total_start_time
            print(f"❌ 文档检索失败: {str(e)}")
            print(f"⏱️  失败前耗时: {total_time:.3f}秒")
            return []

    def _print_retrieved_docs(self, docs: List[Dict[str, Any]], stage_name: str):
        """
        打印检索到的文档内容

        Args:
            docs: 文档列表
            stage_name: 阶段名称
        """
        print(f"\n📋 {stage_name} ({len(docs)} 个文档):")
        print("-" * 50)

        for i, doc in enumerate(docs, 1):
            # 基本信息
            source = doc.get('source', 'Unknown')
            page = doc.get('page', 0)
            doc_type = doc.get('type', 'unknown')

            # 分数信息
            original_score = doc.get('score', 0)
            rerank_score = doc.get('rerank_score', None)
            search_method = doc.get('search_method', 'unknown')

            print(f"📄 文档 {i}:")
            print(f"   📁 来源: {source}")
            print(f"   📄 页码: {page}")
            print(f"   🏷️  类型: {doc_type}")
            print(f"   🔍 检索方法: {search_method}")
            print(f"   📊 原始分数: {original_score:.4f}")
            if rerank_score is not None:
                print(f"   🔄 重排序分数: {rerank_score:.4f}")

            # 内容预览
            content = doc.get('content', '')
            if content:
                # 限制内容长度，避免输出过长
                if len(content) > 200:
                    content_preview = content[:200] + "..."
                    print(f"   📝 内容预览: {content_preview}")
                    print(f"   📏 完整长度: {len(content)} 字符")
                else:
                    print(f"   📝 完整内容: {content}")
            else:
                print(f"   📝 内容: [空]")

            print()  # 空行分隔

    async def query(
        self,
        query: str,
        chat_history: Optional[List[Dict[str, str]]] = None,
        stream: bool = True,
        top_k: int = TOP_K,
        use_rerank: bool = True
    ) -> Union[AsyncGenerator[str, None], str]:
        """
        执行查询
        
        Args:
            query: 查询文本
            chat_history: 聊天历史
            stream: 是否使用流式响应
            top_k: 检索文档数量
            use_rerank: 是否使用重排序
            
        Returns:
            Union[AsyncGenerator[str, None], str]: 响应文本或异步生成器
        """
        try:
            # 检查知识库
            if not self.check_knowledge_base():
                error_message = "知识库为空，请先使用知识库管理工具添加文档。"
                if stream:
                    async def error_generator():
                        yield error_message
                    return error_generator()
                else:
                    return error_message
            
            # 简化查询处理
            print("🔍 使用简化检索模式")
            query_start_time = time.time()

            # 处理查询，提取关键信息
            try:
                query_info = self.query_processor.process_query(query)
                print(f"🔍 查询分析: 公司={query_info.get('company_name', '未识别')}, "
                      f"指标={query_info.get('financial_indicators', [])}, "
                      f"时间={query_info.get('time_period', '未识别')}")
                print(f"🔍 增强查询: {query_info.get('enhanced_query', query)}")
            except Exception as e:
                print(f"⚠️  查询处理失败: {e}")
                query_info = {"enhanced_query": query, "company_name": None}

            # 使用两阶段检索策略
            company_name = query_info.get("company_name")
            if company_name:
                print(f"🔍 使用两阶段检索: 先筛选 '{company_name}' 的文档，再进行内容检索")
                retrieved_docs = self.two_stage_retriever.two_stage_search(
                    query=query_info["enhanced_query"],
                    company_name=company_name,
                    top_k=top_k,
                    use_rerank=use_rerank
                )
            else:
                print("🔍 未指定公司，使用常规向量检索")
                retrieved_docs = self.retrieve_documents(
                    query=query_info["enhanced_query"],
                    top_k=top_k,
                    use_rerank=use_rerank
                )

            # 构建上下文
            context_start = time.time()
            if not retrieved_docs:
                context = "未找到相关信息。"
                print("❌ 未找到相关文档，将返回空上下文")
            else:
                context = self.llm_client.format_retrieved_context(retrieved_docs)
                print(f"\n📝 构建LLM上下文...")
                print(f"📊 上下文长度: {len(context)} 字符")
                print(f"📋 包含文档数: {len(retrieved_docs)} 个")

                # 打印发送给LLM的上下文（截断显示）
                if len(context) > 500:
                    context_preview = context[:500] + "..."
                    print(f"📄 上下文预览:\n{context_preview}")
                else:
                    print(f"📄 完整上下文:\n{context}")

            context_time = time.time() - context_start
            print(f"⏱️  上下文构建耗时: {context_time:.3f}秒")

            # 准备消息
            messages = []
            if chat_history:
                messages.extend(chat_history)
            messages.append({"role": "user", "content": query})

            # 调用LLM
            llm_start = time.time()
            print(f"\n🤖 开始调用LLM...")

            if stream:
                print("🔄 使用流式响应模式")
                return self.llm_client.chat_stream(messages=messages, context=context)
            else:
                print("📝 使用同步响应模式")
                result = await self.llm_client.chat(messages=messages, context=context)
                llm_time = time.time() - llm_start
                total_query_time = time.time() - query_start_time

                print(f"⏱️  LLM调用耗时: {llm_time:.3f}秒")
                print(f"⏱️  总查询耗时: {total_query_time:.3f}秒")
                print("=" * 60)

                return result
                
        except Exception as e:
            error_message = f"查询处理出错: {str(e)}"
            if stream:
                async def error_generator():
                    yield error_message
                return error_generator()
            else:
                return error_message
    
    def similarity_search(
        self, 
        query: str, 
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """
        相似度搜索（不使用LLM）
        
        Args:
            query: 查询文本
            top_k: 返回的文档数量
            
        Returns:
            List[Dict[str, Any]]: 相似文档列表
        """
        return self.retrieve_documents(
            query=query,
            top_k=top_k,
            use_rerank=False
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取查询引擎统计信息"""
        vector_stats = self.vector_store.get_stats()
        
        return {
            "vector_count": vector_stats["total_vectors"],
            "vector_dim": vector_stats["vector_dim"],
            "embedding_model": "API" if not self.embedding_model.use_local else "Local",
            "rerank_enabled": self.rerank_model.use_rerank,
            "top_k": TOP_K,
            "score_threshold": SCORE_THRESHOLD
        }
