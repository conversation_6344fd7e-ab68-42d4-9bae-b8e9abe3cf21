"""
繁简转换模块
使用OpenCC将OCR识别后的繁体中文转换为简体中文，保证语义和结构
"""

import re
from typing import Dict, List

try:
    import opencc
    OPENCC_AVAILABLE = True
except ImportError:
    OPENCC_AVAILABLE = False

class TextConverter:
    """文本转换器"""
    
    def __init__(self):
        """初始化转换器"""
        # 初始化OpenCC转换器
        if OPENCC_AVAILABLE:
            try:
                # 使用繁体到简体的转换配置
                self.converter = opencc.OpenCC('t2s')  # Traditional to Simplified
                self.use_opencc = True
                print("✓ OpenCC繁简转换器初始化成功")
            except Exception as e:
                print(f"⚠ OpenCC初始化失败: {e}, 使用备用词典")
                self.use_opencc = False
                self._init_fallback_dict()
        else:
            print("⚠ OpenCC未安装，使用备用词典")
            self.use_opencc = False
            self._init_fallback_dict()

    def _init_fallback_dict(self):
        """初始化备用繁简对照表（财报相关）"""
        self.traditional_to_simplified = {
            # 公司治理相关
            '董事會': '董事会',
            '董事長': '董事长',
            '執行董事': '执行董事',
            '獨立董事': '独立董事',
            '監事會': '监事会',
            '薪酬委員會': '薪酬委员会',
            '審計委員會': '审计委员会',
            '提名委員會': '提名委员会',
            '風險委員會': '风险委员会',
            '戰略委員會': '战略委员会',
            
            # 财务相关
            '財務': '财务',
            '財政': '财政',
            '營業': '营业',
            '營收': '营收',
            '營運': '营运',
            '資產': '资产',
            '負債': '负债',
            '權益': '权益',
            '現金': '现金',
            '應收': '应收',
            '應付': '应付',
            '預付': '预付',
            '預收': '预收',
            '庫存': '库存',
            '投資': '投资',
            '貸款': '贷款',
            '債務': '债务',
            '債券': '债券',
            '股東': '股东',
            '股份': '股份',
            '股票': '股票',
            '股權': '股权',
            '資本': '资本',
            '盈利': '盈利',
            '虧損': '亏损',
            '損益': '损益',
            '毛利': '毛利',
            '淨利': '净利',
            '稅前': '税前',
            '稅後': '税后',
            '攤銷': '摊销',
            '折舊': '折旧',
            
            # 业务相关
            '業務': '业务',
            '產品': '产品',
            '銷售': '销售',
            '採購': '采购',
            '製造': '制造',
            '研發': '研发',
            '開發': '开发',
            '創新': '创新',
            '技術': '技术',
            '專利': '专利',
            '品牌': '品牌',
            '市場': '市场',
            '競爭': '竞争',
            '客戶': '客户',
            '供應商': '供应商',
            '合作夥伴': '合作伙伴',
            
            # 地区相关
            '中國': '中国',
            '臺灣': '台湾',
            '香港': '香港',
            '澳門': '澳门',
            '歐洲': '欧洲',
            '亞洲': '亚洲',
            '美國': '美国',
            '國際': '国际',
            '全球': '全球',
            '海外': '海外',
            
            # 时间相关
            '財年': '财年',
            '會計': '会计',
            '年度': '年度',
            '季度': '季度',
            '月份': '月份',
            '期間': '期间',
            '截至': '截至',
            
            # 其他常用
            '報告': '报告',
            '資訊': '资讯',
            '資料': '资料',
            '數據': '数据',
            '統計': '统计',
            '分析': '分析',
            '評估': '评估',
            '預測': '预测',
            '計劃': '计划',
            '策略': '策略',
            '目標': '目标',
            '風險': '风险',
            '機會': '机会',
            '挑戰': '挑战',
            '優勢': '优势',
            '劣勢': '劣势',
            '競爭力': '竞争力',
            '可持續': '可持续',
            '環境': '环境',
            '社會': '社会',
            '治理': '治理',
            '責任': '责任',
            '透明': '透明',
            '誠信': '诚信',
            '合規': '合规',
            '監管': '监管',
            '法規': '法规',
            '標準': '标准',
            '質量': '质量',
            '效率': '效率',
            '效益': '效益',
            '價值': '价值',
            '創造': '创造',
            '實現': '实现',
            '達成': '达成',
            '完成': '完成',
            '執行': '执行',
            '實施': '实施',
            '推進': '推进',
            '發展': '发展',
            '增長': '增长',
            '提升': '提升',
            '改善': '改善',
            '優化': '优化',
            '強化': '强化',
            '鞏固': '巩固',
            '維持': '维持',
            '保持': '保持',
            '確保': '确保',
            '保證': '保证',
            '承諾': '承诺',
            '責任': '责任',
            '義務': '义务',
            '權利': '权利',
            '權力': '权力',
            '職責': '职责',
            '職能': '职能',
            '職位': '职位',
            '職務': '职务',
            '管理': '管理',
            '領導': '领导',
            '決策': '决策',
            '監督': '监督',
            '控制': '控制',
            '協調': '协调',
            '溝通': '沟通',
            '合作': '合作',
            '配合': '配合',
            '支持': '支持',
            '協助': '协助',
            '服務': '服务',
            '滿足': '满足',
            '需求': '需求',
            '要求': '要求',
            '期望': '期望',
            '標準': '标准',
            '規範': '规范',
            '制度': '制度',
            '流程': '流程',
            '程序': '程序',
            '機制': '机制',
            '體系': '体系',
            '框架': '框架',
            '結構': '结构',
            '組織': '组织',
            '團隊': '团队',
            '人員': '人员',
            '員工': '员工',
            '職員': '职员',
            '幹部': '干部',
            '管理層': '管理层',
            '高管': '高管',
            '經理': '经理',
            '主管': '主管',
            '總監': '总监',
            '總經理': '总经理',
            '副總': '副总',
            '助理': '助理',
            '秘書': '秘书',
            '顧問': '顾问',
            '專家': '专家',
            '學者': '学者',
            '研究員': '研究员',
            '分析師': '分析师',
            '工程師': '工程师',
            '設計師': '设计师',
            '開發者': '开发者',
            '程序員': '程序员',
            '技術員': '技术员',
            '操作員': '操作员',
            '維修員': '维修员',
            '檢驗員': '检验员',
            '質檢員': '质检员',
            '安全員': '安全员',
            '保安員': '保安员',
            '清潔員': '清洁员',
            '服務員': '服务员',
            '銷售員': '销售员',
            '業務員': '业务员',
            '客服': '客服',
            '採購員': '采购员',
            '倉管員': '仓管员',
            '會計師': '会计师',
            '審計師': '审计师',
            '律師': '律师',
            '醫師': '医师',
            '護士': '护士',
            '教師': '教师',
            '講師': '讲师',
            '教授': '教授',
            '博士': '博士',
            '碩士': '硕士',
            '學士': '学士',
            '學生': '学生',
            '實習生': '实习生',
            '培訓生': '培训生',
            '志願者': '志愿者',
            '義工': '义工',
        }
        
        if not self.use_opencc:
            # 编译正则表达式以提高性能
            self.pattern = re.compile('|'.join(re.escape(key) for key in self.traditional_to_simplified.keys()))
            print(f"✓ 备用繁简转换器初始化成功（{len(self.traditional_to_simplified)} 个词汇）")
    
    def convert_traditional_to_simplified(self, text: str) -> str:
        """
        将OCR识别后的繁体中文转换为简体中文，保证语义和结构

        Args:
            text: OCR识别的输入文本

        Returns:
            str: 转换后的简体中文文本
        """
        if not text:
            return text

        if self.use_opencc:
            try:
                # 使用OpenCC进行转换，保证语义和结构
                converted_text = self.converter.convert(text)
                return converted_text
            except Exception as e:
                print(f"⚠ OpenCC转换失败: {e}, 使用备用方法")
                # 回退到词典方法
                return self._fallback_convert(text)
        else:
            # 使用备用词典方法
            return self._fallback_convert(text)

    def _fallback_convert(self, text: str) -> str:
        """
        备用转换方法，使用词典替换

        Args:
            text: 输入文本

        Returns:
            str: 转换后的文本
        """
        # 使用正则表达式进行批量替换
        def replace_func(match):
            return self.traditional_to_simplified[match.group(0)]

        converted_text = self.pattern.sub(replace_func, text)
        return converted_text
    
    def detect_traditional_chinese(self, text: str) -> bool:
        """
        检测文本是否包含繁体中文
        
        Args:
            text: 输入文本
            
        Returns:
            bool: 是否包含繁体中文
        """
        if not text:
            return False
        
        # 检查是否包含繁体字符
        for traditional_char in self.traditional_to_simplified.keys():
            if traditional_char in text:
                return True
        
        return False
    
    def get_conversion_stats(self, original_text: str, converted_text: str) -> Dict[str, int]:
        """
        获取转换统计信息
        
        Args:
            original_text: 原始文本
            converted_text: 转换后文本
            
        Returns:
            Dict[str, int]: 转换统计
        """
        conversions = {}
        
        for traditional, simplified in self.traditional_to_simplified.items():
            original_count = original_text.count(traditional)
            if original_count > 0:
                conversions[f"{traditional} -> {simplified}"] = original_count
        
        return conversions
