#!/usr/bin/env python3
"""
两阶段检索器
第一阶段：根据document_info筛选公司文档
第二阶段：在筛选结果中进行内容检索
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from .logger import get_logger


class TwoStageRetriever:
    """两阶段检索器"""
    
    def __init__(self, vector_store, embedding_model):
        """
        初始化两阶段检索器
        
        Args:
            vector_store: 向量存储
            embedding_model: 嵌入模型
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.logger = get_logger()
        
        # 公司名称映射
        self.company_mappings = {
            "伊利": "内蒙古伊利实业集团股份有限公司",
            "伊利集团": "内蒙古伊利实业集团股份有限公司",
            "伊利股份": "内蒙古伊利实业集团股份有限公司",
            "内蒙古伊利": "内蒙古伊利实业集团股份有限公司",
            
            "美的": "美的集团股份有限公司",
            "美的集团": "美的集团股份有限公司",
            
            "茅台": "贵州茅台酒股份有限公司",
            "贵州茅台": "贵州茅台酒股份有限公司",
            "茅台酒": "贵州茅台酒股份有限公司",
        }
    
    def two_stage_search(self, query: str, company_name: Optional[str] = None, 
                        top_k: int = 5, use_rerank: bool = True) -> List[Dict[str, Any]]:
        """
        两阶段检索
        
        Args:
            query: 查询内容
            company_name: 公司名称
            top_k: 返回结果数量
            use_rerank: 是否使用重排序
            
        Returns:
            List[Dict[str, Any]]: 检索结果
        """
        self.logger.info(f"开始两阶段检索: 查询='{query}', 公司='{company_name}', top_k={top_k}")
        
        # 第一阶段：根据document_info筛选公司文档
        if company_name:
            filtered_indices = self._filter_by_company(company_name)
            if not filtered_indices:
                self.logger.warning(f"未找到公司 '{company_name}' 的文档")
                return []
            
            self.logger.info(f"第一阶段筛选: 找到 {len(filtered_indices)} 个 '{company_name}' 的文档")
        else:
            # 如果没有指定公司，使用所有文档
            filtered_indices = list(range(len(self.vector_store.metadata)))
            self.logger.info(f"未指定公司，使用所有 {len(filtered_indices)} 个文档")
        
        # 第二阶段：在筛选后的文档中进行内容检索
        results = self._search_in_filtered_docs(query, filtered_indices, top_k, use_rerank)
        
        self.logger.info(f"两阶段检索完成: 返回 {len(results)} 个结果")
        
        return results
    
    def _filter_by_company(self, company_name: str) -> List[int]:
        """
        根据公司名称筛选文档索引
        
        Args:
            company_name: 公司名称
            
        Returns:
            List[int]: 匹配的文档索引列表
        """
        # 获取完整公司名称
        full_company_name = self.company_mappings.get(company_name, company_name)
        
        filtered_indices = []
        
        for i, doc in enumerate(self.vector_store.metadata):
            document_info = doc.get("document_info", "")
            
            # 检查document_info字段是否包含目标公司
            if (full_company_name in document_info or 
                company_name in document_info):
                filtered_indices.append(i)
        
        self.logger.debug(f"公司筛选结果: '{company_name}' -> {len(filtered_indices)} 个文档")
        
        return filtered_indices
    
    def _search_in_filtered_docs(self, query: str, filtered_indices: List[int], 
                                top_k: int, use_rerank: bool) -> List[Dict[str, Any]]:
        """
        在筛选后的文档中进行内容检索
        
        Args:
            query: 查询内容
            filtered_indices: 筛选后的文档索引
            top_k: 返回结果数量
            use_rerank: 是否使用重排序
            
        Returns:
            List[Dict[str, Any]]: 检索结果
        """
        if not filtered_indices:
            return []
        
        # 向量化查询
        query_vectors = self.embedding_model.encode_batch([query])
        query_vector = np.array(query_vectors[0]).reshape(1, -1)
        
        # 在筛选后的文档中计算相似度
        similarities = []

        # 混合策略：向量检索 + 关键词匹配
        try:
            # 1. 向量检索
            all_results = self.vector_store.search(query_vector, limit=len(self.vector_store.metadata))
            vector_similarities = {}

            for result in all_results:
                vector_id = result.get('vector_id', -1)
                if vector_id in filtered_indices:
                    score = result.get('score', 0)
                    vector_similarities[vector_id] = float(score)

            # 2. 关键词匹配增强
            keyword_similarities = self._keyword_matching(query, filtered_indices)

            # 3. 合并分数
            all_candidates = set(vector_similarities.keys()) | set(keyword_similarities.keys())

            for idx in all_candidates:
                vector_score = vector_similarities.get(idx, 0)
                keyword_score = keyword_similarities.get(idx, 0)

                # 组合分数：向量分数 + 关键词分数加权
                combined_score = vector_score + (keyword_score * 0.5)  # 关键词权重0.5
                similarities.append((idx, combined_score))

            self.logger.debug(f"混合检索: 向量结果 {len(vector_similarities)}, 关键词结果 {len(keyword_similarities)}, 合并结果 {len(similarities)}")

        except Exception as e:
            self.logger.error(f"混合检索失败: {e}")
            # 降级到简单的文本匹配
            similarities = self._fallback_text_matching(query, filtered_indices)
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # 取前top_k个结果
        top_similarities = similarities[:top_k * 2]  # 多取一些用于重排序
        
        # 构建结果
        results = []
        for idx, similarity in top_similarities:
            doc = self.vector_store.metadata[idx].copy()
            doc['index'] = idx
            doc['score'] = similarity
            doc['retrieval_method'] = 'two_stage_filtered'
            results.append(doc)
        
        # 重排序（如果启用）
        if use_rerank and len(results) > 1:
            results = self._rerank_results(query, results)
        
        # 返回最终的top_k个结果
        return results[:top_k]
    
    def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        重排序结果
        
        Args:
            query: 查询内容
            results: 检索结果
            
        Returns:
            List[Dict[str, Any]]: 重排序后的结果
        """
        try:
            from .rerank_model import RerankModel
            rerank_model = RerankModel()
            
            # 准备重排序数据
            documents = []
            for result in results:
                documents.append({
                    'content': result.get('content', ''),
                    'source': result.get('source', ''),
                    'page': result.get('page', 0),
                    'type': result.get('type', 'text')
                })
            
            # 执行重排序
            reranked_docs = rerank_model.rerank(query, documents, top_k=len(results))
            
            # 更新分数
            reranked_results = []
            for i, doc in enumerate(reranked_docs):
                # 找到对应的原始结果
                for result in results:
                    if (result.get('content', '') == doc.get('content', '') and
                        result.get('source', '') == doc.get('source', '')):
                        result_copy = result.copy()
                        result_copy['rerank_score'] = doc.get('rerank_score', 0)
                        reranked_results.append(result_copy)
                        break
            
            self.logger.info(f"重排序完成: {len(reranked_results)} 个结果")
            return reranked_results
            
        except Exception as e:
            self.logger.warning(f"重排序失败: {e}")
            return results
    
    def get_company_document_stats(self) -> Dict[str, int]:
        """
        获取各公司的文档统计
        
        Returns:
            Dict[str, int]: 公司名称到文档数量的映射
        """
        company_stats = {}
        
        for doc in self.vector_store.metadata:
            document_info = doc.get("document_info", "")
            
            # 尝试匹配已知公司
            matched_company = None
            for short_name, full_name in self.company_mappings.items():
                if full_name in document_info:
                    matched_company = short_name
                    break
            
            if matched_company:
                company_stats[matched_company] = company_stats.get(matched_company, 0) + 1
            else:
                # 未匹配的文档
                company_stats['其他'] = company_stats.get('其他', 0) + 1
        
        return company_stats
    
    def preview_company_documents(self, company_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        预览指定公司的文档
        
        Args:
            company_name: 公司名称
            limit: 预览数量限制
            
        Returns:
            List[Dict[str, Any]]: 文档预览列表
        """
        filtered_indices = self._filter_by_company(company_name)
        
        preview_docs = []
        for i, idx in enumerate(filtered_indices[:limit]):
            doc = self.vector_store.metadata[idx]
            preview_docs.append({
                'index': idx,
                'document_info': doc.get('document_info', ''),
                'source': doc.get('source', ''),
                'chunk_id': doc.get('chunk_id', ''),
                'content_preview': doc.get('content', '')[:200] + '...'
            })
        
        return preview_docs

    def _fallback_text_matching(self, query: str, filtered_indices: List[int]) -> List[Tuple[int, float]]:
        """
        降级文本匹配方法

        Args:
            query: 查询内容
            filtered_indices: 筛选后的文档索引

        Returns:
            List[Tuple[int, float]]: (索引, 分数) 列表
        """
        similarities = []
        query_lower = query.lower()
        query_words = set(query_lower.split())

        for idx in filtered_indices:
            try:
                doc = self.vector_store.metadata[idx]
                content = doc.get('content', '').lower()
                content_words = set(content.split())

                # 计算词汇重叠度
                if query_words and content_words:
                    intersection = query_words.intersection(content_words)
                    union = query_words.union(content_words)
                    jaccard_score = len(intersection) / len(union) if union else 0

                    # 检查是否包含查询中的关键词
                    keyword_bonus = 0
                    for word in query_words:
                        if word in content:
                            keyword_bonus += 0.1

                    final_score = jaccard_score + keyword_bonus
                    similarities.append((idx, final_score))

            except Exception as e:
                self.logger.debug(f"文本匹配失败 {idx}: {e}")
                continue

        self.logger.info(f"降级文本匹配找到 {len(similarities)} 个结果")
        return similarities

    def _keyword_matching(self, query: str, filtered_indices: List[int]) -> Dict[int, float]:
        """
        关键词匹配增强

        Args:
            query: 查询内容
            filtered_indices: 筛选后的文档索引

        Returns:
            Dict[int, float]: 索引到分数的映射
        """
        keyword_scores = {}
        query_lower = query.lower()

        # 提取查询中的关键词
        financial_keywords = ['营业收入', '净利润', '总资产', '净资产', '现金流', '毛利率']
        time_keywords = ['2025年', '2024年', '第一季度', '第二季度', '第三季度', '第四季度']

        query_financial_keywords = [kw for kw in financial_keywords if kw in query]
        query_time_keywords = [kw for kw in time_keywords if kw in query]

        for idx in filtered_indices:
            try:
                doc = self.vector_store.metadata[idx]
                content = doc.get('content', '').lower()

                score = 0.0

                # 财务关键词匹配
                for keyword in query_financial_keywords:
                    if keyword in content:
                        score += 2.0  # 财务关键词权重高

                # 时间关键词匹配
                for keyword in query_time_keywords:
                    if keyword in content:
                        score += 1.0

                # 数字匹配（特别重要）
                import re
                query_numbers = re.findall(r'\d{1,3}(?:,\d{3})*(?:\.\d+)?', query)
                for number in query_numbers:
                    if number in content:
                        score += 3.0  # 数字匹配权重最高

                # 表格内容加分
                if '|' in content and ('---' in content or '项目' in content):
                    score += 0.5

                if score > 0:
                    keyword_scores[idx] = score

            except Exception as e:
                self.logger.debug(f"关键词匹配失败 {idx}: {e}")
                continue

        self.logger.debug(f"关键词匹配找到 {len(keyword_scores)} 个结果")
        return keyword_scores
