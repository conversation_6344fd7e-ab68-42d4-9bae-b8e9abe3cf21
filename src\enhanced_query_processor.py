#!/usr/bin/env python3
"""
增强查询处理器
提升公司名称匹配和检索准确性
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from .logger import get_logger


class EnhancedQueryProcessor:
    """增强查询处理器"""
    
    def __init__(self):
        self.logger = get_logger()
        
        # 公司名称映射表
        self.company_mappings = {
            # 伊利集团相关
            "伊利": "内蒙古伊利实业集团股份有限公司",
            "伊利集团": "内蒙古伊利实业集团股份有限公司", 
            "伊利股份": "内蒙古伊利实业集团股份有限公司",
            "内蒙古伊利": "内蒙古伊利实业集团股份有限公司",
            
            # 美的集团相关
            "美的": "美的集团股份有限公司",
            "美的集团": "美的集团股份有限公司",
            
            # 茅台相关
            "茅台": "贵州茅台酒股份有限公司",
            "贵州茅台": "贵州茅台酒股份有限公司",
            "茅台酒": "贵州茅台酒股份有限公司",
        }
        
        # 财务指标关键词
        self.financial_keywords = [
            "营业收入", "净利润", "总资产", "净资产", "每股收益",
            "毛利率", "净利率", "资产负债率", "现金流",
            "研发费用", "销售费用", "管理费用"
        ]
        
        # 时间关键词
        self.time_keywords = [
            "2025年", "2024年", "第一季度", "第二季度", "第三季度", "第四季度",
            "年度", "半年度", "季度", "一季度", "二季度", "三季度", "四季度"
        ]
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """
        处理查询，提取关键信息
        
        Args:
            query: 原始查询
            
        Returns:
            Dict[str, Any]: 处理后的查询信息
        """
        result = {
            "original_query": query,
            "enhanced_query": query,
            "company_name": None,
            "full_company_name": None,
            "financial_indicators": [],
            "time_period": None,
            "query_type": "general",
            "boost_terms": []
        }
        
        # 提取公司名称
        company_info = self._extract_company_name(query)
        if company_info:
            result["company_name"] = company_info["short_name"]
            result["full_company_name"] = company_info["full_name"]
            result["boost_terms"].append(company_info["full_name"])
        
        # 提取财务指标
        financial_indicators = self._extract_financial_indicators(query)
        result["financial_indicators"] = financial_indicators
        result["boost_terms"].extend(financial_indicators)
        
        # 提取时间信息
        time_period = self._extract_time_period(query)
        result["time_period"] = time_period
        if time_period:
            result["boost_terms"].append(time_period)
        
        # 确定查询类型
        result["query_type"] = self._determine_query_type(result)
        
        # 生成增强查询
        result["enhanced_query"] = self._generate_enhanced_query(result)
        
        self.logger.debug(f"查询处理结果: {result}")
        
        return result
    
    def _extract_company_name(self, query: str) -> Optional[Dict[str, str]]:
        """提取公司名称"""
        for short_name, full_name in self.company_mappings.items():
            if short_name in query:
                return {
                    "short_name": short_name,
                    "full_name": full_name
                }
        
        # 尝试直接匹配完整公司名称
        for pattern in [
            r'([^，。\n]*?(?:股份有限公司|有限公司|集团股份有限公司))',
            r'([^，。\n]*?(?:集团|公司))'
        ]:
            matches = re.findall(pattern, query)
            if matches:
                company_name = matches[0].strip()
                if len(company_name) > 3:  # 过滤太短的匹配
                    return {
                        "short_name": company_name,
                        "full_name": company_name
                    }
        
        return None
    
    def _extract_financial_indicators(self, query: str) -> List[str]:
        """提取财务指标"""
        indicators = []
        for keyword in self.financial_keywords:
            if keyword in query:
                indicators.append(keyword)
        return indicators
    
    def _extract_time_period(self, query: str) -> Optional[str]:
        """提取时间信息"""
        for keyword in self.time_keywords:
            if keyword in query:
                return keyword
        
        # 尝试匹配年份+季度的组合
        year_quarter_match = re.search(r'(\d{4}年.*?(?:第[一二三四]季度|[一二三四]季度))', query)
        if year_quarter_match:
            return year_quarter_match.group(1)
        
        # 尝试匹配单独的年份
        year_match = re.search(r'(\d{4}年)', query)
        if year_match:
            return year_match.group(1)
        
        return None
    
    def _determine_query_type(self, query_info: Dict[str, Any]) -> str:
        """确定查询类型"""
        if query_info["company_name"] and query_info["financial_indicators"]:
            return "company_financial"
        elif query_info["company_name"]:
            return "company_general"
        elif query_info["financial_indicators"]:
            return "financial_general"
        else:
            return "general"
    
    def _generate_enhanced_query(self, query_info: Dict[str, Any]) -> str:
        """生成增强查询"""
        enhanced_parts = []
        
        # 添加完整公司名称
        if query_info["full_company_name"]:
            enhanced_parts.append(query_info["full_company_name"])
        
        # 添加财务指标
        enhanced_parts.extend(query_info["financial_indicators"])
        
        # 添加时间信息
        if query_info["time_period"]:
            enhanced_parts.append(query_info["time_period"])
        
        # 如果有增强信息，组合生成新查询
        if enhanced_parts:
            enhanced_query = " ".join(enhanced_parts)
            # 保留原查询的其他部分
            original_words = set(query_info["original_query"].split())
            enhanced_words = set(enhanced_query.split())
            remaining_words = original_words - enhanced_words
            
            if remaining_words:
                enhanced_query += " " + " ".join(remaining_words)
            
            return enhanced_query
        
        return query_info["original_query"]
    
    def filter_results_by_company(self, results: List[Dict[str, Any]], 
                                 target_company: str) -> List[Dict[str, Any]]:
        """
        根据公司名称过滤结果
        
        Args:
            results: 检索结果列表
            target_company: 目标公司名称
            
        Returns:
            List[Dict[str, Any]]: 过滤后的结果
        """
        if not target_company:
            return results
        
        # 获取完整公司名称
        full_company_name = self.company_mappings.get(target_company, target_company)
        
        filtered_results = []
        other_results = []
        
        for result in results:
            document_info = result.get("document_info", "")
            content = result.get("content", "")
            
            # 检查文档信息和内容中是否包含目标公司
            if (full_company_name in document_info or 
                full_company_name in content or
                target_company in document_info or
                target_company in content):
                
                # 计算公司匹配度
                company_match_score = 0
                if full_company_name in document_info:
                    company_match_score += 0.5
                if full_company_name in content:
                    company_match_score += 0.3
                if target_company in document_info:
                    company_match_score += 0.2
                
                # 添加公司匹配分数
                result["company_match_score"] = company_match_score
                filtered_results.append(result)
            else:
                other_results.append(result)
        
        # 如果有匹配的公司结果，优先返回；否则返回所有结果
        if filtered_results:
            # 按公司匹配分数排序
            filtered_results.sort(key=lambda x: x.get("company_match_score", 0), reverse=True)
            self.logger.info(f"找到 {len(filtered_results)} 个匹配 '{target_company}' 的结果")
            return filtered_results
        else:
            self.logger.warning(f"未找到匹配 '{target_company}' 的结果，返回所有结果")
            return results
    
    def boost_query_terms(self, query: str, boost_terms: List[str]) -> str:
        """
        为查询添加权重提升词汇
        
        Args:
            query: 原始查询
            boost_terms: 需要提升权重的词汇
            
        Returns:
            str: 增强后的查询
        """
        if not boost_terms:
            return query
        
        # 简单的权重提升：重复重要词汇
        boosted_query = query
        for term in boost_terms:
            if term not in query:
                boosted_query += f" {term}"
        
        return boosted_query
