#!/usr/bin/env python3
"""
并发文档处理器
支持多线程/多进程并发处理多个文档
"""

import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from queue import Queue
import multiprocessing as mp

from .logger import get_logger
from .simplified_document_processor import SimplifiedDocumentProcessor


class ConcurrentDocumentProcessor:
    """并发文档处理器"""
    
    def __init__(self, max_workers: Optional[int] = None, use_processes: bool = False):
        """
        初始化并发处理器
        
        Args:
            max_workers: 最大工作线程/进程数，None时自动检测
            use_processes: 是否使用多进程（True）还是多线程（False）
        """
        self.logger = get_logger()
        self.use_processes = use_processes
        
        # 确定工作器数量
        if max_workers is None:
            cpu_count = os.cpu_count() or 4
            # 对于I/O密集型任务（文档处理），线程数可以比CPU核心数多
            self.max_workers = cpu_count * 2 if not use_processes else cpu_count
        else:
            self.max_workers = max_workers
        
        self.logger.log_system_info("并发文档处理器", {
            "max_workers": self.max_workers,
            "execution_mode": "多进程" if use_processes else "多线程",
            "cpu_count": os.cpu_count()
        })
        
        # 进度跟踪
        self.progress_lock = threading.Lock()
        self.completed_count = 0
        self.total_count = 0
        self.results = []
    
    def process_documents_concurrent(self, file_paths: List[str], 
                                   save_results: bool = True,
                                   progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        并发处理多个文档
        
        Args:
            file_paths: 文件路径列表
            save_results: 是否保存处理结果
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """
        if not file_paths:
            self.logger.warning("没有文件需要处理")
            return []
        
        self.total_count = len(file_paths)
        self.completed_count = 0
        self.results = []
        
        self.logger.info(f"开始并发处理 {self.total_count} 个文档")
        
        with self.logger.timer("并发文档处理", 
                              file_count=self.total_count, 
                              max_workers=self.max_workers,
                              mode="多进程" if self.use_processes else "多线程"):
            
            if self.use_processes:
                results = self._process_with_multiprocessing(file_paths, save_results, progress_callback)
            else:
                results = self._process_with_threading(file_paths, save_results, progress_callback)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        
        self.logger.info(f"并发处理完成: 成功 {success_count}, 失败 {failed_count}")
        
        return results
    
    def _process_with_threading(self, file_paths: List[str], 
                               save_results: bool,
                               progress_callback: Optional[Callable]) -> List[Dict[str, Any]]:
        """使用多线程处理"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_path = {
                executor.submit(self._process_single_document, path, save_results): path 
                for path in file_paths
            }
            
            # 收集结果
            for future in as_completed(future_to_path):
                file_path = future_to_path[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 更新进度
                    with self.progress_lock:
                        self.completed_count += 1
                        if progress_callback:
                            progress_callback(self.completed_count, self.total_count, file_path)
                        else:
                            self._default_progress_callback(self.completed_count, self.total_count, file_path)
                
                except Exception as e:
                    self.logger.error(f"处理文档失败 {file_path}: {e}")
                    results.append({
                        'file_path': file_path,
                        'success': False,
                        'error': str(e),
                        'chunks': [],
                        'processing_time': 0
                    })
        
        return results
    
    def _process_with_multiprocessing(self, file_paths: List[str], 
                                    save_results: bool,
                                    progress_callback: Optional[Callable]) -> List[Dict[str, Any]]:
        """使用多进程处理"""
        results = []
        
        # 多进程需要特殊处理，因为不能直接传递复杂对象
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_path = {
                executor.submit(_process_document_worker, path, save_results): path 
                for path in file_paths
            }
            
            # 收集结果
            for future in as_completed(future_to_path):
                file_path = future_to_path[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 更新进度
                    with self.progress_lock:
                        self.completed_count += 1
                        if progress_callback:
                            progress_callback(self.completed_count, self.total_count, file_path)
                        else:
                            self._default_progress_callback(self.completed_count, self.total_count, file_path)
                
                except Exception as e:
                    self.logger.error(f"处理文档失败 {file_path}: {e}")
                    results.append({
                        'file_path': file_path,
                        'success': False,
                        'error': str(e),
                        'chunks': [],
                        'processing_time': 0
                    })
        
        return results
    
    def _process_single_document(self, file_path: str, save_results: bool) -> Dict[str, Any]:
        """处理单个文档"""
        start_time = time.time()
        
        try:
            processor = SimplifiedDocumentProcessor()
            chunks = processor.process_file(file_path, save_results=save_results)
            
            processing_time = time.time() - start_time
            
            return {
                'file_path': file_path,
                'success': True,
                'chunks': chunks,
                'chunk_count': len(chunks),
                'processing_time': processing_time,
                'error': None
            }
        
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'file_path': file_path,
                'success': False,
                'chunks': [],
                'chunk_count': 0,
                'processing_time': processing_time,
                'error': str(e)
            }
    
    def _default_progress_callback(self, completed: int, total: int, current_file: str):
        """默认进度回调"""
        progress = (completed / total) * 100
        file_name = Path(current_file).name
        print(f"📊 进度: {completed}/{total} ({progress:.1f}%) - 完成: {file_name}")
    
    def process_directory_concurrent(self, directory: str, 
                                   recursive: bool = True,
                                   save_results: bool = True,
                                   file_pattern: str = "*") -> Dict[str, Any]:
        """
        并发处理目录中的所有文档
        
        Args:
            directory: 目录路径
            recursive: 是否递归处理子目录
            save_results: 是否保存结果
            file_pattern: 文件匹配模式
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        directory = Path(directory)
        if not directory.exists():
            self.logger.error(f"目录不存在: {directory}")
            return {"success": 0, "failed": 0, "total_time": 0, "files": []}
        
        # 收集文件
        from config.config import SUPPORTED_EXTENSIONS
        supported_exts = []
        for exts in SUPPORTED_EXTENSIONS.values():
            supported_exts.extend(exts)
        
        files = []
        if recursive:
            for ext in supported_exts:
                files.extend(directory.rglob(f"*{ext}"))
        else:
            for ext in supported_exts:
                files.extend(directory.glob(f"*{ext}"))
        
        file_paths = [str(f) for f in files if f.is_file()]
        
        if not file_paths:
            self.logger.warning(f"目录中没有找到支持的文档: {directory}")
            return {"success": 0, "failed": 0, "total_time": 0, "files": []}
        
        self.logger.info(f"找到 {len(file_paths)} 个文档，开始并发处理")
        
        # 并发处理
        start_time = time.time()
        results = self.process_documents_concurrent(file_paths, save_results)
        total_time = time.time() - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        total_chunks = sum(r['chunk_count'] for r in results)
        
        summary = {
            "success": success_count,
            "failed": failed_count,
            "total_files": len(file_paths),
            "total_chunks": total_chunks,
            "total_time": total_time,
            "avg_time_per_file": total_time / len(file_paths) if file_paths else 0,
            "files": results
        }
        
        self.logger.info(f"目录处理完成: 成功 {success_count}, 失败 {failed_count}, "
                        f"总耗时 {total_time:.2f}秒, 平均 {summary['avg_time_per_file']:.2f}秒/文件")
        
        return summary


def _process_document_worker(file_path: str, save_results: bool) -> Dict[str, Any]:
    """
    多进程工作函数
    注意：这个函数必须在模块级别定义，以便pickle序列化
    """
    start_time = time.time()
    
    try:
        # 在工作进程中创建处理器
        processor = SimplifiedDocumentProcessor()
        chunks = processor.process_file(file_path, save_results=save_results)
        
        processing_time = time.time() - start_time
        
        return {
            'file_path': file_path,
            'success': True,
            'chunks': chunks,
            'chunk_count': len(chunks),
            'processing_time': processing_time,
            'error': None
        }
    
    except Exception as e:
        processing_time = time.time() - start_time
        return {
            'file_path': file_path,
            'success': False,
            'chunks': [],
            'chunk_count': 0,
            'processing_time': processing_time,
            'error': str(e)
        }
