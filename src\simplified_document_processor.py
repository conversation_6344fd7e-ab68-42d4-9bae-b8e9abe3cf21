#!/usr/bin/env python3
"""
简化文档处理器
统一转换为Markdown -> 简繁转换 -> 向量化存储
"""

import os
from typing import List, Dict, Any
from pathlib import Path
from docx import Document
import markdown
import fitz  # PyMuPDF

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

from config.config import SUPPORTED_EXTENSIONS
from .parsing_result_saver import get_result_saver
from .content_type_detector import ContentTypeDetector
from .simple_ocr_processor import SimpleOCRProcessor


class SimplifiedDocumentProcessor:
    """简化文档处理器"""
    
    def __init__(self):
        """初始化工程化文档处理器"""
        self.result_saver = get_result_saver()
        self.content_detector = ContentTypeDetector()
        self.ocr_processor = SimpleOCRProcessor(use_gpu=True)

        print("✓ 工程化文档处理器初始化成功（内容检测 + OCR + Markdown转换）")
    
    def process_file(self, file_path: str, save_results: bool = True) -> List[Dict[str, Any]]:
        """
        处理单个文件的简化流程
        
        Args:
            file_path: 文件路径
            save_results: 是否保存解析结果
            
        Returns:
            List[Dict[str, Any]]: 处理后的文档片段列表
        """
        file_path = Path(file_path) if not isinstance(file_path, Path) else file_path
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 生成文件ID用于保存结果
        file_id = None
        processing_log = []
        
        if save_results:
            file_id = self.result_saver.generate_file_id(str(file_path))
            processing_log.append(f"开始处理文件: {file_path}")
            processing_log.append(f"生成文件ID: {file_id}")

        # 获取文件类型
        file_type = self._get_file_type(file_path)
        processing_log.append(f"文件类型: {file_type}")
        
        try:
            # 步骤1: 检测内容类型
            print(f"🔍 步骤1: 检测文件内容类型...")
            content_type, type_info = self.content_detector.detect_content_type(str(file_path))
            self.content_detector.print_detection_result(content_type, type_info, str(file_path))
            processing_log.append(f"内容类型检测: {content_type} - {type_info.get('reason', '')}")

            # 步骤2: 根据内容类型处理文件
            print(f"📄 步骤2: 根据内容类型处理文件...")
            markdown_content = self._process_by_content_type(file_path, file_type, content_type, type_info)
            processing_log.append(f"内容处理完成，长度: {len(markdown_content)} 字符")

            # 保存处理后的Markdown
            if save_results and file_id:
                self.result_saver.save_raw_content(file_id, f"{content_type}_markdown", markdown_content)

            # 步骤3: 创建文档片段
            print(f"📊 步骤3: 创建文档片段...")
            chunks = self._create_chunks(markdown_content, file_path, content_type)
            processing_log.append(f"创建文档片段完成，生成 {len(chunks)} 个片段")
            
            # 保存最终结果
            if save_results and file_id:
                # 保存最终片段
                self.result_saver.save_final_chunks(file_id, chunks)
                
                # 保存元数据
                metadata = {
                    'original_path': str(file_path),
                    'file_name': file_path.name,
                    'file_type': file_type,
                    'file_size': file_path.stat().st_size,
                    'processing_completed': True,
                    'total_chunks': len(chunks),
                    'markdown_length': len(markdown_content),
                    'content_type': content_type,
                    'content_type_info': type_info
                }
                self.result_saver.save_parsing_metadata(file_id, metadata)
                
                # 保存处理日志
                self.result_saver.save_processing_log(file_id, processing_log)
                
                print(f"✅ 解析结果已保存，文件ID: {file_id}")
            
            return chunks
            
        except Exception as e:
            processing_log.append(f"处理失败: {e}")
            if save_results and file_id:
                self.result_saver.save_processing_log(file_id, processing_log)
            raise e
    
    def _get_file_type(self, file_path: Path) -> str:
        """根据文件扩展名判断文件类型"""
        suffix = file_path.suffix.lower()
        for file_type, extensions in SUPPORTED_EXTENSIONS.items():
            if suffix in extensions:
                return file_type
        raise ValueError(f"不支持的文件类型: {suffix}")

    def _process_by_content_type(self, file_path: Path, file_type: str,
                               content_type: str, type_info: Dict[str, Any]) -> str:
        """
        根据内容类型处理文件

        Args:
            file_path: 文件路径
            file_type: 文件类型
            content_type: 内容类型 ('text', 'scan', 'mixed')
            type_info: 类型检测详细信息

        Returns:
            str: Markdown内容
        """
        if content_type == 'text':
            # 纯文本内容，使用原有的文本提取方法
            print("   📝 使用文本提取模式")
            return self._convert_to_markdown(file_path, file_type)

        elif content_type == 'scan':
            # 扫描版内容，使用OCR识别
            print("   🖼️  使用OCR识别模式")
            return self._convert_with_ocr(file_path, file_type)

        elif content_type == 'mixed':
            # 混合内容，先文本提取再OCR
            print("   🔀 使用混合处理模式（文本提取 + OCR识别）")
            return self._convert_mixed_content(file_path, file_type, type_info)

        else:
            # 未知类型，默认使用文本提取
            print("   ❓ 未知内容类型，使用默认文本提取模式")
            return self._convert_to_markdown(file_path, file_type)

    def _convert_with_ocr(self, file_path: Path, file_type: str) -> str:
        """
        使用OCR识别转换文件

        Args:
            file_path: 文件路径
            file_type: 文件类型

        Returns:
            str: Markdown内容
        """
        if not self.ocr_processor.is_available():
            print("   ❌ OCR不可用，回退到文本提取模式")
            return self._convert_to_markdown(file_path, file_type)

        if file_type == "pdf":
            # PDF OCR处理
            ocr_results = self.ocr_processor.extract_text_from_pdf_images(str(file_path))
            return self.ocr_processor.format_ocr_results_to_markdown(ocr_results, file_path.name)

        elif file_type in ["image", "png", "jpg", "jpeg"]:
            # 图片OCR处理
            ocr_result = self.ocr_processor.extract_text_from_image(str(file_path))
            return self.ocr_processor.format_ocr_results_to_markdown([ocr_result], file_path.name)

        else:
            # 其他类型回退到文本提取
            print(f"   ⚠ 文件类型 {file_type} 不支持OCR，回退到文本提取")
            return self._convert_to_markdown(file_path, file_type)

    def _convert_mixed_content(self, file_path: Path, file_type: str,
                             type_info: Dict[str, Any]) -> str:
        """
        处理混合内容（文本 + OCR）

        Args:
            file_path: 文件路径
            file_type: 文件类型
            type_info: 类型检测信息

        Returns:
            str: 合并后的Markdown内容
        """
        markdown_parts = [f"# {file_path.stem} (混合内容处理)\n"]

        # 1. 先提取文本内容
        print("      📝 提取文本内容...")
        try:
            text_content = self._convert_to_markdown(file_path, file_type)
            if text_content and len(text_content.strip()) > 100:
                markdown_parts.append("## 文本内容\n")
                markdown_parts.append(text_content)
                markdown_parts.append("\n")
        except Exception as e:
            print(f"      ⚠ 文本提取失败: {e}")

        # 2. 再进行OCR识别
        print("      🖼️  OCR识别图片内容...")
        try:
            if self.ocr_processor.is_available():
                if file_type == "pdf":
                    ocr_results = self.ocr_processor.extract_text_from_pdf_images(str(file_path))
                    if ocr_results:
                        markdown_parts.append("## OCR识别内容\n")
                        ocr_markdown = self.ocr_processor.format_ocr_results_to_markdown(ocr_results, file_path.name)
                        # 移除OCR结果中的标题，避免重复
                        ocr_lines = ocr_markdown.split('\n')[1:]  # 跳过第一行标题
                        markdown_parts.append('\n'.join(ocr_lines))
            else:
                print("      ❌ OCR不可用，跳过OCR处理")
        except Exception as e:
            print(f"      ⚠ OCR处理失败: {e}")

        return "\n".join(markdown_parts)

    def _convert_to_markdown(self, file_path: Path, file_type: str) -> str:
        """
        将文档转换为Markdown格式
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            str: Markdown内容
        """
        if file_type == "markdown":
            # 已经是Markdown，直接读取
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        elif file_type == "text":
            # 纯文本转Markdown
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return f"# {file_path.stem}\n\n{content}"
        
        elif file_type == "word":
            # Word转Markdown
            return self._word_to_markdown(file_path)
        
        elif file_type == "pdf":
            # PDF转Markdown
            return self._pdf_to_markdown(file_path)
        
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
    
    def _word_to_markdown(self, file_path: Path) -> str:
        """Word文档转Markdown"""
        try:
            doc = Document(file_path)
            markdown_lines = [f"# {file_path.stem}\n"]
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 简单的样式转换
                    if paragraph.style.name.startswith('Heading'):
                        level = int(paragraph.style.name[-1]) if paragraph.style.name[-1].isdigit() else 1
                        markdown_lines.append(f"{'#' * level} {text}\n")
                    else:
                        markdown_lines.append(f"{text}\n")
            
            # 处理表格
            for table in doc.tables:
                markdown_lines.append("\n")
                for i, row in enumerate(table.rows):
                    cells = [cell.text.strip() for cell in row.cells]
                    markdown_lines.append("| " + " | ".join(cells) + " |")
                    
                    # 添加表头分隔符
                    if i == 0:
                        markdown_lines.append("| " + " | ".join(["---"] * len(cells)) + " |")
                
                markdown_lines.append("\n")
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            raise Exception(f"Word转Markdown失败: {e}")
    
    def _pdf_to_markdown(self, file_path: Path) -> str:
        """PDF转Markdown - 优化版，避免表格内容重复"""
        try:
            markdown_lines = [f"# {file_path.stem}\n"]

            if PDFPLUMBER_AVAILABLE:
                # 使用pdfplumber处理PDF
                with pdfplumber.open(file_path) as pdf:
                    for page_num, page in enumerate(pdf.pages, 1):
                        markdown_lines.append(f"\n## 第{page_num}页\n")

                        # 先提取表格
                        tables = page.extract_tables()
                        table_areas = []

                        if tables:
                            print(f"    📊 第{page_num}页发现 {len(tables)} 个表格")

                            for table_idx, table in enumerate(tables):
                                markdown_lines.append(f"\n### 表格 {table_idx + 1}\n")

                                # 转换为Markdown表格
                                for i, row in enumerate(table):
                                    if row and any(cell for cell in row if cell):
                                        # 清理单元格内容
                                        cells = []
                                        for cell in row:
                                            if cell:
                                                # 清理换行符和多余空格
                                                clean_cell = str(cell).replace('\n', ' ').strip()
                                                cells.append(clean_cell)
                                            else:
                                                cells.append("")

                                        markdown_lines.append("| " + " | ".join(cells) + " |")

                                        # 添加表头分隔符
                                        if i == 0:
                                            markdown_lines.append("| " + " | ".join(["---"] * len(cells)) + " |")

                                markdown_lines.append("\n")

                                # 记录表格区域（用于后续排除）
                                try:
                                    # 获取表格边界框
                                    table_bbox = page.within_bbox(page.bbox).extract_tables()[table_idx]
                                    if hasattr(table_bbox, 'bbox'):
                                        table_areas.append(table_bbox.bbox)
                                except:
                                    pass

                        # 提取非表格区域的文本
                        page_text = page.extract_text()
                        if page_text:
                            # 过滤掉表格数据行，只保留有意义的文本
                            lines = page_text.split('\n')
                            non_table_lines = []

                            for line in lines:
                                line = line.strip()
                                # 跳过明显的表格数据行和表格相关的格式行
                                if line and not self._is_table_related_line(line):
                                    non_table_lines.append(line)

                            # 只有当有有意义的非表格文本时才添加
                            meaningful_text = '\n'.join(non_table_lines).strip()
                            if meaningful_text and len(meaningful_text) > 50:  # 至少50个字符
                                markdown_lines.append("### 页面文本\n")
                                markdown_lines.append(meaningful_text)
                                markdown_lines.append("\n")
            else:
                # 使用PyMuPDF作为备选
                doc = fitz.open(str(file_path))
                for page_num in range(len(doc)):
                    page = doc[page_num]
                    markdown_lines.append(f"\n## 第{page_num + 1}页\n")

                    text = page.get_text()
                    if text:
                        markdown_lines.append(text)
                        markdown_lines.append("\n")

                doc.close()

            return "\n".join(markdown_lines)

        except Exception as e:
            raise Exception(f"PDF转Markdown失败: {e}")

    def _is_table_related_line(self, line: str) -> bool:
        """
        判断是否为表格相关行（包括数据行、标题行、格式行等）

        Args:
            line: 文本行

        Returns:
            bool: 是否为表格相关行
        """
        # 简单的启发式规则
        if not line.strip():
            return False

        import re

        # 包含大量数字和逗号的行（如：50,600,957,885.78）
        number_pattern = r'\d+[,，]\d+[,，]\d+'
        if re.search(number_pattern, line):
            return True

        # 包含百分比的行
        if '%' in line and re.search(r'\d+\.\d+', line):
            return True

        # 包含多个数字分隔的行
        numbers = re.findall(r'\d+\.?\d*', line)
        if len(numbers) >= 3:
            return True

        # 表格标题行模式
        table_headers = [
            '项目', '本报告期', '上年同期', '增减变动幅度',
            '本报告期末', '上年度末', '单位：元', '币种：人民币'
        ]
        if any(header in line for header in table_headers):
            return True

        # 财务指标名称行
        financial_terms = [
            '营业收入', '净利润', '归属于上市公司股东', '经营活动产生的现金流量',
            '基本每股收益', '稀释每股收益', '加权平均净资产收益率', '总资产',
            '扣除非经常性损益'
        ]
        if any(term in line for term in financial_terms):
            return True

        # 页码行
        if re.match(r'^\d+\s*/\s*\d+$', line.strip()):
            return True

        return False
    
    def _create_chunks(self, content: str, file_path: Path, content_type: str = 'text') -> List[Dict[str, Any]]:
        """
        创建文档片段

        Args:
            content: Markdown内容
            file_path: 文件路径
            content_type: 内容类型

        Returns:
            List[Dict[str, Any]]: 文档片段列表
        """
        chunks = []
        
        # 按段落分割内容
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 50:  # 只保留有意义的段落
                chunk = {
                    "content": paragraph,
                    "source": str(file_path),
                    "type": "text",
                    "chunk_id": i,
                    "processing_method": f"{content_type}_processing",
                    "content_type": content_type
                }
                chunks.append(chunk)
        
        # 如果没有有效段落，创建一个完整文档片段
        if not chunks and content.strip():
            chunk = {
                "content": content,
                "source": str(file_path),
                "type": "document",
                "chunk_id": 0,
                "processing_method": f"{content_type}_processing",
                "content_type": content_type
            }
            chunks.append(chunk)
        
        return chunks
