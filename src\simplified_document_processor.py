#!/usr/bin/env python3
"""
简化文档处理器
统一转换为Markdown -> 简繁转换 -> 向量化存储
"""

import os
from typing import List, Dict, Any
from pathlib import Path
from docx import Document
import markdown
import fitz  # PyMuPDF

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

from config.config import SUPPORTED_EXTENSIONS
from .text_converter import TextConverter
from .parsing_result_saver import get_result_saver


class SimplifiedDocumentProcessor:
    """简化文档处理器"""
    
    def __init__(self):
        """初始化简化文档处理器"""
        self.text_converter = TextConverter()
        self.result_saver = get_result_saver()
        
        print("✓ 简化文档处理器初始化成功（Markdown转换 + 简繁转换）")
    
    def process_file(self, file_path: str, save_results: bool = True) -> List[Dict[str, Any]]:
        """
        处理单个文件的简化流程
        
        Args:
            file_path: 文件路径
            save_results: 是否保存解析结果
            
        Returns:
            List[Dict[str, Any]]: 处理后的文档片段列表
        """
        file_path = Path(file_path) if not isinstance(file_path, Path) else file_path
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 生成文件ID用于保存结果
        file_id = None
        processing_log = []
        
        if save_results:
            file_id = self.result_saver.generate_file_id(str(file_path))
            processing_log.append(f"开始处理文件: {file_path}")
            processing_log.append(f"生成文件ID: {file_id}")

        # 获取文件类型
        file_type = self._get_file_type(file_path)
        processing_log.append(f"文件类型: {file_type}")
        
        try:
            # 步骤1: 转换为Markdown
            print(f"📄 步骤1: 转换为Markdown格式...")
            markdown_content = self._convert_to_markdown(file_path, file_type)
            processing_log.append(f"转换为Markdown完成，长度: {len(markdown_content)} 字符")
            
            # 保存原始Markdown
            if save_results and file_id:
                self.result_saver.save_raw_content(file_id, "original_markdown", markdown_content)
            
            # 步骤2: 简繁转换
            print(f"🔄 步骤2: 简繁体转换...")
            simplified_content = self.text_converter.convert_traditional_to_simplified(markdown_content)
            has_conversion = markdown_content != simplified_content
            processing_log.append(f"简繁转换完成，是否有转换: {has_conversion}")
            
            # 保存简体Markdown
            if save_results and file_id:
                self.result_saver.save_raw_content(file_id, "simplified_markdown", simplified_content)
            
            # 步骤3: 创建文档片段
            print(f"📊 步骤3: 创建文档片段...")
            chunks = self._create_chunks(simplified_content, file_path, has_conversion, markdown_content)
            processing_log.append(f"创建文档片段完成，生成 {len(chunks)} 个片段")
            
            # 保存最终结果
            if save_results and file_id:
                # 保存最终片段
                self.result_saver.save_final_chunks(file_id, chunks)
                
                # 保存元数据
                metadata = {
                    'original_path': str(file_path),
                    'file_name': file_path.name,
                    'file_type': file_type,
                    'file_size': file_path.stat().st_size,
                    'processing_completed': True,
                    'total_chunks': len(chunks),
                    'has_traditional_conversion': has_conversion,
                    'markdown_length': len(markdown_content),
                    'simplified_length': len(simplified_content)
                }
                self.result_saver.save_parsing_metadata(file_id, metadata)
                
                # 保存处理日志
                self.result_saver.save_processing_log(file_id, processing_log)
                
                print(f"✅ 解析结果已保存，文件ID: {file_id}")
            
            return chunks
            
        except Exception as e:
            processing_log.append(f"处理失败: {e}")
            if save_results and file_id:
                self.result_saver.save_processing_log(file_id, processing_log)
            raise e
    
    def _get_file_type(self, file_path: Path) -> str:
        """根据文件扩展名判断文件类型"""
        suffix = file_path.suffix.lower()
        for file_type, extensions in SUPPORTED_EXTENSIONS.items():
            if suffix in extensions:
                return file_type
        raise ValueError(f"不支持的文件类型: {suffix}")
    
    def _convert_to_markdown(self, file_path: Path, file_type: str) -> str:
        """
        将文档转换为Markdown格式
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            str: Markdown内容
        """
        if file_type == "markdown":
            # 已经是Markdown，直接读取
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        elif file_type == "text":
            # 纯文本转Markdown
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return f"# {file_path.stem}\n\n{content}"
        
        elif file_type == "word":
            # Word转Markdown
            return self._word_to_markdown(file_path)
        
        elif file_type == "pdf":
            # PDF转Markdown
            return self._pdf_to_markdown(file_path)
        
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
    
    def _word_to_markdown(self, file_path: Path) -> str:
        """Word文档转Markdown"""
        try:
            doc = Document(file_path)
            markdown_lines = [f"# {file_path.stem}\n"]
            
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 简单的样式转换
                    if paragraph.style.name.startswith('Heading'):
                        level = int(paragraph.style.name[-1]) if paragraph.style.name[-1].isdigit() else 1
                        markdown_lines.append(f"{'#' * level} {text}\n")
                    else:
                        markdown_lines.append(f"{text}\n")
            
            # 处理表格
            for table in doc.tables:
                markdown_lines.append("\n")
                for i, row in enumerate(table.rows):
                    cells = [cell.text.strip() for cell in row.cells]
                    markdown_lines.append("| " + " | ".join(cells) + " |")
                    
                    # 添加表头分隔符
                    if i == 0:
                        markdown_lines.append("| " + " | ".join(["---"] * len(cells)) + " |")
                
                markdown_lines.append("\n")
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            raise Exception(f"Word转Markdown失败: {e}")
    
    def _pdf_to_markdown(self, file_path: Path) -> str:
        """PDF转Markdown"""
        try:
            markdown_lines = [f"# {file_path.stem}\n"]
            
            if PDFPLUMBER_AVAILABLE:
                # 使用pdfplumber处理PDF
                with pdfplumber.open(file_path) as pdf:
                    for page_num, page in enumerate(pdf.pages, 1):
                        markdown_lines.append(f"\n## 第{page_num}页\n")
                        
                        # 提取文本
                        text = page.extract_text()
                        if text:
                            markdown_lines.append(text)
                        
                        # 提取表格
                        tables = page.extract_tables()
                        for table in tables:
                            markdown_lines.append("\n")
                            for i, row in enumerate(table):
                                if row and any(cell for cell in row if cell):
                                    cells = [str(cell).strip() if cell else "" for cell in row]
                                    markdown_lines.append("| " + " | ".join(cells) + " |")
                                    
                                    # 添加表头分隔符
                                    if i == 0:
                                        markdown_lines.append("| " + " | ".join(["---"] * len(cells)) + " |")
                            
                            markdown_lines.append("\n")
            else:
                # 使用PyMuPDF作为备选
                doc = fitz.open(str(file_path))
                for page_num in range(len(doc)):
                    page = doc[page_num]
                    markdown_lines.append(f"\n## 第{page_num + 1}页\n")
                    
                    text = page.get_text()
                    if text:
                        markdown_lines.append(text)
                
                doc.close()
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            raise Exception(f"PDF转Markdown失败: {e}")
    
    def _create_chunks(self, content: str, file_path: Path, 
                      has_conversion: bool, original_content: str = None) -> List[Dict[str, Any]]:
        """
        创建文档片段
        
        Args:
            content: 简体中文内容
            file_path: 文件路径
            has_conversion: 是否进行了繁简转换
            original_content: 原始内容（如果有转换）
            
        Returns:
            List[Dict[str, Any]]: 文档片段列表
        """
        chunks = []
        
        # 按段落分割内容
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 50:  # 只保留有意义的段落
                chunk = {
                    "content": paragraph,
                    "source": str(file_path),
                    "type": "text",
                    "chunk_id": i,
                    "traditional_converted": has_conversion,
                    "original_content": original_content if has_conversion else None,
                    "processing_method": "simplified_markdown"
                }
                chunks.append(chunk)
        
        # 如果没有有效段落，创建一个完整文档片段
        if not chunks and content.strip():
            chunk = {
                "content": content,
                "source": str(file_path),
                "type": "document",
                "chunk_id": 0,
                "traditional_converted": has_conversion,
                "original_content": original_content if has_conversion else None,
                "processing_method": "simplified_markdown"
            }
            chunks.append(chunk)
        
        return chunks
