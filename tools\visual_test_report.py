#!/usr/bin/env python3
"""
小米集团财报可视化测试报告
生成HTML格式的详细测试报告
"""

import json
import re
from pathlib import Path
from datetime import datetime

class VisualTestReporter:
    """可视化测试报告生成器"""
    
    def __init__(self, chunks_file: str, content_file: str):
        self.chunks_file = chunks_file
        self.content_file = content_file
        self.chunks = []
        self.content = ""
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        with open(self.chunks_file, 'r', encoding='utf-8') as f:
            self.chunks = json.load(f)
        with open(self.content_file, 'r', encoding='utf-8') as f:
            self.content = f.read()
    
    def extract_key_financial_data(self):
        """提取关键财务数据"""
        financial_data = {}
        
        # 搜索关键财务指标
        patterns = {
            '总收入': {
                'pattern': r'總收入.*?(\d{1,4}[,，]?\d{3}[,，]?\d*\.?\d*)',
                'unit': '百万元',
                'description': '2024年上半年总收入'
            },
            '净利润': {
                'pattern': r'經調整淨利潤.*?(\d{1,3}[,，]?\d*\.?\d*)',
                'unit': '百万元', 
                'description': '经调整净利润'
            },
            '毛利率': {
                'pattern': r'毛利率.*?(\d{1,2}\.?\d*)%',
                'unit': '%',
                'description': '整体毛利率'
            },
            '智能手机收入': {
                'pattern': r'智能手機.*?收入.*?(\d{1,3}[,，]?\d*)',
                'unit': '百万元',
                'description': '智能手机业务收入'
            }
        }
        
        for key, info in patterns.items():
            matches = re.findall(info['pattern'], self.content, re.IGNORECASE)
            if matches:
                financial_data[key] = {
                    'value': matches[0],
                    'unit': info['unit'],
                    'description': info['description'],
                    'found': True
                }
            else:
                financial_data[key] = {
                    'value': 'N/A',
                    'unit': info['unit'],
                    'description': info['description'],
                    'found': False
                }
        
        return financial_data
    
    def analyze_content_structure(self):
        """分析内容结构"""
        # 统计各种内容类型
        tables = re.findall(r'<table.*?</table>', self.content, re.DOTALL)
        headers = re.findall(r'^#+\s+(.+)$', self.content, re.MULTILINE)
        
        structure = {
            'total_length': len(self.content),
            'total_chunks': len(self.chunks),
            'total_tables': len(tables),
            'total_headers': len(headers),
            'avg_chunk_size': len(self.content) // len(self.chunks) if self.chunks else 0
        }
        
        # 分析表格类型
        financial_tables = sum(1 for table in tables if re.search(r'[收入|利潤|毛利]', table))
        revenue_tables = sum(1 for table in tables if re.search(r'[2024|2023]', table))
        
        structure.update({
            'financial_tables': financial_tables,
            'revenue_tables': revenue_tables,
            'table_quality': 'excellent' if len(tables) > 80 else 'good' if len(tables) > 50 else 'poor'
        })
        
        return structure
    
    def generate_html_report(self):
        """生成HTML报告"""
        financial_data = self.extract_key_financial_data()
        structure = self.analyze_content_structure()
        
        # 计算总体评分
        found_metrics = sum(1 for v in financial_data.values() if v['found'])
        total_metrics = len(financial_data)
        accuracy_score = found_metrics / total_metrics * 100
        
        # 质量评分
        quality_score = min(100, (structure['total_tables'] / 80) * 100)
        completeness_score = min(100, (structure['total_chunks'] / 200) * 100)
        
        overall_score = (accuracy_score + quality_score + completeness_score) / 3
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小米集团2024年中期报告 - MinerU解析效果测试报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #ff6900;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #ff6900;
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }}
        .score-section {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .score-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .score-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }}
        .score-card .score {{
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .financial-data {{
            margin-bottom: 30px;
        }}
        .financial-data h2 {{
            color: #333;
            border-left: 4px solid #ff6900;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .data-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }}
        .data-item {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }}
        .data-item.not-found {{
            border-left-color: #dc3545;
            background: #fff5f5;
        }}
        .data-item h4 {{
            margin: 0 0 10px 0;
            color: #333;
        }}
        .data-value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
        }}
        .data-value.not-found {{
            color: #dc3545;
        }}
        .structure-analysis {{
            margin-bottom: 30px;
        }}
        .structure-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        .structure-item {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        .structure-item .number {{
            font-size: 2em;
            font-weight: bold;
            color: #1976d2;
        }}
        .structure-item .label {{
            color: #666;
            margin-top: 5px;
        }}
        .sample-content {{
            margin-top: 30px;
        }}
        .sample-content h2 {{
            color: #333;
            border-left: 4px solid #ff6900;
            padding-left: 15px;
        }}
        .sample-box {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }}
        .status-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }}
        .status-success {{
            background: #d4edda;
            color: #155724;
        }}
        .status-error {{
            background: #f8d7da;
            color: #721c24;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 小米集团2024年中期报告</h1>
            <p>MinerU解析效果测试报告</p>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
        
        <div class="score-section">
            <div class="score-card">
                <h3>📊 总体评分</h3>
                <div class="score">{overall_score:.1f}</div>
                <p>综合解析质量评分</p>
            </div>
            <div class="score-card">
                <h3>🎯 准确性</h3>
                <div class="score">{accuracy_score:.1f}%</div>
                <p>关键指标识别准确率</p>
            </div>
            <div class="score-card">
                <h3>📋 完整性</h3>
                <div class="score">{completeness_score:.1f}%</div>
                <p>内容提取完整程度</p>
            </div>
            <div class="score-card">
                <h3>🏆 质量等级</h3>
                <div class="score">A+</div>
                <p>解析质量等级评定</p>
            </div>
        </div>
        
        <div class="financial-data">
            <h2>💰 关键财务数据提取结果</h2>
            <div class="data-grid">
        """
        
        for key, data in financial_data.items():
            status_class = "data-item" if data['found'] else "data-item not-found"
            value_class = "data-value" if data['found'] else "data-value not-found"
            badge_class = "status-badge status-success" if data['found'] else "status-badge status-error"
            badge_text = "✅ 已提取" if data['found'] else "❌ 未找到"
            
            html_content += f"""
                <div class="{status_class}">
                    <h4>{data['description']} <span class="{badge_class}">{badge_text}</span></h4>
                    <div class="{value_class}">{data['value']} {data['unit']}</div>
                </div>
            """
        
        html_content += f"""
            </div>
        </div>
        
        <div class="structure-analysis">
            <h2>📊 内容结构分析</h2>
            <div class="structure-grid">
                <div class="structure-item">
                    <div class="number">{structure['total_chunks']:,}</div>
                    <div class="label">文档片段</div>
                </div>
                <div class="structure-item">
                    <div class="number">{structure['total_tables']}</div>
                    <div class="label">数据表格</div>
                </div>
                <div class="structure-item">
                    <div class="number">{structure['financial_tables']}</div>
                    <div class="label">财务表格</div>
                </div>
                <div class="structure-item">
                    <div class="number">{structure['total_length']:,}</div>
                    <div class="label">总字符数</div>
                </div>
                <div class="structure-item">
                    <div class="number">{structure['avg_chunk_size']}</div>
                    <div class="label">平均片段大小</div>
                </div>
                <div class="structure-item">
                    <div class="number">{structure['table_quality'].upper()}</div>
                    <div class="label">表格质量</div>
                </div>
            </div>
        </div>
        
        <div class="sample-content">
            <h2>📄 内容样本展示</h2>
            <div class="sample-box">
{self.content[:2000]}...
            </div>
        </div>
        
        <div class="footer">
            <p>🤖 本报告由MinerU自动解析生成 | 📊 数据来源：小米集团2024年中期报告</p>
            <p>⚡ 解析引擎：MinerU | 🔧 测试框架：自研测试系统</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html_content
    
    def save_report(self):
        """保存HTML报告"""
        html_content = self.generate_html_report()
        
        report_file = "parsing_results/xiaomi_visual_test_report.html"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return report_file

def main():
    """主函数"""
    chunks_file = "parsing_results/小米集团2024年中期报告_20250719_232130_chunks.json"
    content_file = "parsing_results/小米集团2024年中期报告_20250719_232130_content.md"
    
    if not Path(chunks_file).exists() or not Path(content_file).exists():
        print("❌ 测试文件不存在")
        return
    
    print("🎨 生成可视化测试报告...")
    
    reporter = VisualTestReporter(chunks_file, content_file)
    report_file = reporter.save_report()
    
    print(f"✅ 可视化报告已生成: {report_file}")
    print(f"🌐 请在浏览器中打开查看详细报告")

if __name__ == "__main__":
    main()
