#!/usr/bin/env python3
"""
小米集团财报解析效果测试脚本
测试MinerU解析结果的准确性和完整性
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

class XiaomiReportTester:
    """小米集团财报测试器"""
    
    def __init__(self, chunks_file: str, content_file: str):
        """
        初始化测试器
        
        Args:
            chunks_file: 片段JSON文件路径
            content_file: 内容Markdown文件路径
        """
        self.chunks_file = chunks_file
        self.content_file = content_file
        self.chunks = []
        self.content = ""
        self.load_data()
    
    def load_data(self):
        """加载测试数据"""
        # 加载片段数据
        with open(self.chunks_file, 'r', encoding='utf-8') as f:
            self.chunks = json.load(f)
        
        # 加载内容数据
        with open(self.content_file, 'r', encoding='utf-8') as f:
            self.content = f.read()
        
        print(f"✅ 加载数据完成:")
        print(f"   📊 片段数量: {len(self.chunks)}")
        print(f"   📝 内容长度: {len(self.content):,} 字符")
    
    def test_key_financial_metrics(self) -> Dict[str, Any]:
        """测试关键财务指标提取"""
        print("\n🔍 测试1: 关键财务指标提取")
        
        # 预期的关键财务数据
        expected_metrics = {
            '总收入': ['1,644', '1644', '164,394.7', '164394.7'],
            '净利润': ['127', '12,666.4', '12666.4'],
            '毛利率': ['21.4%', '21.4'],
            '智能手机收入': ['930', '92,996', '92996'],
            '智能电动汽车收入': ['64', '62', '6,394.9', '6394.9'],
            '互联网服务收入': ['163', '16,314', '16314'],
            '同比增长': ['29.6%', '51.3%', '24.6%']
        }
        
        results = {}
        for metric, patterns in expected_metrics.items():
            found = False
            found_values = []
            
            for pattern in patterns:
                # 在内容中搜索
                if pattern in self.content:
                    found = True
                    found_values.append(pattern)
            
            results[metric] = {
                'found': found,
                'values': found_values,
                'status': '✅' if found else '❌'
            }
            
            print(f"   {results[metric]['status']} {metric}: {found_values if found else '未找到'}")
        
        return results
    
    def test_business_segments(self) -> Dict[str, Any]:
        """测试业务分部信息提取"""
        print("\n🔍 测试2: 业务分部信息提取")
        
        segments = {
            '智能手机': ['智能手机', '手机', 'smartphone'],
            'IoT与生活消费产品': ['IoT', '生活消费产品', '智能大家电'],
            '互联网服务': ['互联网服务', '广告业务', '游戏业务'],
            '智能电动汽车': ['智能电动汽车', 'Xiaomi SU7', '电动汽车']
        }
        
        results = {}
        for segment, keywords in segments.items():
            found_keywords = []
            for keyword in keywords:
                if keyword in self.content:
                    found_keywords.append(keyword)
            
            results[segment] = {
                'found_keywords': found_keywords,
                'coverage': len(found_keywords) / len(keywords),
                'status': '✅' if found_keywords else '❌'
            }
            
            print(f"   {results[segment]['status']} {segment}: {len(found_keywords)}/{len(keywords)} 关键词")
        
        return results
    
    def test_table_extraction(self) -> Dict[str, Any]:
        """测试表格提取效果"""
        print("\n🔍 测试3: 表格提取效果")
        
        # 统计HTML表格
        html_tables = re.findall(r'<table.*?</table>', self.content, re.DOTALL)
        
        # 分析表格内容
        financial_tables = 0
        revenue_tables = 0
        segment_tables = 0
        
        for table in html_tables:
            if any(keyword in table for keyword in ['收入', '毛利', '利润']):
                financial_tables += 1
            if '分部' in table or '业务' in table:
                segment_tables += 1
            if any(keyword in table for keyword in ['2024年', '2023年', '同比']):
                revenue_tables += 1
        
        results = {
            'total_tables': len(html_tables),
            'financial_tables': financial_tables,
            'segment_tables': segment_tables,
            'revenue_tables': revenue_tables,
            'table_quality': 'good' if len(html_tables) > 50 else 'poor'
        }
        
        print(f"   📊 总表格数: {results['total_tables']}")
        print(f"   💰 财务表格: {results['financial_tables']}")
        print(f"   🏢 分部表格: {results['segment_tables']}")
        print(f"   📈 收入表格: {results['revenue_tables']}")
        print(f"   ✅ 表格质量: {results['table_quality']}")
        
        return results
    
    def test_chunk_quality(self) -> Dict[str, Any]:
        """测试片段质量"""
        print("\n🔍 测试4: 片段质量分析")
        
        # 分析片段统计
        total_chunks = len(self.chunks)
        char_counts = [chunk['metadata']['char_count'] for chunk in self.chunks]
        word_counts = [chunk['metadata']['word_count'] for chunk in self.chunks]
        
        # 统计不同类型的片段
        financial_chunks = 0
        table_chunks = 0
        text_chunks = 0
        
        for chunk in self.chunks:
            content = chunk['content']
            if any(keyword in content for keyword in ['收入', '利润', '毛利', '亿元']):
                financial_chunks += 1
            if '<table' in content:
                table_chunks += 1
            if content.startswith('#'):
                text_chunks += 1
        
        results = {
            'total_chunks': total_chunks,
            'financial_chunks': financial_chunks,
            'table_chunks': table_chunks,
            'text_chunks': text_chunks,
            'avg_char_count': sum(char_counts) / len(char_counts),
            'avg_word_count': sum(word_counts) / len(word_counts),
            'quality_score': (financial_chunks + table_chunks) / total_chunks
        }
        
        print(f"   📊 总片段数: {results['total_chunks']}")
        print(f"   💰 财务片段: {results['financial_chunks']}")
        print(f"   📋 表格片段: {results['table_chunks']}")
        print(f"   📝 文本片段: {results['text_chunks']}")
        print(f"   📏 平均字符数: {results['avg_char_count']:.1f}")
        print(f"   📊 质量评分: {results['quality_score']:.2f}")
        
        return results
    
    def test_specific_data_points(self) -> Dict[str, Any]:
        """测试特定数据点的准确性"""
        print("\n🔍 测试5: 特定数据点验证")
        
        # 关键数据点验证
        data_points = {
            '2024年上半年总收入': {
                'expected': '1,644亿元',
                'patterns': ['1,644', '1644', '164,394.7'],
                'context': '2024年上半年'
            },
            '智能手机出货量': {
                'expected': '82.8百万台',
                'patterns': ['82.8', '82.8百万台'],
                'context': '智能手机出货量'
            },
            'Xiaomi SU7交付量': {
                'expected': '27,367辆',
                'patterns': ['27,367', '27367'],
                'context': 'SU7'
            },
            '经调整净利润增长': {
                'expected': '51.3%',
                'patterns': ['51.3%', '51.3'],
                'context': '经调整净利润'
            }
        }
        
        results = {}
        for data_point, info in data_points.items():
            found = False
            found_context = []
            
            for pattern in info['patterns']:
                # 查找包含该数据和上下文的片段
                for chunk in self.chunks:
                    content = chunk['content']
                    if pattern in content and info['context'] in content:
                        found = True
                        found_context.append(content[:200] + '...')
                        break
            
            results[data_point] = {
                'expected': info['expected'],
                'found': found,
                'context': found_context[:1],  # 只保留第一个匹配的上下文
                'status': '✅' if found else '❌'
            }
            
            print(f"   {results[data_point]['status']} {data_point}: {info['expected']}")
        
        return results
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成完整测试报告"""
        print("🧪 开始小米集团财报解析效果测试")
        print("=" * 60)
        
        # 执行所有测试
        metrics_test = self.test_key_financial_metrics()
        segments_test = self.test_business_segments()
        tables_test = self.test_table_extraction()
        chunks_test = self.test_chunk_quality()
        data_points_test = self.test_specific_data_points()
        
        # 计算总体评分
        metrics_score = sum(1 for r in metrics_test.values() if r['found']) / len(metrics_test)
        segments_score = sum(r['coverage'] for r in segments_test.values()) / len(segments_test)
        tables_score = 1.0 if tables_test['total_tables'] > 50 else 0.5
        chunks_score = chunks_test['quality_score']
        data_points_score = sum(1 for r in data_points_test.values() if r['found']) / len(data_points_test)
        
        overall_score = (metrics_score + segments_score + tables_score + chunks_score + data_points_score) / 5
        
        report = {
            'overall_score': overall_score,
            'metrics_test': metrics_test,
            'segments_test': segments_test,
            'tables_test': tables_test,
            'chunks_test': chunks_test,
            'data_points_test': data_points_test,
            'summary': {
                'metrics_score': metrics_score,
                'segments_score': segments_score,
                'tables_score': tables_score,
                'chunks_score': chunks_score,
                'data_points_score': data_points_score
            }
        }
        
        print(f"\n📊 测试总结:")
        print(f"   💰 财务指标提取: {metrics_score:.1%}")
        print(f"   🏢 业务分部识别: {segments_score:.1%}")
        print(f"   📋 表格提取质量: {tables_score:.1%}")
        print(f"   📝 片段质量评分: {chunks_score:.1%}")
        print(f"   🎯 数据点准确性: {data_points_score:.1%}")
        print(f"   🏆 总体评分: {overall_score:.1%}")
        
        # 评级
        if overall_score >= 0.9:
            grade = "A+ (优秀)"
        elif overall_score >= 0.8:
            grade = "A (良好)"
        elif overall_score >= 0.7:
            grade = "B (一般)"
        else:
            grade = "C (需改进)"
        
        print(f"   📈 解析质量等级: {grade}")
        
        return report

def main():
    """主函数"""
    # 使用最新的MinerU处理结果
    chunks_file = "parsing_results/小米集团2024年中期报告_20250719_232130_chunks.json"
    content_file = "parsing_results/小米集团2024年中期报告_20250719_232130_content.md"
    
    if not Path(chunks_file).exists():
        print(f"❌ 片段文件不存在: {chunks_file}")
        return
    
    if not Path(content_file).exists():
        print(f"❌ 内容文件不存在: {content_file}")
        return
    
    # 创建测试器并运行测试
    tester = XiaomiReportTester(chunks_file, content_file)
    report = tester.generate_test_report()
    
    # 保存测试报告
    report_file = "parsing_results/xiaomi_test_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存: {report_file}")

if __name__ == "__main__":
    main()
