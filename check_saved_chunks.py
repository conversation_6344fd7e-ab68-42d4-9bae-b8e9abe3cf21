#!/usr/bin/env python3
"""
检查保存的chunks内容
"""

import json
from pathlib import Path

def check_saved_chunks():
    """检查保存的chunks"""
    # 检查保存的chunks
    chunks_file = Path('parsing_results/final_chunks/内蒙古伊利实业集团股份有限公司 2025年第一季度报告_PDF_final_chunks.json')

    with open(chunks_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f'总共 {data["total_chunks"]} 个片段')

    # 查找包含稀释每股收益的片段
    eps_chunks = []
    for i, chunk in enumerate(data['chunks']):
        content = chunk.get('content', '')
        if '每股收益' in content or '稀释' in content:
            eps_chunks.append((i, chunk))

    print(f'\n找到 {len(eps_chunks)} 个包含每股收益的片段:')
    for i, (idx, chunk) in enumerate(eps_chunks):
        print(f'片段 {idx}: {chunk["content"][:200]}...')
        
    # 查找包含总资产的片段
    asset_chunks = []
    for i, chunk in enumerate(data['chunks']):
        content = chunk.get('content', '')
        if '总资产' in content or '资产总计' in content:
            asset_chunks.append((i, chunk))

    print(f'\n找到 {len(asset_chunks)} 个包含总资产的片段:')
    for i, (idx, chunk) in enumerate(asset_chunks):
        print(f'片段 {idx}: {chunk["content"][:200]}...')

if __name__ == "__main__":
    check_saved_chunks()
