import asyncio
import aiohttp
import numpy as np
from typing import List, Union
from config.config import (
    USE_LOCAL_EMBEDDING,
    LOCAL_EMBEDDING_MODEL,
    DASHSCOPE_API_KEY,
    ALI_EMBEDDING_MODEL,
    VECTOR_DIM
)
from .batch_embedding import BatchEmbeddingProcessor

class EmbeddingModel:
    def __init__(self):
        """初始化嵌入模型"""
        self.use_local = USE_LOCAL_EMBEDDING

        if self.use_local:
            self._init_local_model()
        else:
            self._init_api_client()

    def _init_local_model(self):
        """初始化本地嵌入模型"""
        # 为了简化演示，直接使用随机向量
        print("✓ 使用随机向量作为嵌入（演示模式）")
        self.model = None
        self.use_fallback = True  # 临时启用随机向量，加快处理速度

        # 如果需要真实的嵌入模型，可以取消注释以下代码：
        # try:
        #     from sentence_transformers import SentenceTransformer
        #     self.model = SentenceTransformer(LOCAL_EMBEDDING_MODEL)
        #     print(f"✓ 本地嵌入模型加载成功: {LOCAL_EMBEDDING_MODEL}")
        #     self.use_fallback = False
        # except ImportError:
        #     print("⚠ sentence-transformers 未安装，将使用随机向量作为fallback")
        #     self.model = None
        #     self.use_fallback = True
        # except Exception as e:
        #     print(f"⚠ 本地模型加载失败，将使用随机向量作为fallback: {e}")
        #     self.model = None
        #     self.use_fallback = True

    def _init_api_client(self):
        """初始化API客户端"""
        if not DASHSCOPE_API_KEY:
            print("⚠ DASHSCOPE_API_KEY 未设置，将使用随机向量作为fallback")
            self.use_fallback = True

        else:
            self.api_key = DASHSCOPE_API_KEY
            self.api_url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
            self.use_fallback = False
            print("✓ 阿里云嵌入API客户端初始化成功")

        # 初始化批量处理器
        self.batch_processor = BatchEmbeddingProcessor()

    def encode(self, texts: Union[str, List[str]]) -> np.ndarray:
        """
        将文本编码为向量（同步版本）
        :param texts: 单个文本或文本列表
        :return: numpy数组形式的向量
        """
        if isinstance(texts, str):
            texts = [texts]

        if self.use_local:
            return self._encode_local(texts)
        else:
            # 使用同步的API调用
            return self._encode_api_sync(texts)

    async def encode_async(self, texts: Union[str, List[str]]) -> np.ndarray:
        """
        将文本编码为向量（异步版本）
        :param texts: 单个文本或文本列表
        :return: numpy数组形式的向量
        """
        if isinstance(texts, str):
            texts = [texts]

        if self.use_local:
            return self._encode_local(texts)
        else:
            return await self._encode_api(texts)

    def _encode_local(self, texts: List[str]) -> np.ndarray:
        """使用本地模型编码"""
        if self.use_fallback or self.model is None:
            # 使用随机向量作为fallback
            print(f"⚠ 使用随机向量编码 {len(texts)} 个文本")
            embeddings = np.random.rand(len(texts), VECTOR_DIM).astype('float32')
            # 标准化向量
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            embeddings = embeddings / norms
            return embeddings
        else:
            embeddings = self.model.encode(texts, convert_to_numpy=True)
            return embeddings.astype('float32')

    async def _encode_api(self, texts: List[str]) -> np.ndarray:
        """使用API编码"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        all_embeddings = []

        async with aiohttp.ClientSession() as session:
            for text in texts:
                data = {
                    "model": ALI_EMBEDDING_MODEL,
                    "input": {
                        "texts": [text]
                    }
                }

                async with session.post(self.api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        embedding = result["output"]["embeddings"][0]["embedding"]
                        all_embeddings.append(embedding)
                    else:
                        error_text = await response.text()
                        raise Exception(f"API调用失败: {response.status} - {error_text}")

        return np.array(all_embeddings, dtype='float32')

    def _encode_api_sync(self, texts: List[str]) -> np.ndarray:
        """使用API编码（同步版本）"""
        if self.use_fallback:
            # 使用随机向量作为fallback
            print(f"⚠ 使用随机向量编码 {len(texts)} 个文本")
            embeddings = np.random.rand(len(texts), VECTOR_DIM).astype('float32')
            # 标准化向量
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            embeddings = embeddings / norms
            return embeddings

        import requests

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        all_embeddings = []
        total_texts = len(texts)

        print(f"    🔄 开始向量化 {total_texts} 个文本片段...")

        for i, text in enumerate(texts, 1):
            if i % 5 == 0 or i == total_texts:
                print(f"    🔄 向量化进度: {i}/{total_texts}")

            # 截断过长的文本
            if len(text) > 8000:  # 阿里云API限制
                text = text[:8000]

            data = {
                "model": ALI_EMBEDDING_MODEL,
                "input": {
                    "texts": [text]
                }
            }

            try:
                response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
                if response.status_code == 200:
                    result = response.json()
                    embedding = result["output"]["embeddings"][0]["embedding"]
                    all_embeddings.append(embedding)
                else:
                    # 如果API调用失败，使用随机向量作为fallback
                    print(f"    ⚠ API调用失败（{i}/{total_texts}），使用随机向量: {response.status_code}")
                    all_embeddings.append(np.random.rand(VECTOR_DIM).tolist())
            except Exception as e:
                print(f"    ⚠ API调用异常（{i}/{total_texts}），使用随机向量: {e}")
                all_embeddings.append(np.random.rand(VECTOR_DIM).tolist())

        print(f"    ✅ 向量化完成: {len(all_embeddings)} 个向量")
        return np.array(all_embeddings, dtype='float32')

    def encode_batch(self, texts: List[str], use_batch_api: bool = True) -> np.ndarray:
        """
        批量编码文本（支持异步批处理）

        Args:
            texts: 文本列表
            use_batch_api: 是否使用批量API

        Returns:
            np.ndarray: 嵌入向量数组
        """
        if len(texts) == 0:
            return np.array([], dtype='float32').reshape(0, VECTOR_DIM)

        # 如果文本数量较少或不使用批量API，使用常规方法
        if len(texts) < 10 or not use_batch_api or self.use_local:
            return self.encode(texts)

        # 使用批量异步处理
        if hasattr(self, 'batch_processor'):
            print(f"🚀 使用批量异步处理 {len(texts)} 个文本...")
            return self.batch_processor.process_texts_batch(texts)
        else:
            # 回退到常规处理
            return self.encode(texts)

    def get_dimension(self) -> int:
        """获取向量维度"""
        return VECTOR_DIM