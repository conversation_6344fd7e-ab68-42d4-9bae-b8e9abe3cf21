#!/usr/bin/env python3
"""
财报专用查询处理器
针对包含表格的财报文件，使用专门的prompt和LLM处理
"""

import json
import pickle
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path


class FinancialQueryProcessor:
    """财报专用查询处理器"""
    
    def __init__(self):
        """初始化财报查询处理器"""
        self.financial_keywords = {
            '营业收入', '净利润', '归属于上市公司股东的净利润', '总资产', '股东权益',
            '基本每股收益', '稀释每股收益', '净资产收益率', '经营活动产生的现金流量净额',
            '营业成本', '销售费用', '管理费用', '财务费用', '研发费用', '所得税费用',
            '流动资产', '非流动资产', '流动负债', '非流动负债', '资本公积', '盈余公积',
            '增长率', '同比', '环比', '变动幅度', '财报', '季度报告', '年报', '中期报告'
        }
        
        self.financial_file_patterns = {
            '财报', '季度报告', '年报', '中期报告', '财务报告', '业绩报告'
        }
        
        print("✓ 财报专用查询处理器初始化成功")
    
    def is_financial_query(self, query: str) -> bool:
        """
        判断是否为财务相关查询
        
        Args:
            query: 用户查询
            
        Returns:
            bool: 是否为财务查询
        """
        query_lower = query.lower()
        
        # 检查财务关键词
        for keyword in self.financial_keywords:
            if keyword in query:
                return True
        
        # 检查数字和百分比模式
        import re
        if re.search(r'\d+.*?[亿万元%]', query) or re.search(r'多少|增长|下降|变化', query):
            return True
        
        return False
    
    def get_financial_documents(self) -> List[Dict[str, Any]]:
        """
        获取所有财报相关文档
        
        Returns:
            List[Dict[str, Any]]: 财报文档列表
        """
        try:
            metadata_file = Path("faiss_index/metadata.pkl")
            if not metadata_file.exists():
                return []
            
            with open(metadata_file, 'rb') as f:
                documents = pickle.load(f)
            
            # 筛选财报相关文档
            financial_docs = []
            for doc in documents:
                source = doc.get('source', '').lower()
                
                # 检查文件名是否包含财报关键词
                is_financial = any(pattern in source for pattern in self.financial_file_patterns)
                
                # 检查是否包含表格
                has_table = (doc.get('type') in ['table', 'table_row', 'table_row_enhanced'] or 
                           'table' in doc.get('content', '').lower())
                
                if is_financial or has_table:
                    financial_docs.append(doc)
            
            return financial_docs
            
        except Exception as e:
            print(f"❌ 获取财报文档失败: {e}")
            return []
    
    def extract_relevant_financial_data(self, query: str, 
                                      financial_docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取与查询相关的财务数据
        
        Args:
            query: 用户查询
            financial_docs: 财报文档列表
            
        Returns:
            List[Dict[str, Any]]: 相关财务数据
        """
        relevant_data = []
        
        # 提取查询中的关键词
        query_keywords = []
        for keyword in self.financial_keywords:
            if keyword in query:
                query_keywords.append(keyword)
        
        # 如果没有明确的财务关键词，使用通用匹配
        if not query_keywords:
            query_keywords = ['营业收入', '净利润', '总资产', '增长率']
        
        # 按相关性筛选文档
        for doc in financial_docs:
            content = doc.get('content', '')
            source = doc.get('source', '')
            
            # 计算相关性得分
            relevance_score = 0
            matched_keywords = []
            
            for keyword in query_keywords:
                if keyword in content:
                    relevance_score += 2
                    matched_keywords.append(keyword)
                elif keyword in query and any(k in content for k in [keyword[:2], keyword[-2:]]):
                    relevance_score += 1
            
            # 检查数值匹配
            import re
            if re.search(r'\d+[,，]\d+[,，]\d+', content):  # 大数值格式
                relevance_score += 1
            
            if re.search(r'\d+\.?\d*%', content):  # 百分比
                relevance_score += 1
            
            # 表格类型加分
            if doc.get('type') in ['table', 'table_row_enhanced']:
                relevance_score += 1
            
            if relevance_score > 0:
                doc_with_score = doc.copy()
                doc_with_score['relevance_score'] = relevance_score
                doc_with_score['matched_keywords'] = matched_keywords
                relevant_data.append(doc_with_score)
        
        # 按相关性排序
        relevant_data.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return relevant_data[:20]  # 返回最相关的20个文档
    
    def build_financial_prompt(self, query: str, 
                             relevant_data: List[Dict[str, Any]]) -> str:
        """
        构建财报专用的LLM提示词
        
        Args:
            query: 用户查询
            relevant_data: 相关财务数据
            
        Returns:
            str: 构建的提示词
        """
        # 整理数据来源
        sources = set()
        table_data = []
        text_data = []
        
        for doc in relevant_data:
            source = doc.get('source', '')
            if source:
                sources.add(Path(source).name)
            
            content = doc.get('content', '')
            doc_type = doc.get('type', '')
            page = doc.get('page', '')
            
            if doc_type in ['table', 'table_row', 'table_row_enhanced']:
                table_data.append({
                    'content': content,
                    'page': page,
                    'type': doc_type,
                    'source': Path(source).name if source else '未知'
                })
            else:
                text_data.append({
                    'content': content,
                    'page': page,
                    'source': Path(source).name if source else '未知'
                })
        
        # 构建提示词
        prompt = f"""你是一个专业的财务分析师，请基于以下财报数据回答用户的问题。

**用户问题：**
{query}

**数据来源：**
{', '.join(sources) if sources else '财务报告'}

**财务表格数据：**
"""
        
        # 添加表格数据
        if table_data:
            for i, data in enumerate(table_data[:10], 1):  # 最多10个表格数据
                prompt += f"\n{i}. 来源：{data['source']} 第{data['page']}页\n"
                prompt += f"   类型：{data['type']}\n"
                prompt += f"   内容：{data['content']}\n"
        else:
            prompt += "暂无表格数据\n"
        
        # 添加文本数据
        if text_data:
            prompt += f"\n**补充文本信息：**\n"
            for i, data in enumerate(text_data[:5], 1):  # 最多5个文本数据
                prompt += f"\n{i}. 来源：{data['source']} 第{data['page']}页\n"
                prompt += f"   内容：{data['content'][:200]}...\n"
        
        prompt += f"""

**回答要求：**
1. 请基于上述财务数据准确回答用户问题
2. 如果数据中包含具体数值，请在回答中明确引用
3. 如果涉及增长率或变化，请说明对比期间
4. 请在回答末尾列出具体的数据来源，格式为：
   
   **数据来源：**
   - 文档：[文档名称]
   - 页面：第X页
   - 数据类型：[表格/文本]

5. 如果无法从提供的数据中找到答案，请明确说明并建议查阅相关财报的具体章节

**注意事项：**
- 确保数据引用准确，不要编造数据
- 如果数据存在矛盾，请指出并说明可能的原因
- 对于财务术语，请使用专业但易懂的表达方式

请开始回答："""

        return prompt
    
    def process_financial_query(self, query: str) -> Tuple[str, List[Dict[str, Any]]]:
        """
        处理财务查询
        
        Args:
            query: 用户查询
            
        Returns:
            Tuple[str, List[Dict[str, Any]]]: (LLM提示词, 相关数据)
        """
        try:
            print(f"🔍 检测到财务查询，启用专用处理模式")
            
            # 1. 获取财报文档
            financial_docs = self.get_financial_documents()
            print(f"📊 找到 {len(financial_docs)} 个财报相关文档")
            
            if not financial_docs:
                return "未找到相关财报数据，请确认文档已正确加载到知识库中。", []
            
            # 2. 提取相关数据
            relevant_data = self.extract_relevant_financial_data(query, financial_docs)
            print(f"🎯 筛选出 {len(relevant_data)} 个相关数据片段")
            
            if not relevant_data:
                return "在已有财报数据中未找到与您查询相关的信息，建议您查阅完整的财务报告。", []
            
            # 3. 构建专用提示词
            financial_prompt = self.build_financial_prompt(query, relevant_data)
            
            return financial_prompt, relevant_data
            
        except Exception as e:
            print(f"❌ 财务查询处理失败: {e}")
            return f"财务查询处理出现错误：{e}", []
    
    def format_financial_response(self, llm_response: str, 
                                relevant_data: List[Dict[str, Any]]) -> str:
        """
        格式化财务查询响应
        
        Args:
            llm_response: LLM原始响应
            relevant_data: 相关数据
            
        Returns:
            str: 格式化后的响应
        """
        try:
            # 如果LLM响应已经包含数据来源，直接返回
            if "数据来源" in llm_response or "来源：" in llm_response:
                return llm_response
            
            # 否则添加数据来源信息
            sources_info = "\n\n**数据来源：**\n"
            
            # 统计来源信息
            source_stats = {}
            for doc in relevant_data[:10]:  # 最多显示10个来源
                source = Path(doc.get('source', '')).name if doc.get('source') else '未知文档'
                page = doc.get('page', '未知')
                doc_type = doc.get('type', 'text')
                
                key = f"{source}_第{page}页"
                if key not in source_stats:
                    source_stats[key] = {
                        'source': source,
                        'page': page,
                        'types': set(),
                        'count': 0
                    }
                
                source_stats[key]['types'].add(doc_type)
                source_stats[key]['count'] += 1
            
            # 格式化来源信息
            for i, (key, info) in enumerate(source_stats.items(), 1):
                types_str = "、".join(info['types'])
                sources_info += f"{i}. 文档：{info['source']}\n"
                sources_info += f"   页面：第{info['page']}页\n"
                sources_info += f"   数据类型：{types_str}\n"
                sources_info += f"   引用片段：{info['count']}个\n\n"
            
            return llm_response + sources_info
            
        except Exception as e:
            print(f"❌ 响应格式化失败: {e}")
            return llm_response
