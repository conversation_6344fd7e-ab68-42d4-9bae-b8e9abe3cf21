import json
import aiohttp
from typing import AsyncGenerator, Optional, Dict, Any
from config.config import DEEPSEEK_API_KEY, DEEPSEEK_API_BASE

class LLMClient:
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化LLM客户端
        :param api_key: DeepSeek API密钥，如果不提供则使用配置文件中的密钥
        """
        self.api_key = api_key or DEEPSEEK_API_KEY
        if not self.api_key:
            raise ValueError("DeepSeek API密钥未设置")
        
        self.api_base = DEEPSEEK_API_BASE
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    async def chat_stream(
        self,
        messages: list,
        context: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
    ) -> AsyncGenerator[str, None]:
        """
        与DeepSeek Chat API进行流式对话
        :param messages: 对话历史消息列表
        :param context: 上下文信息
        :param temperature: 温度参数
        :param max_tokens: 最大生成token数
        :return: 生成文本的异步生成器
        """
        # 如果提供了上下文，将其添加到system message中
        if context:
            system_message = {
                "role": "system",
                "content": (
                    "你是一个AI助手。请基于以下参考信息回答用户的问题。"
                    f"参考信息：\n{context}\n\n"
                    "如果参考信息中没有相关内容，请明确告知用户。"
                    "回答时请给出具体的参考来源（文件名和页码）。"
                )
            }
            messages = [system_message] + messages

        # 准备请求数据
        data = {
            "model": "deepseek-chat",
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/chat/completions",
                headers=self.headers,
                json=data
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"API请求失败: {response.status} - {error_text}")

                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line:
                        if line.startswith('data: '):
                            line = line[6:]  # 移除 "data: " 前缀
                        if line == '[DONE]':
                            break
                        try:
                            response_data = json.loads(line)
                            if 'choices' in response_data:
                                delta = response_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue

    async def chat(
        self,
        messages: list,
        context: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000,
    ) -> str:
        """
        与DeepSeek Chat API进行非流式对话
        :param messages: 对话历史消息列表
        :param context: 上下文信息
        :param temperature: 温度参数
        :param max_tokens: 最大生成token数
        :return: 生成的完整响应文本
        """
        response_text = ""
        async for text in self.chat_stream(messages, context, temperature, max_tokens):
            response_text += text
        return response_text

    def format_retrieved_context(self, documents: list) -> str:
        """
        格式化检索到的文档作为上下文
        :param documents: 检索到的文档列表
        :return: 格式化后的上下文字符串
        """
        context_parts = []
        for i, doc in enumerate(documents, 1):
            source = doc["source"]
            page = doc["page"]
            content = doc["content"]
            doc_type = doc["type"]
            
            if doc_type == "table":
                content = f"表格内容：{content}"
            
            context_parts.append(
                f"[文档 {i}] 来源: {source}, 页码: {page}\n"
                f"内容: {content}\n"
            )
        
        return "\n".join(context_parts) 