#!/usr/bin/env python3
"""
表格感知的智能文档分块器
能够识别表格结构，保持表格完整性，并将相关文本与表格合并到同一chunk中
"""

import re
from typing import List, Dict, Any, Tuple
from .logger import get_logger


class TableAwareChunker:
    """表格感知的智能分块器"""
    
    def __init__(self, chunk_size: int = 950, chunk_overlap: int = 50):
        """
        初始化分块器
        
        Args:
            chunk_size: 目标chunk大小
            chunk_overlap: chunk重叠大小
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.logger = get_logger()
    
    def chunk_document(self, content: str, document_info: str) -> List[Dict[str, Any]]:
        """
        智能分块文档
        
        Args:
            content: 文档内容
            document_info: 文档信息
            
        Returns:
            List[Dict[str, Any]]: 分块结果
        """
        # 按页面分割
        pages = self._split_by_pages(content)
        
        chunks = []
        chunk_id = 0
        
        for page_num, page_content in enumerate(pages, 1):
            # 识别页面中的表格和文本块
            page_blocks = self._identify_content_blocks(page_content, page_num)
            
            # 智能合并相关块
            merged_blocks = self._merge_related_blocks(page_blocks)
            
            # 生成chunks
            for block in merged_blocks:
                if len(block['content'].strip()) > 0:
                    chunk = {
                        'content': block['content'].strip(),
                        'chunk_id': chunk_id,
                        'page': page_num,
                        'type': block['type'],
                        'document_info': document_info,
                        'has_table': block.get('has_table', False),
                        'table_count': block.get('table_count', 0)
                    }
                    chunks.append(chunk)
                    chunk_id += 1
        
        # 后处理：处理跨页表格
        chunks = self._handle_cross_page_tables(chunks)
        
        self.logger.info(f"表格感知分块完成: {len(chunks)} 个chunks")
        
        return chunks
    
    def _split_by_pages(self, content: str) -> List[str]:
        """按页面分割内容"""
        # 识别页面分隔符
        page_patterns = [
            r'## 第(\d+)页',
            r'# 第(\d+)页',
            r'页码[：:]\s*(\d+)',
            r'第\s*(\d+)\s*页'
        ]
        
        pages = []
        current_page = ""
        
        lines = content.split('\n')
        
        for line in lines:
            is_page_break = False
            
            for pattern in page_patterns:
                if re.search(pattern, line):
                    if current_page.strip():
                        pages.append(current_page.strip())
                    current_page = line + '\n'
                    is_page_break = True
                    break
            
            if not is_page_break:
                current_page += line + '\n'
        
        # 添加最后一页
        if current_page.strip():
            pages.append(current_page.strip())
        
        # 如果没有找到页面分隔符，将整个内容作为一页
        if not pages:
            pages = [content]
        
        return pages
    
    def _identify_content_blocks(self, page_content: str, page_num: int) -> List[Dict[str, Any]]:
        """识别页面中的内容块（表格、文本等）"""
        blocks = []
        lines = page_content.split('\n')
        
        current_block = {
            'content': '',
            'type': 'text',
            'has_table': False,
            'table_count': 0,
            'start_line': 0
        }
        
        in_table = False
        table_lines = []
        
        for i, line in enumerate(lines):
            # 检测表格开始
            if self._is_table_line(line):
                if not in_table:
                    # 表格开始，保存之前的文本块
                    if current_block['content'].strip():
                        blocks.append(current_block.copy())
                    
                    # 开始新的表格块
                    current_block = {
                        'content': '',
                        'type': 'mixed',  # 可能包含表格标题
                        'has_table': True,
                        'table_count': 1,
                        'start_line': i
                    }
                    in_table = True
                
                table_lines.append(line)
                current_block['content'] += line + '\n'
                
            elif in_table and self._is_table_continuation(line, table_lines):
                # 表格继续
                table_lines.append(line)
                current_block['content'] += line + '\n'
                
            else:
                # 非表格行
                if in_table:
                    # 表格结束，检查是否需要包含后续相关文本
                    if self._is_table_related_text(line):
                        current_block['content'] += line + '\n'
                    else:
                        # 表格块结束
                        if current_block['content'].strip():
                            blocks.append(current_block.copy())
                        
                        # 开始新的文本块
                        current_block = {
                            'content': line + '\n',
                            'type': 'text',
                            'has_table': False,
                            'table_count': 0,
                            'start_line': i
                        }
                        in_table = False
                        table_lines = []
                else:
                    # 普通文本
                    current_block['content'] += line + '\n'
        
        # 添加最后一个块
        if current_block['content'].strip():
            blocks.append(current_block)
        
        return blocks
    
    def _is_table_line(self, line: str) -> bool:
        """判断是否是表格行"""
        line = line.strip()
        
        # Markdown表格特征
        if '|' in line and line.count('|') >= 2:
            return True
        
        # 表格分隔行
        if re.match(r'^[\s\-\|:]+$', line) and '|' in line:
            return True
        
        return False
    
    def _is_table_continuation(self, line: str, table_lines: List[str]) -> bool:
        """判断是否是表格的继续行"""
        line = line.strip()
        
        # 空行可能是表格内的分隔
        if not line:
            return True
        
        # 包含管道符的行
        if '|' in line:
            return True
        
        # 如果前面有表格分隔行，后面的行可能是表格数据
        if table_lines and any('---' in tl for tl in table_lines[-3:]):
            # 检查是否包含数字或常见表格内容
            if re.search(r'\d+[,.]?\d*', line) or any(keyword in line for keyword in ['项目', '合计', '小计', '总计']):
                return True
        
        return False
    
    def _is_table_related_text(self, line: str) -> bool:
        """判断是否是表格相关的文本（如表格标题、注释等）"""
        line = line.strip().lower()
        
        # 表格标题关键词
        title_keywords = ['表', '一览表', '明细表', '统计表', '财务数据', '主要指标', '会计数据']
        if any(keyword in line for keyword in title_keywords):
            return True
        
        # 表格注释关键词
        note_keywords = ['注：', '说明：', '备注：', '单位：', '币种：']
        if any(keyword in line for keyword in note_keywords):
            return True
        
        return False
    
    def _merge_related_blocks(self, blocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并相关的内容块"""
        if not blocks:
            return blocks
        
        merged_blocks = []
        current_merged = blocks[0].copy()
        
        for i in range(1, len(blocks)):
            block = blocks[i]
            
            # 检查是否应该合并
            should_merge = False
            
            # 1. 如果当前块是表格，下一个块是相关文本
            if current_merged['has_table'] and not block['has_table']:
                if len(block['content']) < 200:  # 短文本可能是表格说明
                    should_merge = True
            
            # 2. 如果当前块是文本，下一个块是表格
            elif not current_merged['has_table'] and block['has_table']:
                if len(current_merged['content']) < 300:  # 短文本可能是表格标题
                    should_merge = True
            
            # 3. 检查合并后的大小
            if should_merge:
                merged_content = current_merged['content'] + '\n' + block['content']
                if len(merged_content) <= self.chunk_size * 1.2:  # 允许超出20%
                    current_merged['content'] = merged_content
                    current_merged['has_table'] = current_merged['has_table'] or block['has_table']
                    current_merged['table_count'] += block['table_count']
                    current_merged['type'] = 'mixed' if current_merged['has_table'] else 'text'
                    continue
            
            # 不合并，保存当前块并开始新块
            merged_blocks.append(current_merged)
            current_merged = block.copy()
        
        # 添加最后一个块
        merged_blocks.append(current_merged)
        
        return merged_blocks
    
    def _handle_cross_page_tables(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理跨页表格"""
        if len(chunks) <= 1:
            return chunks
        
        processed_chunks = []
        i = 0
        
        while i < len(chunks):
            current_chunk = chunks[i]
            
            # 检查是否是跨页表格的情况
            if (i < len(chunks) - 1 and 
                current_chunk.get('has_table', False) and
                chunks[i + 1].get('page', 0) == current_chunk.get('page', 0) + 1):
                
                next_chunk = chunks[i + 1]
                
                # 如果下一页开头是表格继续
                if self._is_table_continuation_chunk(current_chunk, next_chunk):
                    # 合并跨页表格
                    merged_content = current_chunk['content'] + '\n\n' + next_chunk['content']
                    
                    if len(merged_content) <= self.chunk_size * 1.5:  # 允许跨页表格更大
                        merged_chunk = current_chunk.copy()
                        merged_chunk['content'] = merged_content
                        merged_chunk['table_count'] += next_chunk.get('table_count', 0)
                        processed_chunks.append(merged_chunk)
                        i += 2  # 跳过下一个chunk
                        continue
            
            processed_chunks.append(current_chunk)
            i += 1
        
        return processed_chunks
    
    def _is_table_continuation_chunk(self, chunk1: Dict[str, Any], chunk2: Dict[str, Any]) -> bool:
        """判断chunk2是否是chunk1表格的继续"""
        content2_start = chunk2['content'].strip()[:200]
        
        # 如果第二个chunk开头就是表格行
        if '|' in content2_start.split('\n')[0]:
            return True
        
        # 如果包含表格继续的关键词
        continuation_keywords = ['续表', '接上表', '（续）', '续上页']
        if any(keyword in content2_start for keyword in continuation_keywords):
            return True
        
        return False
