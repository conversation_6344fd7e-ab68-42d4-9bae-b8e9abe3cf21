"""
表格检测和Markdown转换模块
在OCR识别过程中检测表格并转换为Markdown格式
"""

import re
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

try:
    import pdfplumber
    import fitz  # PyMuPDF
    from tabulate import tabulate
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False

class TableDetector:
    """表格检测器"""
    
    def __init__(self):
        """初始化表格检测器"""
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError("缺少必要依赖: pip install pdfplumber tabulate")
        
        # 表格检测参数
        self.table_settings = {
            "vertical_strategy": "lines",
            "horizontal_strategy": "lines",
            "min_words_vertical": 3,
            "min_words_horizontal": 1,
            "intersection_tolerance": 3,
            "text_tolerance": 3,
            "text_x_tolerance": 3,
            "text_y_tolerance": 3
        }
        
        print("✓ 表格检测器初始化成功")
    
    def detect_tables_in_page(self, pdf_path: str, page_num: int) -> List[Dict[str, Any]]:
        """
        检测页面中的表格
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页面编号（从0开始）
            
        Returns:
            List[Dict]: 检测到的表格信息列表
        """
        tables = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                if page_num >= len(pdf.pages):
                    return tables
                
                page = pdf.pages[page_num]
                
                # 检测表格
                detected_tables = page.find_tables(table_settings=self.table_settings)
                
                for i, table in enumerate(detected_tables):
                    # 提取表格数据
                    table_data = table.extract()
                    
                    if table_data and len(table_data) > 1:  # 至少有标题行和一行数据
                        # 清理表格数据
                        cleaned_data = self._clean_table_data(table_data)
                        
                        if cleaned_data:
                            table_info = {
                                'table_id': f"page_{page_num+1}_table_{i+1}",
                                'page': page_num + 1,
                                'bbox': table.bbox,  # 边界框
                                'data': cleaned_data,
                                'rows': len(cleaned_data),
                                'cols': len(cleaned_data[0]) if cleaned_data else 0,
                                'area': self._calculate_table_area(table.bbox)
                            }
                            tables.append(table_info)
                            
        except Exception as e:
            print(f"⚠ 页面{page_num+1}表格检测失败: {e}")
        
        return tables
    
    def _clean_table_data(self, raw_data: List[List[str]]) -> List[List[str]]:
        """
        清理表格数据
        
        Args:
            raw_data: 原始表格数据
            
        Returns:
            List[List[str]]: 清理后的表格数据
        """
        if not raw_data:
            return []
        
        cleaned_data = []
        
        for row in raw_data:
            if not row:
                continue
            
            # 清理每个单元格
            cleaned_row = []
            for cell in row:
                if cell is None:
                    cleaned_cell = ""
                else:
                    # 清理单元格内容
                    cleaned_cell = str(cell).strip()
                    # 移除多余的空白字符
                    cleaned_cell = re.sub(r'\s+', ' ', cleaned_cell)
                
                cleaned_row.append(cleaned_cell)
            
            # 只保留非空行
            if any(cell.strip() for cell in cleaned_row):
                cleaned_data.append(cleaned_row)
        
        return cleaned_data
    
    def _calculate_table_area(self, bbox: Tuple[float, float, float, float]) -> float:
        """
        计算表格面积
        
        Args:
            bbox: 边界框 (x0, y0, x1, y1)
            
        Returns:
            float: 表格面积
        """
        if not bbox or len(bbox) != 4:
            return 0.0
        
        x0, y0, x1, y1 = bbox
        return abs((x1 - x0) * (y1 - y0))
    
    def convert_table_to_markdown(self, table_data: List[List[str]], table_id: str = "") -> str:
        """
        将表格数据转换为Markdown格式
        
        Args:
            table_data: 表格数据
            table_id: 表格ID
            
        Returns:
            str: Markdown格式的表格
        """
        if not table_data or len(table_data) < 1:
            return ""
        
        try:
            # 确保所有行的列数一致
            max_cols = max(len(row) for row in table_data)
            normalized_data = []
            
            for row in table_data:
                # 补齐列数
                normalized_row = row + [""] * (max_cols - len(row))
                normalized_data.append(normalized_row)
            
            # 使用tabulate生成Markdown表格
            if len(normalized_data) > 1:
                # 有标题行
                headers = normalized_data[0]
                data_rows = normalized_data[1:]
                markdown_table = tabulate(data_rows, headers=headers, tablefmt="pipe")
            else:
                # 只有一行数据，作为标题
                markdown_table = tabulate([normalized_data[0]], tablefmt="pipe")
            
            # 添加表格标识
            result = f"\n**表格 {table_id}**\n\n{markdown_table}\n"
            
            return result
            
        except Exception as e:
            print(f"⚠ 表格转换Markdown失败: {e}")
            # 回退到简单格式
            return self._fallback_table_format(table_data, table_id)
    
    def _fallback_table_format(self, table_data: List[List[str]], table_id: str = "") -> str:
        """
        回退的表格格式化方法
        
        Args:
            table_data: 表格数据
            table_id: 表格ID
            
        Returns:
            str: 简单格式的表格
        """
        if not table_data:
            return ""
        
        result = f"\n**表格 {table_id}**\n\n"
        
        for i, row in enumerate(table_data):
            if i == 0:
                # 标题行
                result += "| " + " | ".join(str(cell) for cell in row) + " |\n"
                result += "|" + "|".join(" --- " for _ in row) + "|\n"
            else:
                # 数据行
                result += "| " + " | ".join(str(cell) for cell in row) + " |\n"
        
        result += "\n"
        return result
    
    def is_table_region(self, page_image, bbox: Tuple[float, float, float, float]) -> bool:
        """
        判断指定区域是否为表格
        
        Args:
            page_image: 页面图像
            bbox: 边界框
            
        Returns:
            bool: 是否为表格区域
        """
        # 这里可以添加更复杂的表格检测逻辑
        # 比如检测线条、网格结构等
        
        # 简单实现：基于面积判断
        area = self._calculate_table_area(bbox)
        
        # 如果面积太小，可能不是表格
        if area < 1000:  # 可调整的阈值
            return False
        
        return True
    
    def extract_non_table_regions(self, pdf_path: str, page_num: int, tables: List[Dict[str, Any]]) -> List[Tuple[float, float, float, float]]:
        """
        提取非表格区域的边界框
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页面编号
            tables: 检测到的表格列表
            
        Returns:
            List[Tuple]: 非表格区域的边界框列表
        """
        non_table_regions = []
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                if page_num >= len(pdf.pages):
                    return non_table_regions
                
                page = pdf.pages[page_num]
                page_bbox = (0, 0, page.width, page.height)
                
                if not tables:
                    # 没有表格，整个页面都是非表格区域
                    return [page_bbox]
                
                # 简单实现：将页面分割为多个区域
                # 这里可以实现更复杂的区域分割算法
                
                # 按表格位置分割页面
                table_bboxes = [table['bbox'] for table in tables]
                
                # 计算非表格区域（简化实现）
                # 实际应用中需要更复杂的几何计算
                y_positions = []
                for bbox in table_bboxes:
                    y_positions.extend([bbox[1], bbox[3]])  # y0, y1
                
                y_positions = sorted(set(y_positions))
                
                # 在表格之间创建区域
                current_y = 0
                for y in y_positions:
                    if y > current_y:
                        # 检查这个区域是否与表格重叠
                        region_bbox = (0, current_y, page.width, y)
                        if not self._overlaps_with_tables(region_bbox, table_bboxes):
                            non_table_regions.append(region_bbox)
                    current_y = y
                
                # 添加最后一个区域
                if current_y < page.height:
                    region_bbox = (0, current_y, page.width, page.height)
                    if not self._overlaps_with_tables(region_bbox, table_bboxes):
                        non_table_regions.append(region_bbox)
                        
        except Exception as e:
            print(f"⚠ 非表格区域提取失败: {e}")
        
        return non_table_regions
    
    def _overlaps_with_tables(self, region_bbox: Tuple[float, float, float, float], 
                             table_bboxes: List[Tuple[float, float, float, float]]) -> bool:
        """
        检查区域是否与表格重叠
        
        Args:
            region_bbox: 区域边界框
            table_bboxes: 表格边界框列表
            
        Returns:
            bool: 是否重叠
        """
        rx0, ry0, rx1, ry1 = region_bbox
        
        for tx0, ty0, tx1, ty1 in table_bboxes:
            # 检查重叠
            if not (rx1 < tx0 or rx0 > tx1 or ry1 < ty0 or ry0 > ty1):
                return True
        
        return False
