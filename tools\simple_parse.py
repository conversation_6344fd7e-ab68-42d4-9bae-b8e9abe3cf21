#!/usr/bin/env python3
"""
简化解析工具
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.simplified_document_processor import SimplifiedDocumentProcessor


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python tools/simple_parse.py <文件路径>")
        return
    
    file_path = sys.argv[1]
    
    print(f"🔍 解析文件: {file_path}")
    print("=" * 50)
    
    try:
        processor = SimplifiedDocumentProcessor()
        chunks = processor.process_file(file_path, save_results=True)
        
        print(f"✅ 解析完成!")
        print(f"   生成片段: {len(chunks)} 个")
        
        # 显示前3个片段的内容预览
        for i, chunk in enumerate(chunks[:3], 1):
            content = chunk['content'][:200] + "..." if len(chunk['content']) > 200 else chunk['content']
            print(f"\n片段 {i}:")
            print(f"  类型: {chunk['type']}")
            print(f"  内容: {content}")
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
