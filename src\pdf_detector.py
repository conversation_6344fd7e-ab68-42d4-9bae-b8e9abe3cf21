"""
PDF类型检测模块
自动判断PDF是否为扫描版，区分文本型PDF和扫描型PDF
"""

import re
from typing import Tuple, Dict, Any
from pathlib import Path
from PyPDF2 import PdfReader

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class PDFDetector:
    """PDF类型检测器"""
    
    def __init__(self):
        """初始化PDF检测器"""
        self.min_text_ratio = 0.1  # 最小文本比例阈值
        self.min_readable_chars = 50  # 最小可读字符数
        self.cid_pattern = re.compile(r'\(cid:\d+\)')  # CID编码模式
        
    def detect_pdf_type(self, file_path: Path) -> Dict[str, Any]:
        """
        检测PDF类型
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        result = {
            'file_path': str(file_path),
            'is_scanned': False,
            'is_text_based': False,
            'has_extractable_text': False,
            'text_quality': 'unknown',
            'confidence': 0.0,
            'recommendation': 'unknown',
            'details': {}
        }
        
        try:
            # 基本信息
            reader = PdfReader(file_path)
            total_pages = len(reader.pages)
            result['details']['total_pages'] = total_pages
            
            print(f"    📄 检测PDF类型: {file_path.name} ({total_pages} 页)")
            
            # 采样检测（检测前几页和中间几页）
            sample_pages = self._get_sample_pages(total_pages)
            result['details']['sample_pages'] = sample_pages
            
            text_stats = []
            
            for page_num in sample_pages:
                if page_num < total_pages:
                    page_result = self._analyze_page(reader.pages[page_num], page_num + 1)
                    text_stats.append(page_result)
            
            # 综合分析
            analysis = self._analyze_text_stats(text_stats)
            result.update(analysis)
            
            # 生成建议
            result['recommendation'] = self._generate_recommendation(result)
            
            print(f"    📊 检测结果: {result['recommendation']}")
            
            return result
            
        except Exception as e:
            print(f"    ❌ PDF类型检测失败: {e}")
            result['error'] = str(e)
            return result
    
    def _get_sample_pages(self, total_pages: int) -> list:
        """
        获取采样页面
        
        Args:
            total_pages: 总页数
            
        Returns:
            list: 采样页面索引列表
        """
        if total_pages <= 5:
            return list(range(total_pages))
        elif total_pages <= 20:
            return [0, 1, total_pages // 2, total_pages - 2, total_pages - 1]
        else:
            return [0, 1, 2, total_pages // 4, total_pages // 2, 
                   total_pages * 3 // 4, total_pages - 3, total_pages - 2, total_pages - 1]
    
    def _analyze_page(self, page, page_num: int) -> Dict[str, Any]:
        """
        分析单个页面
        
        Args:
            page: PDF页面对象
            page_num: 页码
            
        Returns:
            Dict[str, Any]: 页面分析结果
        """
        result = {
            'page_num': page_num,
            'text_length': 0,
            'readable_chars': 0,
            'chinese_chars': 0,
            'cid_count': 0,
            'has_images': False,
            'text_quality': 'poor'
        }
        
        try:
            # 提取文本
            text = page.extract_text()
            result['text_length'] = len(text) if text else 0
            
            if text:
                # 分析文本质量
                result['readable_chars'] = self._count_readable_chars(text)
                result['chinese_chars'] = self._count_chinese_chars(text)
                result['cid_count'] = len(self.cid_pattern.findall(text))
                
                # 判断文本质量
                if result['cid_count'] > result['readable_chars']:
                    result['text_quality'] = 'cid_encoded'  # CID编码
                elif result['readable_chars'] > self.min_readable_chars:
                    result['text_quality'] = 'good'
                elif result['readable_chars'] > 10:
                    result['text_quality'] = 'fair'
                else:
                    result['text_quality'] = 'poor'
            
            # 检查是否有图像（简化检测）
            try:
                if hasattr(page, 'images') and page.images:
                    result['has_images'] = True
                elif '/XObject' in page.get('/Resources', {}):
                    result['has_images'] = True
            except:
                pass
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _count_readable_chars(self, text: str) -> int:
        """
        计算可读字符数
        
        Args:
            text: 输入文本
            
        Returns:
            int: 可读字符数
        """
        if not text:
            return 0
        
        readable_count = 0
        for char in text:
            char_code = ord(char)
            # 中文字符、英文字母、数字、常用标点
            if (
                (0x4E00 <= char_code <= 0x9FFF) or  # 中文
                (0x3000 <= char_code <= 0x303F) or  # 中文标点
                (0xFF00 <= char_code <= 0xFFEF) or  # 全角字符
                (48 <= char_code <= 57) or          # 数字
                (65 <= char_code <= 90) or          # 大写字母
                (97 <= char_code <= 122) or         # 小写字母
                char in '.,!?;:()[]{}"\'-'          # 常用标点
            ):
                readable_count += 1
        
        return readable_count
    
    def _count_chinese_chars(self, text: str) -> int:
        """
        计算中文字符数
        
        Args:
            text: 输入文本
            
        Returns:
            int: 中文字符数
        """
        if not text:
            return 0
        
        return sum(1 for char in text if 0x4E00 <= ord(char) <= 0x9FFF)
    
    def _analyze_text_stats(self, text_stats: list) -> Dict[str, Any]:
        """
        综合分析文本统计
        
        Args:
            text_stats: 页面文本统计列表
            
        Returns:
            Dict[str, Any]: 综合分析结果
        """
        if not text_stats:
            return {
                'is_scanned': True,
                'is_text_based': False,
                'has_extractable_text': False,
                'text_quality': 'none',
                'confidence': 0.9
            }
        
        # 统计各种质量的页面数
        quality_counts = {'good': 0, 'fair': 0, 'poor': 0, 'cid_encoded': 0}
        total_readable = 0
        total_cid = 0
        pages_with_images = 0
        
        for stat in text_stats:
            quality = stat.get('text_quality', 'poor')
            quality_counts[quality] += 1
            total_readable += stat.get('readable_chars', 0)
            total_cid += stat.get('cid_count', 0)
            if stat.get('has_images', False):
                pages_with_images += 1
        
        total_pages = len(text_stats)
        
        # 判断PDF类型
        good_ratio = quality_counts['good'] / total_pages
        cid_ratio = quality_counts['cid_encoded'] / total_pages
        image_ratio = pages_with_images / total_pages
        
        # 决策逻辑
        if good_ratio >= 0.7:
            # 大部分页面文本质量好
            return {
                'is_scanned': False,
                'is_text_based': True,
                'has_extractable_text': True,
                'text_quality': 'good',
                'confidence': 0.9
            }
        elif cid_ratio >= 0.5:
            # 大部分页面是CID编码
            return {
                'is_scanned': False,
                'is_text_based': True,
                'has_extractable_text': False,
                'text_quality': 'cid_encoded',
                'confidence': 0.8
            }
        elif image_ratio >= 0.7 and total_readable < 100:
            # 大部分页面有图像且可读文本很少
            return {
                'is_scanned': True,
                'is_text_based': False,
                'has_extractable_text': False,
                'text_quality': 'scanned',
                'confidence': 0.8
            }
        else:
            # 混合类型或质量较差
            return {
                'is_scanned': True,
                'is_text_based': False,
                'has_extractable_text': total_readable > 50,
                'text_quality': 'mixed',
                'confidence': 0.6
            }
    
    def _generate_recommendation(self, result: Dict[str, Any]) -> str:
        """
        生成处理建议
        
        Args:
            result: 检测结果
            
        Returns:
            str: 处理建议
        """
        if result['is_text_based'] and result['has_extractable_text']:
            return 'use_text_extraction'  # 使用文本提取
        elif result['text_quality'] == 'cid_encoded':
            return 'use_ocr'  # 使用OCR（CID编码无法直接提取）
        elif result['is_scanned']:
            return 'use_ocr'  # 使用OCR（扫描版）
        else:
            return 'use_ocr'  # 默认使用OCR
