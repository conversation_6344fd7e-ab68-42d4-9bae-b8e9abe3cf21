#!/usr/bin/env python3
"""
财务数据检索器
专门用于检索财务报告中的关键数据
"""

import re
from typing import List, Dict, Any, Optional
from .logger import get_logger


class FinancialDataRetriever:
    """财务数据检索器"""
    
    def __init__(self):
        self.logger = get_logger()
        
        # 财务指标关键词映射
        self.financial_keywords = {
            "营业收入": ["营业收入", "营业总收入", "主营业务收入", "收入"],
            "净利润": ["净利润", "归属于上市公司股东的净利润", "归属于母公司所有者的净利润"],
            "总资产": ["总资产", "资产总计", "资产合计"],
            "净资产": ["净资产", "所有者权益", "股东权益", "归属于上市公司股东的所有者权益"],
            "每股收益": ["每股收益", "基本每股收益", "稀释每股收益"],
            "现金流": ["现金流量净额", "经营活动产生的现金流量净额", "现金流"]
        }
    
    def search_financial_data(self, metadata: List[Dict[str, Any]], 
                            query: str, company_name: str = None) -> List[Dict[str, Any]]:
        """
        搜索财务数据
        
        Args:
            metadata: 文档元数据列表
            query: 查询内容
            company_name: 公司名称
            
        Returns:
            List[Dict[str, Any]]: 匹配的文档列表
        """
        # 提取查询中的财务指标
        target_indicators = self._extract_financial_indicators(query)
        
        # 提取查询中的数值
        target_numbers = self._extract_numbers(query)
        
        self.logger.info(f"财务数据检索: 指标={target_indicators}, 数值={target_numbers}, 公司={company_name}")
        
        # 搜索匹配的文档
        matched_docs = []
        
        for i, doc in enumerate(metadata):
            content = doc.get('content', '')
            doc_info = doc.get('document_info', '')
            
            # 公司名称过滤
            if company_name and not self._company_matches(doc_info, content, company_name):
                continue
            
            # 计算匹配分数
            score = self._calculate_financial_match_score(
                content, target_indicators, target_numbers
            )
            
            if score > 0:
                doc_copy = doc.copy()
                doc_copy['financial_match_score'] = score
                doc_copy['index'] = i
                matched_docs.append(doc_copy)
        
        # 按匹配分数排序
        matched_docs.sort(key=lambda x: x['financial_match_score'], reverse=True)
        
        self.logger.info(f"找到 {len(matched_docs)} 个匹配的财务文档")
        
        return matched_docs
    
    def _extract_financial_indicators(self, query: str) -> List[str]:
        """提取查询中的财务指标"""
        indicators = []
        query_lower = query.lower()
        
        for indicator, keywords in self.financial_keywords.items():
            for keyword in keywords:
                if keyword in query or keyword.lower() in query_lower:
                    indicators.append(indicator)
                    break
        
        return indicators
    
    def _extract_numbers(self, query: str) -> List[str]:
        """提取查询中的数值"""
        # 匹配各种数字格式
        patterns = [
            r'\d{1,3}(?:,\d{3})*(?:\.\d+)?',  # 带逗号的数字
            r'\d+\.\d+',  # 小数
            r'\d+',  # 整数
        ]
        
        numbers = []
        for pattern in patterns:
            matches = re.findall(pattern, query)
            numbers.extend(matches)
        
        return numbers
    
    def _company_matches(self, doc_info: str, content: str, company_name: str) -> bool:
        """检查文档是否匹配指定公司"""
        # 公司名称映射
        company_mappings = {
            "伊利": "内蒙古伊利实业集团股份有限公司",
            "伊利集团": "内蒙古伊利实业集团股份有限公司",
            "美的": "美的集团股份有限公司",
            "美的集团": "美的集团股份有限公司",
            "茅台": "贵州茅台酒股份有限公司",
            "贵州茅台": "贵州茅台酒股份有限公司",
        }
        
        full_company_name = company_mappings.get(company_name, company_name)
        
        return (full_company_name in doc_info or 
                full_company_name in content or
                company_name in doc_info or
                company_name in content)
    
    def _calculate_financial_match_score(self, content: str, 
                                       target_indicators: List[str], 
                                       target_numbers: List[str]) -> float:
        """计算财务匹配分数"""
        score = 0.0
        content_lower = content.lower()
        
        # 检查财务指标匹配
        for indicator in target_indicators:
            keywords = self.financial_keywords.get(indicator, [])
            for keyword in keywords:
                if keyword in content or keyword.lower() in content_lower:
                    score += 1.0
                    break
        
        # 检查数值匹配
        for number in target_numbers:
            if number in content:
                score += 2.0  # 数值匹配权重更高
        
        # 检查是否是表格数据（通常包含更多财务信息）
        if '|' in content and ('---' in content or '项目' in content):
            score += 0.5
        
        # 检查是否包含财务关键词
        financial_terms = ['万元', '亿元', '元', '增长', '下降', '同比', '环比']
        for term in financial_terms:
            if term in content:
                score += 0.1
        
        return score
    
    def get_best_financial_matches(self, metadata: List[Dict[str, Any]], 
                                 query: str, company_name: str = None, 
                                 top_k: int = 5) -> List[Dict[str, Any]]:
        """
        获取最佳财务数据匹配
        
        Args:
            metadata: 文档元数据列表
            query: 查询内容
            company_name: 公司名称
            top_k: 返回结果数量
            
        Returns:
            List[Dict[str, Any]]: 最佳匹配的文档列表
        """
        matched_docs = self.search_financial_data(metadata, query, company_name)
        
        # 返回前top_k个结果
        return matched_docs[:top_k]
    
    def format_financial_results(self, results: List[Dict[str, Any]]) -> str:
        """格式化财务检索结果"""
        if not results:
            return "未找到匹配的财务数据。"
        
        formatted_results = []
        
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            source = result.get('source', '')
            score = result.get('financial_match_score', 0)
            
            formatted_results.append(
                f"[财务数据 {i}] 来源: {source}, 匹配分数: {score:.2f}\n"
                f"内容: {content[:300]}...\n"
            )
        
        return "\n".join(formatted_results)
