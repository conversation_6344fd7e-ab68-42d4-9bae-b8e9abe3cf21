#!/usr/bin/env python3
"""
文件解析和保存工具
解析指定文件并保留所有中间结果和最终结果
"""

import sys
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.simplified_document_processor import SimplifiedDocumentProcessor
from src.parsing_result_saver import get_result_saver


def parse_and_save_file(file_path: str, force_reparse: bool = False) -> str:
    """
    解析文件并保存所有结果
    
    Args:
        file_path: 文件路径
        force_reparse: 是否强制重新解析
        
    Returns:
        str: 文件ID
    """
    print(f"🔍 开始解析文件: {file_path}")
    print("=" * 60)
    
    file_path = Path(file_path)
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    # 检查是否已经解析过
    result_saver = get_result_saver()
    
    if not force_reparse:
        # 检查是否已有解析结果
        parsed_files = result_saver.list_parsed_files()
        existing_file = None
        
        for parsed_file in parsed_files:
            if parsed_file.get('original_path') == str(file_path):
                existing_file = parsed_file
                break
        
        if existing_file:
            print(f"⚠ 文件已解析过:")
            print(f"   文件ID: {existing_file['file_id']}")
            print(f"   解析时间: {existing_file['parsing_timestamp']}")
            print(f"   总片段数: {existing_file['total_chunks']}")
            
            if not force_reparse:
                choice = input("是否重新解析? (y/N): ").strip().lower()
                if choice != 'y':
                    print("使用现有解析结果")
                    return existing_file['file_id']
    
    # 开始解析
    start_time = datetime.now()
    print(f"🚀 开始解析 ({start_time.strftime('%Y-%m-%d %H:%M:%S')})")
    
    try:
        # 初始化简化文档处理器
        processor = SimplifiedDocumentProcessor()
        
        # 解析文件（启用结果保存）
        chunks = processor.process_file(str(file_path), save_results=True)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n✅ 解析完成!")
        print(f"   耗时: {duration:.2f} 秒")
        print(f"   生成片段: {len(chunks)} 个")
        
        # 分析片段类型
        chunk_types = {}
        enhanced_count = 0
        
        for chunk in chunks:
            chunk_type = chunk.get('type', 'unknown')
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
            
            if chunk.get('semantic_enhanced'):
                enhanced_count += 1
        
        print(f"   片段类型分布:")
        for chunk_type, count in sorted(chunk_types.items()):
            print(f"     {chunk_type}: {count} 个")
        
        print(f"   语义增强片段: {enhanced_count} 个")
        
        # 获取文件ID
        parsed_files = result_saver.list_parsed_files()
        current_file = None
        
        for parsed_file in parsed_files:
            if parsed_file.get('original_path') == str(file_path):
                current_file = parsed_file
                break
        
        if current_file:
            file_id = current_file['file_id']
            print(f"   文件ID: {file_id}")
            return file_id
        else:
            print("⚠ 无法获取文件ID")
            return None
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        raise e


def show_parsing_results(file_id: str):
    """
    显示解析结果概览
    
    Args:
        file_id: 文件ID
    """
    print(f"\n📊 解析结果概览 (文件ID: {file_id})")
    print("=" * 50)
    
    result_saver = get_result_saver()
    results = result_saver.load_parsing_results(file_id)
    
    if not results:
        print("❌ 未找到解析结果")
        return
    
    # 显示元数据
    metadata = results.get('metadata', {})
    print(f"📄 文件信息:")
    print(f"   原始路径: {metadata.get('original_path')}")
    print(f"   文件名: {metadata.get('file_name')}")
    print(f"   文件类型: {metadata.get('file_type')}")
    print(f"   文件大小: {metadata.get('file_size', 0) / 1024 / 1024:.2f} MB")
    print(f"   解析时间: {metadata.get('parsing_timestamp')}")
    
    # 显示片段统计
    chunks = results.get('chunks', [])
    if chunks:
        print(f"\n📊 片段统计:")
        print(f"   总片段数: {len(chunks)}")
        
        chunk_types = {}
        enhanced_count = 0
        total_chars = 0
        
        for chunk in chunks:
            chunk_type = chunk.get('type', 'unknown')
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
            
            if chunk.get('semantic_enhanced'):
                enhanced_count += 1
            
            total_chars += len(chunk.get('content', ''))
        
        print(f"   类型分布:")
        for chunk_type, count in sorted(chunk_types.items()):
            print(f"     {chunk_type}: {count} 个")
        
        print(f"   语义增强片段: {enhanced_count} 个")
        print(f"   总字符数: {total_chars:,}")
    
    # 显示语义增强统计
    enhanced_data = results.get('enhanced', {})
    if enhanced_data:
        enhanced_items = enhanced_data.get('enhanced_data', [])
        print(f"\n🤖 语义增强统计:")
        print(f"   增强项目数: {len(enhanced_items)}")
        print(f"   保存时间: {enhanced_data.get('saved_timestamp')}")


def list_all_parsed_files():
    """列出所有已解析的文件"""
    print("📋 已解析文件列表")
    print("=" * 60)
    
    result_saver = get_result_saver()
    parsed_files = result_saver.list_parsed_files()
    
    if not parsed_files:
        print("暂无已解析的文件")
        return
    
    for i, file_info in enumerate(parsed_files, 1):
        print(f"{i}. {file_info.get('file_name', 'Unknown')}")
        print(f"   文件ID: {file_info.get('file_id')}")
        print(f"   路径: {file_info.get('original_path')}")
        print(f"   类型: {file_info.get('file_type')}")
        print(f"   解析时间: {file_info.get('parsing_timestamp')}")
        print(f"   片段数: {file_info.get('total_chunks', 0)}")
        print()


def show_storage_stats():
    """显示存储统计信息"""
    print("📊 存储统计信息")
    print("=" * 40)
    
    result_saver = get_result_saver()
    stats = result_saver.get_storage_stats()
    
    print(f"总解析文件数: {stats.get('total_files', 0)}")
    print(f"原始内容文件: {stats.get('raw_content_files', 0)}")
    print(f"表格数据文件: {stats.get('table_files', 0)}")
    print(f"语义增强文件: {stats.get('enhanced_files', 0)}")
    print(f"最终片段文件: {stats.get('chunk_files', 0)}")
    print(f"存储路径: {stats.get('storage_path')}")
    print(f"最后更新: {stats.get('last_updated')}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文件解析和保存工具")
    parser.add_argument("file_path", nargs="?", help="要解析的文件路径")
    parser.add_argument("--force", "-f", action="store_true", help="强制重新解析")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有已解析文件")
    parser.add_argument("--stats", "-s", action="store_true", help="显示存储统计信息")
    parser.add_argument("--show", help="显示指定文件ID的解析结果")
    
    args = parser.parse_args()
    
    try:
        if args.list:
            list_all_parsed_files()
        elif args.stats:
            show_storage_stats()
        elif args.show:
            show_parsing_results(args.show)
        elif args.file_path:
            file_id = parse_and_save_file(args.file_path, args.force)
            if file_id:
                show_parsing_results(file_id)
        else:
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n⏹ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
