## 1.  介绍

面试官,你好！
        首先非常感谢贵公司给我提供的这次面试机会，我叫王志扬，21年毕业于保定学院物联网工程专业。

## 2.  工作

21年毕业后，我先后在两家公司担任NLP算法工程师，专注于自然语言处理工作;

在**北京仁真电子商务有限公司**期间,我主要的职责包括:

参与设计和构建文本的分类模型，文本分类准确率提升了10%；

随后,我在**深圳知鸟教育科技有限公司**继续从事相关的工作；

由于业务的需求，参与设计和构建了一个知识图谱，；

设计并部署了一个保险类问题自动分类系统，极大地提高了保险类问题分类的处理效率；

与团队合作，微调了一个保险类的模型用来支持RAG系统的问答 ；

近期，和团队开发了一个AI导师的项目，构建一个知识库，然后基于这个 知识库进行RAG 问答。

那么以上就是本人的基本情况介绍，谢谢。

## 3.  项目

### MyRAG项目

文档上传和转换阶段

- 系统支持多种格式的输入文件（PDF、PPT、DOC等）

- 通过专门的转换器（FileConverter）将不同格式统一转换为增强型Markdown

- 每种格式都有其特定的转换器实现（PDFConverter, PPTXConverter, DOCXConverter）

- 转换过程会保留文档的结构信息、图片、表格等多模态内容

AST解析与语义增强阶段

- 将转换后的Markdown文档解析成抽象语法树（AST）

- 通过parse_markdown函数分析文档的层次结构

- 提取文档中的语义关系和结构信息

- 保留标题层级、列表关系等文档结构

智能文档切分阶段

- 使用split_document函数将AST切分成合适大小的文档块

- 切分时考虑语义完整性和文档结构

- 每个文档块包含：

- 唯一ID

- 块类型

- 内容

- 结构信息

向量化与索引阶段

- 使用generate_embeddings为每个文档块生成向量表示

- 支持多种向量化方法：

- "simple"：基础向量化

- "transformer"：基于预训练模型的向量化

- 通过index_documents函数将向量存入检索系统

- 支持两种检索器类型：

- "simple"：纯向量检索

- "hybrid"：混合检索（结合向量和关键词）

检索服务阶段

- 通过search_documents函数提供检索服务

- 支持参数：

- top_k：返回结果数量

- method：检索方法

- alpha：混合检索权重（向量vs关键词）

- 返回相关度最高的文档块



### RAG

**知识库构建流程**

1. 新建知识库

   - 用户先通过接口（如 /local_doc_qa/new_knowledge_base）新建知识库，生成唯一 kb_id 和 kb_name。

2. 上传文档

   - 用户上传各种类型的文档到知识库。
   - 系统自动识别文档类型，采用对应的解析和切分策略。

3. 文档解析与切分

   - 针对不同文档类型有不同的切分逻辑，核心目标是保证 chunk 的语义完整性和上下文连贯性。
   - 解析与切分后，结果会入库，后续问答直接基于这些切分块（chunk）进行检索。

   **如何结构化解析：**

   1. 基本单元

   - chunk 最初是以 PDF 解析服务返回的 block、paragraph（段落）为基本单元进行切分的。
   - 一个 block 可能包含多个段落（para），每个段落会被抽取出文本和位置信息。
     这类服务会先做版面分析（Layout Analysis），对每一页自动分割出“区块”（block），并识别每个区块的属性（正文、标题、表格、图片、脚注、参考文献等）。 结构化解析后，通常会返回如下 JSON 层级结构。(PaddleOCR 的 ppstructure、百度文档解析、youdao split_parse)

   2. 语义完整性与最小长度

   - 对于太短的段落（token 数小于 200，见 para_too_short_and_need_merge），会自动与前面的段落合并，保证 chunk 语义完整且不至于太短。
   - 合并时不仅拼接文本，还会合并位置信息。

   3. 合并策略

   - merge_conj_short_paras 函数会遍历所有初步切分的段落，再次把相邻的短段落合并，进一步提升 chunk 的连贯性。

   4. 多级粒度（后处理）

   - 在 save_chunks_to_db 时，除了基本 chunk，还会生成更大粒度的“虚拟chunk”（把多个小chunk合并，限制总长度），以及更小粒度的“sub-chunk”（把chunk按句子再细分），以适应不同检索需求。

   5. 特殊内容处理

   - 标题（TITLE block）会被标记为 "title" 类型，其余为 "normal"。
   - 公式、图片等 block 有专门处理方法，但一般不会参与普通 chunk 的摘要和问答。
   - REFERENCE（参考文献）等非正文内容会被跳过。

   6. 切分目标

   - 最终 chunk 的目的是：既不能太短（信息量太少，语义割裂），也不能太长（不利于后续 embedding/摘要），并最大限度保持原文档逻辑和结构。

     

QAnything 系统检索和生成答案的流程如下：

1. **检索阶段**

- 用户输入问题后，系统首先根据问题和所选知识库（kb_ids）进行检索，主要包括：
  - FAQ 完全匹配：如果知识库中有 FAQ 问题与用户问题完全一致，直接返回对应答案。
  - 否则，采用多路检索（如向量检索、全文检索），从知识库相关文档中筛选出高相关内容（source_documents），数量受 top_k 限制。
  - 检索结果还会去重、可选 rerank（重排序），保证相关性更高的内容排前。

1. **生成阶段**

- 如果检索到 FAQ 完全匹配，直接输出答案。
- 否则，将检索到的 source_documents 以 prompt 形式（可带自定义模板）拼接，连同历史对话、用户问题一起输入大模型（如 OpenAI LLM）。
- 大模型根据这些内容生成最终答案。
- 若检索结果含图片，系统会关联图片内容，最终生成图文混排回答。
- 生成过程可流式输出，边生成边返回给前端。

1. **结果返回**

- 系统最终返回包括生成的答案、引用的知识片段、相关检索内容等结构化数据。
- 同时记录一条完整的问答日志，便于后续追溯和分析。

核心逻辑简述：FAQ优先→知识检索→拼Prompt进大模型→生成答案→返回结构化结果。



### 知识图谱

下面给您讲一下我最近做的一个项目：**历史知识问答系统**

**图数据库scheme**

构建的知识图谱**一万五千**多节点左右的节点，边大概有**二万三千多**条边；

本知识图谱包含**10多种类型**的**一万五千**个实体和**341种类型**的**二万三千多**条关系及其属性。 其中，**实体类型**有：历史人物、历史事件、地点、战争、时间、权利机构、作品、历史时期、法律等确定的类型，对于其他实体个数较少的类型全部归到“其他”这一类别。 **关系的类型**包括平定、父子、修订、出版物、朋友、发生于、参与者、创建了、领导了、属于、影响、画作、源自等，用以描述两个实体之间的关系。

**3.1架构**

该项目的架构分为**三个主要部分**：**数据收集与预处理、模型构建与微调、以及模型评估**。在数据收集与预处理中，收集了中学历史教材、辅助教材等数据，并进行了文本清洗和格式标准化。接着，选择ChatGLM3-6B-Base作为基础模型，并通过LoRA技术进行微调，使用训练集训练模型，并通过验证集调整参数，最后使用测试集进行性能评估，重点计算信息抽取任务的F1值。在模型部署阶段，首先优化模型性能，随后将其部署到生产服务器上。

1. **数据收集**:

   - 收集高中历史教材、历史试题库数据。

   - 训练集15000条，验证集2420条，测试集2466条。

     构建的知识图谱**一万五千**多节点左右的节点，边大概有**四万三千**条边；

     本知识图谱包含**10多种类型**的**一万五千**个实体和**341种类型**的**四万三千**条关系及其属性。 其中，**实体类型**有：历史人物、历史事件、地点、战争、时间、权利机构、作品、历史时期、法律等确定的类型，对于其他实体个数较少的类型全部归到“其他”这一类别。 **关系的类型**包括平定、父子、修订、出版物、朋友、发生于、参与者、影响了、创建了、领导了、属于、影响、画作、源自等，用以描述两个实体之间的关系。

2. **数据预处理**

   - **文本清洗**

     - **去除噪声数据**：删除数据中的无用内容，如版权声明、页眉页脚、脚注、页面编号等。

     ​	**去除特殊字符和格式**：清理非文本字符（如换行符、制表符等）。使用正则表达式（Regex）**去除多余的空格、标点符号等。**

   - **数据标准化：** 

     - 负责统一文本格式，包括将所有文本**去除停用词和数字格式**，以及处理**多音字**等中文特有的语言现象

   - **数据结构化：**

     - **拆分章节和主题**：将教材内容按章节、主题或知识点进行分割，保持内容的逻辑结构。题库数据可以按题型（如选择题、填空题、简答题等）或主题进行分割。

   - **人工标注数据**：文本+spo三元组，以适应模型的输入

3. **数据的输入**

   - 每一条数据都分为 `context` 和 `target` 两部分：

     > 1. `context` 部分是**接受用户的输入**。2.`target` 部分用于**指定模型的输出json格式的spo三元组**。
     >
     > `context` 中又包括 2 个部分：
     >
     > 1. Instruction：**告诉模型现在需要做「阅读理解」任务**，就是当前执行什么样的任务；当需要一个模型同时解决多个任务时可以设定不同的指令。
     > 2. Input：**告知模型要抽取的句子以及输出的格式**，例如：找到句子中的三元组信息并输出成json格式给我:\n\n公元前 230—前 221 年，秦国采取远交近攻策略，分化瓦解，各个击破，相继灭掉东方六国，建立起第一个统一王朝—秦朝，定都咸阳。
     >
     > `target`包括1个部分：**spo三元组**
     >
     > ```python
     > {"context": 
     > "Instruction: 你现在是一个很厉害的阅读理解器，严格按照人类指令进行回答。\n
     > Input: 找到句子中的三元组信息并输出成json给我:\n\n公元前 230—前 221 年，秦国采取远交近攻策略，分化瓦解，各个击破，相继灭掉东方六国，建立起第一个统一王朝—秦朝，定都咸阳。 ", 
     > 
     > "target": "```json\n[{\"predicate\": \"采取\", \"object_type\": \"国家\", \"subject_type\": \"策略\", \"object\": \"秦国\", \"subject\": \"远交近攻\"}]\n```"}
     > ```

4. **基础模型选择**:

   - 选择ChatGLM3-6B-Base作为基础模型。

5. **模型微调**:

   - LoRA技术应用
     - 利用LoRA技术对模型参数进行低秩矩阵分解。
     - 设置梯度检查点，提高训练效率。
   - 模型训练
   - 优化策略：学习率预热，调整批量大小，梯度裁剪，梯度检查点，累计梯度更新。
     - 使用训练集进行模型训练。
     - 利用验证集调整模型参数。

6. **模型评估**:

   - 使用测试集评估模型性能。

   - 计算信息抽取任务的F1值91.5%。

   - 人工评估主要是抽的是否少了,是否完整,抽的语义是否符合逻辑

     


**知识图谱的核心要素**

1. **节点 (Nodes)：** 每个节点代表一个具体的实体，如事件、人物、地点、时间、概念等。
2. **边 (Edges)：** 边表示节点之间的关系，例如时间顺序、因果关系、关联等。
3. **属性 (Attributes)：** 节点和边可以有属性，比如时间节点可以有“年份”，事件节点可以有“地点”等。
4. **类别 (Types)：** 每个节点和边有其类别，如“历史人物”、“历史事件”、“地理位置”、“时间”等。

**历史教材知识图谱的Scheme 设计** 

1. **节点类型**（Node Types）

- **历史事件 (Historical Event)**
  - 属性：事件名称、日期、地点、涉及人物、重要性（等级）、事件描述、相关链接（参考文献、图片、文献资料等）
- **历史人物 (Historical Figure)**
  - 属性：姓名、生卒年月、国籍、身份（如：皇帝、将军、科学家）、主要贡献、相关事件
- **时间节点 (Time)**
  - 属性：年份、世纪、时间段
- **地理位置 (Location)**
  - 属性：地点名称、地理坐标、当前国家/区域、历史背景
- **历史概念 (Historical Concept)**
  - 属性：概念名称、定义、相关事件、发展历程
- **文献资料 (Document)**
  - 属性：标题、作者、出版时间、链接、相关事件或人物

2.**边类型**（Edge Types）

- **事件-人物 (Event-Figure)**
  - 关系：参与 (Participated In)、领导 (Led)、被影响 (Influenced By)
- **事件-时间 (Event-Time)**
  - 关系：发生于 (Occurred In)、结束于 (Ended In)
- **事件-地点 (Event-Location)**
  - 关系：发生在 (Occurred At)
- **人物-地点 (Figure-Location)**
  - 关系：出生于 (Born In)、死亡于 (Died In)、统治 (Ruled Over)
- **事件-事件 (Event-Event)**
  - 关系：因果关系 (Causes)、结果关系 (Results In)、同时发生 (Coincides With)
- **概念-事件 (Concept-Event)**
  - 关系：相关于 (Related To)、引发了 (Led To)
- **概念-概念 (Concept-Concept)**
  - 关系：继承 (Derived From)、相似 (Similar To)、对立 (Opposed To)

3. **可能的查询和用途**

- 查询某一历史事件的起因和结果。
- 查询某一历史人物参与的所有事件及其在这些事件中的角色。
- 查询特定地点发生的所有重要历史事件。
- 查询某一历史概念的发展历程及其相关事件和人物。
- 可视化时间轴上事件和人物的关系，以展示历史的演进过程。

示例：**部分知识图谱实例**

假设我们要表示中国历史上的一个著名事件——**辛亥革命（1911年）**，该事件的知识图谱结构可能如下：

- **节点：**
  - **历史事件：辛亥革命**
    - 属性：日期：1911年10月10日、地点：武昌、影响：推翻清朝统治
  - **历史人物：孙中山**
    - 属性：生卒：1866-1925、身份：革命领袖、国籍：中国
  - **地点：武昌**
    - 属性：当前国家：中国
  - **时间：1911年**
    - 属性：年份：1911
  - **历史概念：革命**
    - 属性：定义：一个国家或地区的社会政治制度的根本变革
- **边：**
  - **辛亥革命** - 参与 - **孙中山**
  - **辛亥革命** - 发生在 - **武昌**
  - **辛亥革命** - 发生于 - **1911年**
  - **辛亥革命** - 相关于 - **革命**

通过这样的知识图谱设计，我们可以更直观地理解和呈现复杂的历史关系和信息。这样既可以用作教学辅助工具，也可以作为学习者自我探索历史知识的导航。



### **人工标注数据集**

**标注工具：Label Studio、doccano**

**人工评估的步骤**

1. **制定评估标准**:

- **准确性 (Precision)**：从系统中提取的三元组中有多少是正确的。
- **召回率 (Recall)**：从所有正确的三元组中有多少被系统成功提取。
- **F1-score**：准确性和召回率的调和平均数，用于衡量系统的整体表现。
- **正确性 (Correctness)**：抽取出的三元组是否符合实际语义和文本内容。
- **覆盖率 (Coverage)**：抽取出的三元组是否全面覆盖了文本中的关键信息。

2. **随机抽样**

   从模型抽取出的SPO三元组中随机抽取一部分数据，确保样本具有代表性。这部分样本数据可以用作人工评估的输入。

3. **创建评估模板**

- 原始文本：原始的待抽取信息的文本。
- 抽取结果：模型抽取出的SPO三元组。
- 评估选项：例如“正确”、“部分正确”（需要更正）、或“错误”。



**人工标注的后优化**

**1、分析badcase**，修复数据：根据**模型的预测结果以及人工实际标注结果的dif数据**，总结dif数据类型，然后查验训练集中该部分dif类型数据是否错误。

举例说明：如想要识别“北京”为地点词，测试集人工标注为地点，但是模型预测为人名，这时就产生了dif数据，需要去查验训练集中的“北京”一词是否标记为人名，如果是就更改，如果不是，那么可能导致的原因为，模型样本数据不均衡或者该类数据欠缺，进行一个补充即可

**2、结果后处理**：这种情况一般出现在，**训练集数据标注没问题，但是预测结果就是错误的，无法通过模型来修改，此时可以利用后处理手段来辅助模型的预测结果，可以构造一个词典，将数据强制转化为正确的**。

如：“北京”一词，在训练集中都是地点词，但是模型结果识别为人名，此时，可以构造一个地址词典，将北京等词放进词典中，然后对模型识别结果进行匹配，如果匹配到结果在词典中，那么我们**强制将该数据转化为地点**。



**加快获取有效数据集：半监督（自训练）方式**

1、首先人工标注1w条数据集（人工标注的数据我们一般认为准确率为100%），将标注好的数据集，分为训练集8000和测试集2000。

2、利用8000训练集训练我们的基础模型如：bilstm+crf，根据训练好的模型去预测测试集，**对比和人工标注的结果dif(差异)**，如果准确率为96%，可以忽略dif，我们认为该模型效果不错，直接使用训练好的模型，接下来第3步

3、利用第二步训练好的模型，去预测新一批数据（该部分数据为非标注数据），如新选取1w条，此时经过模型预测后，1w条新的数据已打上标签

4、将第3步得到新的1w条数据和之前的8000人工标注数据合并，再次送入模型中训练，训练好的模型去预测2000条测试集，**对比和人工标注的结果dif(差异)**，此时准确率可能为94%，如果我们定义模型准确率指标为95%为，那么此时训练集中引入了噪声，存在脏数据（也就是实体类别有标错的数据），可以根据2000条测试集的dif数据，**人工筛选实体数据错误类型，对应查找训练集的数据，然后修改，修改完后重复4步骤**，发现模型准确率为95%,即达标，然后再重复3-4步骤，直到数据为20w条

5、最后注意：保持2000条测试集不变，用于模型评估。

在自训练过程中，保持8000条人工标注数据在训练集中不变，不断扩充高置信度的新伪标注数据加入训练集。

**示例流程**

1. **初始标注**：
   - 人工标注1万条数据，分成8000条训练集和2000条测试集。
2. **模型训练与评估**：
   - 使用8000条数据训练模型。
   - 评估模型在2000条测试集上的表现。
3. **迭代训练**：
   - 使用模型预测新的1万条非标注数据，并打上标签。
   - 将新打标签的数据与原有的8000条数据合并，形成新的训练集。
   - 从新的训练集中随机选取1000条作为验证集。
   - 用剩下的数据继续训练模型，并在验证集上监控过拟合。
   - 评估模型在2000条测试集上的表现。
4. **重复迭代**：
   - 根据验证集的表现调整模型，并继续重复上述过程，直到达到预期的准确率。
   - 保持测试集不变，确保评估的一致性和公平性。





### **服务器的配置**

------

整个项目使用的服务器一般配置：2张24G显存的4090显卡

**Batch size（批次大小）**：假设每个 GPU 的批次大小为 16。

**Epochs（训练轮数）**：假设微调过程中运行 5 个 epoch。也试过10个，20个。

数据为15000条，然后设置梯度检查点，使用了学习率预热,线性下降的退火等策略，使用deepseed框架zero-1训练，以及用vllm框架推理。**FlashAttention-2** 通过**极致的内存优化和并行计算策略**。

### **训练阶段（微调）**

**1. 显存占用**

| **组件**                 | **LoRA（FP16）** | **QLoRA（4-bit量化）** | **备注**                                                     |
| ------------------------ | ---------------- | ---------------------- | ------------------------------------------------------------ |
| **基础模型参数**         | ~12 GB           | ~3 GB                  | ChatGLM3-6B参数，FP16（2字节/参数） vs 4-bit量化（0.5字节/参数） |
| **适配器参数（LoRA）**   | ~0.1 GB          | ~0.1 GB                | 低秩矩阵（秩=8，仅训练0.1%参数）                             |
| **梯度**                 | ~0.1 GB          | ~0.1 GB                | 仅适配器参数梯度（FP16）                                     |
| **优化器状态（ZeRO-1）** | ~0.3 GB          | ~0.3 GB                | Adam优化器状态分片（FP32参数+动量/方差，ZeRO-1分片至2卡）    |
| **激活值（梯度检查点）** | ~3 GB            | ~3 GB                  | 梯度检查点减少激活值显存占用（约50%降低）                    |
| **其他（临时缓存）**     | ~2 GB            | ~2 GB                  | 序列长度相关（输入/输出张量、中间计算缓存）                  |
| **总计/GPU**             | **~17.5 GB**     | **~8.5 GB**            | QLoRA显存需求显著降低，单卡24GB显存足够。                    |

**2.训练时间**

| **Epochs** | **LoRA**               | **QLoRA**              |
| ---------- | ---------------------- | ---------------------- |
| 5 epochs   | 5×469×1.1 ≈ **43分钟** | 5×469×1.4 ≈ **55分钟** |
| 10 epochs  | ~86分钟                | ~110分钟               |
| 20 epochs  | ~172分钟               | ~220分钟               |

### **推理阶段（vLLM框架）**

**1. 显存占用**

- **模型加载**：vLLM支持动态显存管理（PagedAttention）：
  - **FP16模型**：~12 GB（单卡加载完整模型）。
  - **4-bit量化模型**：~6 GB（量化+内存优化）。
- **推理Batch Size**：
  - 输入序列长度512，输出128，单卡可支持**~80并发请求**（显存占用约18-20GB）。

**2. 推理速度**

- **吞吐量**（RTX 4090单卡）：
  - **FP16**：~45 tokens/秒（batch size=16）。
  - **4-bit量化**：~70 tokens/秒（计算效率提升）。
- **时延**（单个请求）：
  - 生成128 tokens：FP16约2.8秒，量化约1.8秒。



### 微调时遇到的问题以及解决方案

**一、计算资源问题**

**问题：**

1. **显存（GPU Memory）不足：** 大模型参数量巨大，即使是微调也可能因为模型尺寸、批量大小（batch size）、序列长度等因素导致显存溢出（OOM）。
2. **训练速度慢：** 微调过程仍然计算密集，尤其是在大数据集上，可能耗时过长。
3. **计算资源昂贵：** 租赁高性能GPU的成本较高。

**解决方案：**

1. 显存优化：
   - **减小批量大小（Batch Size）：** 最直接的方法，但可能影响模型性能和收敛性。
   - **梯度累积（Gradient Accumulation）：** 通过多次小批量的前向和反向传播来累积梯度，模拟大批量训练的效果，同时不增加显存占用。
   - **混合精度训练（Mixed Precision Training）：** 使用FP16/BF16代替FP32进行大部分计算，显著减少显存占用和加速计算。
   - **激活检查点（Activation Checkpointing）：** 在反向传播时重新计算某些层的激活值，而不是在内存中一直存储，以节省显存。缺点是增加了计算时间。
   - **优化器状态分片（Optimizer State Sharding）：** 将优化器的状态（如AdamW的动量和方差）分散存储在不同的GPU上，如ZeRO系列优化器 (DeepSpeed ZeRO-1/2/3)。
   - **模型并行（Model Parallelism）/张量并行（Tensor Parallelism）/FSDP (Fully Sharded Data Parallel)：** 在模型参数量过大无法单GPU存放时使用。将模型的不同层或层内的张量分配到不同的GPU上。FSDP是更先进的数据并行形式，它不仅分片梯度和优化器状态，还分片模型参数，能显著减少显存占用。
   - **更短的序列长度：** 适当缩短输入序列长度。
2. 加速训练：
   - **高效的数据加载：** 使用多进程加载（`num_workers`）、数据预取、内存映射文件等。
   - **FlashAttention：** 一种更高效的Attention计算方式，能显著加速Transformer模型的训练和推理，并减少显存占用。
   - **更快的互联：** 使用InfiniBand等高速网络，减少分布式训练中的通信开销。
3. 成本控制：
   - **按需租赁：** 云服务商（AWS, GCP, Azure）提供按需或竞价实例。
   - **参数高效微调（PEFT）方法：** 显著降低微调所需的计算资源和存储，如下所述。

**二、灾难性遗忘与泛化性问题**

**问题：**

1. **灾难性遗忘（Catastrophic Forgetting）：** 在微调过程中，模型可能会“遗忘”在预训练阶段学到的通用知识，导致在与微调任务不相关的能力上性能下降。
2. **过拟合（Overfitting）：** 在小规模的特定任务数据集上微调时，模型可能过度拟合训练数据，导致在未见过的数据上泛化能力差。

**解决方案：**

1. 参数高效微调（Parameter-Efficient Fine-Tuning, PEFT）：
   - **LoRA (Low-Rank Adaptation)：** 冻结预训练模型的绝大部分参数，只在Transformer的某些层中注入小的、可训练的低秩适配器（Adapter）。显著减少了可训练参数量，减轻了灾难性遗忘，并降低了计算和存储需求。
   - **Prompt Tuning/Prefix Tuning：** 冻结大模型所有参数，只优化一小段可学习的“软提示”（soft prompt）或前缀（prefix），将其与输入拼接。
   - **Adapter Tuning：** 在预训练模型的固定层之间插入小型、可训练的神经网络模块（Adapter）。
   - **优点：** PEFT方法训练速度快、计算资源需求低、有效缓解灾难性遗忘、每个任务只存储少量额外参数。
2. **多任务学习（Multi-task Learning）：** 在微调时，同时训练模型在多个相关任务上的能力，这有助于模型学习更通用的表示，减少过拟合。
3. **数据增强（Data Augmentation）：** 增加微调数据集的多样性和规模，例如通过回译、同义词替换等方式。
4. 正则化（Regularization）：
   - **权重衰减（Weight Decay）：** 惩罚大权重值，防止过拟合。
   - **Dropout：** 在训练时随机失活神经元，增加模型的鲁棒性。
5. **早停（Early Stopping）：** 监控验证集上的性能，当性能不再提升时停止训练，避免过拟合。
6. **学习率调度（Learning Rate Scheduling）：** 使用较小的学习率进行微调，并采用预热（warmup）和衰减（decay）策略，有助于稳定训练并防止过拟合。

**三、数据相关问题**

**问题：**

1. **数据质量差：** 微调数据集可能包含噪声、错误标签、重复数据或不一致的格式。
2. **数据量小：** 对于特定任务，高质量的标注数据往往难以获取，导致数据量不足。
3. **数据分布不匹配（Domain Shift）：** 微调数据与预训练数据或实际应用数据存在分布差异。

**解决方案：**

1. 数据清洗与预处理：
   - **人工审核：** 对关键样本进行人工检查和校正。
   - **自动化清洗：** 使用启发式规则、去重算法、低质量文本过滤等。
   - **统一格式：** 将数据转化为模型期望的格式。
2. 数据增强（Data Augmentation）：
   - **文本层面：** 同义词替换、随机插入/删除/替换词、回译（Translate-Back-Translate）、随机打乱句子顺序。
   - **策略层面：** Few-shot Learning (少样本学习)、Demonstration Learning (演示学习)。
3. 迁移学习策略：
   - **Adapter Tuning/LoRA等PEFT方法：** 这些方法通过引入少量可训练参数，能够更好地适应小规模数据集，降低过拟合风险。
   - **多阶段微调：** 如果有中间领域数据，可以先在中间领域数据上进行领域适应性微调（Domain-Adaptive Fine-tuning），再在特定任务数据上微调。
4. **高质量数据收集：** 投入资源进行高质量的人工标注。

**四、训练稳定性与收敛问题**

**问题：**

1. **训练不稳定：** 损失值波动大，难以收敛，甚至发散。
2. **收敛速度慢：** 模型学习效率低，需要大量训练步数才能达到好的性能。
3. **超参数敏感：** 微调效果对学习率、优化器等超参数非常敏感。

**解决方案：**

1. 调整学习率：
   - **较小的学习率：** 微调通常使用比预训练时小得多的学习率（例如，预训练可能是1e-4，微调可能是1e-5或2e-5）。
   - **学习率调度器：** 使用`warmup`（学习率从0逐渐增加）结合`cosine decay`（余弦衰减）或`linear decay`（线性衰减）等策略。
   - **学习率搜索：** 使用网格搜索、随机搜索或贝叶斯优化来寻找最佳学习率。
2. **优化器选择：** **AdamW**通常是首选，它结合了Adam的优点并加入了权重衰减。
3. **梯度裁剪（Gradient Clipping）：** 防止梯度爆炸，提高训练稳定性。
4. **增加训练步数：** 在确保不发生过拟合的前提下，适当增加训练步数。
5. **权重初始化：** 使用预训练模型的权重进行初始化，而不是随机初始化。
6. **检查数据：** 确保数据没有严重错误或异常值。
7. **冻结部分层：** 可以先冻结模型的底层（通常学习通用特征），只微调顶层（学习特定任务特征），然后逐渐解冻更多层进行微调。



### 大模型优化技术

**1.autocast  混合精度训练**

PyTorch中一种**混合精度**的技术，可在保持数值精度的情况下提高训练速度和减少显存占用。

它会**自动**将这些**数据**转换为适当的精度，以便在支持的运算符上使用FP16来加速计算，同时在需要更高精度的地方恢复到FP32。

**模型权重也会保持在较高的精度，而计算会在较低精度下执行**。

**2.梯度累积（Gradient Accumulation）**

- **原理**：将多个小批次的梯度累积后统一更新，模拟大批次训练。
-  **梯度累积 (Gradient Accumulation)** 一种通过多次前向和反向传播计算梯度，但不立即执行优化器更新，而是将这些梯度累加起来，直到累积了足够多的小批次（模拟一个大批次）后，再执行一次优化器更新的技术。其主要目的是在**受限于硬件显存无法使用大批次**的情况下，通过累积小批次的梯度来**模拟大批次训练的效果**。

**3.model.gradient_checkpointing_enable() 梯度检查点**

**梯度检查点**是一种优化技术，用于在反向传播过程中降低内存使用

**原理**：在前向传播过程中不保存所有中间激活值，而是在反向传播时重新计算这些值。这样做的好处是可以显著减少所需的GPU内存，代价是可能会稍微增加一些计算时间，因为某些计算需要在反向传播时重新执行。**内存和计算时间之间的权衡**

**4.input.requires_grad_(True)确保模型的输入张量能够支持梯度计算**

在PyTorch中，模型的输入张量默认不计算梯度（`requires_grad=False`），但在某些任务（如对抗训练、输入优化或联合微调嵌入层时）需要启用输入梯度。可以通过手动设置`input.requires_grad_(True)`或自定义前向逻辑实现这一需求。例如，在使用LoRA微调时，若需同时优化输入嵌入（如prompt tuning），则需显式启用输入梯度。

**5.warm_steps = int(warmup_ratio * max_train_steps)  总训练步数乘以一个预热比例**

 **学习率预热 (Learning Rate Warmup)** 指在训练的最开始阶段，**不直接使用预设的初始学习率**，而是从一个非常小的学习率开始，然后根据设定的策略（通常是线性增加）**逐渐将学习率提高到预设的初始学习率**。这个过程称为预热阶段。

**预热阶段**目的是让模型更加平滑地进入训练状态，避免一开始就使用较大的学习率导致训练不稳定。

**6.torch.nn.utils.clip_grad_norm_(model.parameters(), args.max_grad_norm)**  **梯度裁剪**

对模型的所有可训练参数应用梯度裁剪（gradient clipping）。梯度裁剪是一种常用的技巧，用于防止梯度爆炸（gradient explosion），即梯度变得非常大以至于数值上不稳定，可能导致训练过程中的数值溢出或发散。

`torch.nn.utils.clip_grad_norm_` 函数会计算所有参数梯度的总范数（通常是 L2 范数），如果该范数超过设定的阈值 `args.max_grad_norm`，就会等比例地缩小所有梯度，使其总范数等于该阈值，从而**限制梯度的最大幅度**。

**为什么使用梯度裁剪**

- **防止梯度爆炸**：梯度裁剪可以有效地防止梯度变得过大，从而避免了训练过程中数值不稳定的情况。
- **提高训练稳定性**：通过限制梯度的大小，可以使得训练过程更加稳定，有助于模型收敛。
- **改善泛化能力**：有时候梯度裁剪也可以帮助模型获得更好的泛化能力。

7.**FlashAttention-2**

**技术原理**

(1) **分块计算**（Tiling）

- 将大型QK^T矩阵分割为小块，每次仅加载部分数据到GPU高速缓存中计算，避免一次性占用过多显存。
- **示例**：处理一个4096长度的序列时，分块为多个512x512的小块处理。

**(2) 核函数融合（Kernel Fusion）**

- 将Softmax、矩阵乘法和缩放等操作合并为单个GPU核函数，减少中间数据读写次数。
- **效果**：降低显存带宽压力，提升计算速度。

**(3) 双重并行化**

- **序列并行**：在序列长度维度上分块，多线程并行处理不同块。
- **特征并行**：在注意力头的维度上分配线程，充分利用GPU多核。

8.**内存管理**deepseek

- **ZeRO（Zero Redundancy Optimizer）**：
  - **ZeRO-1**：优化器状态分片。
  - **ZeRO-2**：梯度分片。
  - **ZeRO-3**：参数分片，支持千亿级模型训练（DeepSpeed实现）。

| **场景**         | **推荐技术**                                     |
| ---------------- | ------------------------------------------------ |
| **显存不足**     | 梯度检查点 + QLoRA + 混合精度训练                |
| **训练速度慢**   | FlashAttention-2 + 数据并行 + ZeRO-1（deepseed） |
| **推理延迟高**   | vLLM + 4-bit量化 + 动态批处理                    |
| **边缘设备部署** | 知识蒸馏 + 结构化剪枝 + TensorRT量化             |





### PET和P-Tuning

**PET模型**提出两个很重要的组件：

- **Pattern（Template）模板** ：记作T, 即上文提到的Template，其为额外添加的带有`[mask]`标记的短文本，通常一个样本只有一个Pattern（因为我们希望只有1个让模型预测的`[mask]`标记）。

- **Verbalizer** ：记作V, 即标签词的映射，对于具体的分类任务，需要选择指定的标签词（label word）。例如情感分析中，我们期望Verbalizer可能是 （positive和negative是类标签）。同样，不同的任务有其相应的label word。

  **缺点**：

  - 采用人工构建的方法成本高，需要与领域任务相关的先验知识；
  - 人工设计的Pattern和Verbalizer不能保证获得最优解，训练不稳定，不同的PVP对结果产生的差异明显，方差大；
  - 在预训练阶段MLM任务并非完全按照PVP的模式进行训练的（比如MLM训练通常都是长文本，mask的数量也并非只有1个，预测的概率分布也并非是有限的），因此人工构建的Pattern和Verbalizer使得Prompt-Tuning与MLM在语义和分布上依然存在差异。

**P-tuning**：

P-tuning 的核心思想是，利用一个**可学习的提示（prompt）**来引导预训练语言模型进行任务特定的学习。与传统的微调方法不同，P-tuning 并不改变模型的原始参数，而是通过在模型的输入中**插入一段可学习的连续向量（embedding）**来调整模型的行为。

P-tuning 的工作流程通常如下：

1. **初始化可学习提示**：在模型的输入部分，插入一段可学习的连续向量（embedding），这些向量的初始值可以是随机的，也可以通过其他策略初始化。
2. **优化提示参数**：通过训练数据，不断调整提示的向量值，以最小化任务的损失函数。在这个过程中，模型本身的参数保持不变，只有提示的向量被优化。
3. **利用提示进行推理**：经过优化的提示与任务输入结合，输入到模型中进行推理和预测。





**P-Tuning V1 和 V2 的主要区别**



1. **提示（Prompt）的结构和表达方式**：
   - **P-Tuning V1**：使用连续的可学习向量（embedding）来表示提示。这些向量**直接插入到模型的输入中**，与实际的文本输入一起经过模型的编码器（如Transformer）的各层传播。**V1 的提示是浅层的，仅仅是输入的扩展**，不对模型的深层结构进行过多干涉。
   - **P-Tuning V2**：提出了“深度提示”（deep prompt）的概念，通过在 Transformer 的**每一层都插入和优化提示向量**来进行提示优化。V2 的提示不仅限于模型输入的初始层，而是**贯穿整个模型的所有层**，这种深度插入的方式能够更好地影响模型的深层次表示学习，因此对模型的控制力更强。
2. **提示的位置和优化方式**：
   - **P-Tuning V1**：提示（prompt）**仅被添加到模型的输入位置**，即与输入序列的第一个层次的嵌入结合，并在之后的所有层中不变。V1 版本对提示的优化比较简单，通常只在输入侧进行有限的参数调整。
   - **P-Tuning V2**：提示（prompt）不仅仅是在输入层添加，而是会在 Transformer 的**每一层的输入上都插入可学习的向量（prefix）**。V2 可以看作是对模型多个层次的提示联合优化，这样的深层提示调控增强了模型在下游任务上的表现能力。
3. **任务适用性和泛化能力**：
   - **P-Tuning V1**：更适用于**较小规模的数据集和简单的任务（如分类任务）**，由于它仅改变输入层的提示，在面对更复杂任务（如文本生成或大型问答任务）时效果有限。
   - **P-Tuning V2**：通过多层提示优化，显著**提高了对复杂任务（如生成任务、多轮对话任务等）**的适应能力。V2 展示了在大型生成式任务和序列生成任务上的显著性能提升，并在小数据集的场景下表现出了更好的泛化能力。
4. **计算效率和训练复杂度**：
   - **P-Tuning V1**：计算效率较高，因为只需对输入的提示进行优化，不涉及模型内部结构的修改。训练过程也较简单，但其性能提升可能受限。
   - **P-Tuning V2**：虽然 V2 的提示学习会引入额外的计算开销（因为每一层都需要优化提示参数），但由于其更高效的模型调整方式，整体性能提升显著。在实际应用中，这种额外的计算开销通常是可以接受的，尤其是在任务复杂度较高时。
5. **实现和灵活性**：
   - **P-Tuning V1**：实现相对简单，适用于不需要太多结构性调整的任务。其设计相对简单，但也缺乏更深层次的模型控制能力。
   - **P-Tuning V2**：引入了更多的灵活性，可以应用于各种预训练模型（如GPT、BERT等），并能灵活调整提示的深度、位置和优化方式。因此，V2 被认为是 V1 的重要扩展和提升，适用于更广泛的自然语言处理任务场景。

**总结**

**P-Tuning V1** 是一种浅层的提示学习方法，主要通过在**输入端**插入可学习的向量来优化模型的表现，适用于简单任务和小数据集。**P-Tuning V2** 则引入了更深层次的提示优化，在 Transformer 的**每一层**都添加提示向量，以更好地适应复杂任务和大规模数据集。在实际应用中，P-Tuning V2 被证明在多个自然语言处理任务上具有更强的性能和灵活性。

### **Prefix Tuning 和 P-Tuning **

Prefix-Tuning 是将额外的embedding加在开头，看起来更像模仿Instruction指令，而P-Tuning 位置不固定。Prefix-Tuning 通过在每个层都添加可训练参数，通过MLP初始化，而P-Tuning只在输入的时候加入embedding, 并通过LSTM+MLP初始化

1. **提示（Prompt）的位置与范围**：
   - **P-Tuning**：P-Tuning 的提示是通过在模型的输入部分插入一个可学习的向量（embedding）来实现。这些向量被插入到输入文本的开始或中间，与输入序列一起经过模型的多层传播。P-Tuning V1 仅在输入层插入提示，而 P-Tuning V2 进一步在模型的各层中都添加提示，这样可以更深度地引导模型学习特定任务。
   - **Prefix Tuning**：Prefix Tuning 的提示是一种“前缀”（prefix）提示，通过在 Transformer 的每一层的自注意力模块中插入一段可学习的前缀向量。这些前缀被视为“虚拟的上下文”，可以在模型的多层注意力机制中发挥作用。具体来说，Prefix Tuning 会在每一层的注意力键（Key）和值（Value）上添加前缀向量，这样可以在模型生成文本时不断影响模型的行为。
2. **提示的表达形式**：
   - **P-Tuning**：提示是直接插入的可学习连续向量（embedding），这些向量相当于添加到输入文本中的额外“词”。在训练期间，这些嵌入向量与模型的其他输入部分一起进行传播和优化。
   - **Prefix Tuning**：提示是通过一段较长的前缀嵌入序列插入到每一层的注意力模块中。这些前缀可以视为模型的“虚拟上下文”，在每层的键和值部分被用作附加信息，影响每一层的注意力计算，而不仅仅是作为输入文本的一部分。
3. **提示的优化方式**：
   - **P-Tuning**：提示的优化主要是在输入侧进行的，通过调整提示的连续向量，使得模型的输出更加符合特定任务的目标。P-Tuning v2 会在每一层插入提示并优化它们，但总体思路仍然是优化模型输入层的提示。
   - **Prefix Tuning**：优化的目标是每一层自注意力机制的前缀向量，因此优化会直接影响每一层的注意力计算。Prefix Tuning 的提示相当于对模型的多层注意力机制进行优化，而不是仅仅改变输入。
4. **任务适用性和模型控制**：
   - **P-Tuning**：适用于分类、情感分析等任务，以及序列到序列的生成任务，但主要依赖于输入提示的优化。P-Tuning V2 的多层提示优化策略增强了对复杂任务的适应能力。
   - **Prefix Tuning**：更适用于生成式任务，如文本生成、对话生成等。因为它在每层的自注意力中插入前缀，可以持续影响生成过程，使得模型在长文本生成中能够更好地控制输出的内容和风格。
5. **计算效率与复杂度**：
   - **P-Tuning**：计算效率较高，因为即使是 P-Tuning V2，也只是在输入或多层中插入提示，不会显著增加每一层的计算负担。
   - **Prefix Tuning**：在每一层的注意力机制中插入前缀可能会增加一些计算负担，因为需要在每层都对注意力进行额外的计算，但由于只优化少量的前缀向量，这种开销通常是可控的。

**总结**

- **P-Tuning** 是一种在输入或多层插入可学习的提示向量的方法，主要通过改变输入层提示或多个层次的提示来提升模型的性能，更加适用于多种任务场景。
- **Prefix Tuning** 通过在每一层的自注意力机制中插入前缀提示向量，将提示学习提升到了对模型生成过程的多层次控制，更适合用于生成任务，特别是需要对模型生成内容进行更精细控制的任务。 

### F1值的计算

**是通过手动构建精确率和召回率**

精确率表示模型预测的三元组中有多少是正确的

召回率表示实际的三元组中有多少被模型正确预测出来

```
   tp = len(set(pred_spos) & set(true_spos))  # 真正例
   fp = len(set(pred_spos) - set(true_spos))  # 假正例
   fn = len(set(true_spos) - set(pred_spos))  # 假反例
```



### **RAG**

RAG 系统通常由两个主要组件构成：**检索器（Retriever）和生成器（Reader）**：

- 检索器负责从外部知识库中检索相关的上下文片段，这些片段随后被传递给生成器
- 生成器基于这些上下文生成最终的回答。

结果表明：随着上下文片段数量的增加，系统的性能逐步提升，但**当片段数量达到 10 到 15 个(top_10~15)**时，性能开始趋于稳定，甚至在某些情况下会出现下降。这一现象表明，过多的上下文片段可能会导致信息过载，反而影响生成器的表现。

**文档/文本切分方法** 

**(MinerU、docmian是pdf)**  、**olmOCR图片和pdf**

**a）Fixed size chunking（固定大小分块）**：这是最常见、最直接的分块方法。我们只需决定分块中的**tokens数量**，以及它们之间是否应该有任何**重叠（overloop）**。一般来说，我们希望在块之间保持一些重叠，以确保语义上下文不会在块之间丢失。

**工具示例** ：`TokenTextSplitter`（按Token分割）

**特点**

- **固定大小** ：每个块的大小是预先设定的（例如 500 个字符、100 个词或 256 个标记）。
- **简单直接** ：实现起来非常简单，只需按固定步长切分即可。
- **可能破坏语义** ：由于不考虑内容边界，可能会在句子或段落中间切断，导致语义不连贯。

**b）Recursive Chunking（递归分块）**：

递归分块是一种基于内容结构的分块方法。它通过递归地分析文档的内容层次结构（如段落、句子、子句等），将文档划分为具有语义意义的块。这种方法通常会根据文档的自然边界（如标题、段落、列表等）进行分割。

**典型方法** ：`RecursiveCharacterTextSplitter`（**LangChain**默认分割器）

**特点**

- **语义驱动** ：分块的结果通常与文档的语义结构高度相关。
- **动态大小** ：每个块的大小可能不同，取决于内容的自然边界。
- **递归性** ：如果某个块仍然过大，可以进一步递归地将其细分为更小的块。
- **适合复杂文档** ：特别适用于结构化较强的文档（如技术手册、法律文件、学术论文等），因为这些文档通常有明确的层次结构。

**c）Document Specific Chunking（文档特定分块）**：基于文档的逻辑部分（如段落或小节）来生成对齐的块。该方法可以保持内容的组织，从而保持了文本的连贯性，比如Markdown、Html等特殊格式。

**工具示例** ：`MarkdownHeaderTextSplitter`（**LangChain**按Markdown标题分割）

**Markdown**

​	**(1)按标题层级分块**  标题层级（`#`, `##`, `###` ）等

- **方法** ：将每个标题`#`, `##`, `###`及其后续内容作为一个独立的块。


​        **(2) 按段落分块**

- **方法** ：将每个段落（由空行分隔的内容）作为一个独立的块。

​        **(3) 按特定元素分块**

- **方法** ：根据 Markdown 的特定元素（如列表、代码块、表格等）进行分块。

**Html**

​	**(1) 按标签层级分块**

- **方法** ：根据 HTML 标签的层级结构（如 `<h1>`、`<h2>` 等标题标签）进行分块。

   **(2) 按段落或区块分块**

- **方法** ：将每个 `<p>` 或 `<div>` 标签内的内容作为一个独立的块。

   **(3) 按特定元素分块**

- **方法** ：根据 HTML 的特定元素（如 `<table>`、`<ul>`、`<code>` 等）进行分块。

**d）Semantic Chunking（语义分块）**：语义分块会考虑文本内容之间的关系。它将文本划分为有意义的、语义完整的块。这种方法确保了信息在检索过程中的完整性，从而获得更准确、更符合上下文的结果。与之前的分块策略相比，速度较慢。**实现语义分块通常需要结合自然语言处理（NLP）技术和文档解析工具。**

**e）基于LLM的动态切分（LLM-Driven Splitting）**  **我的想法：训练一个小模型专门用来语义切分。**

- **特点** ：通过大语言模型代理分析文本，动态识别最佳分割点。
- **实现方式** ：LLM生成分块建议或直接执行切分指令。
- **潜力** ：适应复杂语境，但依赖模型性能与成本



**文本嵌入模型**

**通用语义搜索**：**BGE-large-zh** ——北京智源人工智能研究院BAAI

**长文本处理**：**jina-embeddings-v2**

**OpenAI ：text-embedding-3**（商业API）



**gte-Qwen2-7B-instruct**——阿里 

阿里云商业化接口为 **text-embedding-v3**



**bge-m3:latest ——智谱**

**架构**：基于BERT的多模态模型

| **开发机构**   | 阿里巴巴                 | 智谱AI             |
| -------------- | ------------------------ | ------------------ |
| **基础模型**   | Qwen2-7B LLM             | BERT-based 架构    |
| **MTEB 排名**  | 中英文任务领先（No.1-2） | 通用任务表现均衡   |
| **上下文长度** | 8192 tokens              | 512 tokens         |
| **多语言支持** | 70+ 种语言               | 中英为主           |
| **部署优化**   | vLLM 框架，推理速度快    | 标准部署，兼容性广 |
| **典型场景**   | RAG、复杂指令任务        | 通用语义检索       |



**索引**

- 相似性检索：即计算查询向量与所有存储向量的**相似性得分**，返回得分高的记录。常见的相似性计算方法包括：余弦相似性、欧氏距离、曼哈顿距离等。
- 全文检索：全文检索是一种比较经典的检索方式，在数据存入时，通过关键词构建倒排索引；在检索时，通过**关键词**进行全文检索，找到对应的记录。

**向量索引（Vector Index）**的数据处理方式：
文本向量化：使用嵌入模型（如Word2Vec、BERT、GPT等）将文本转换为数值向量形式。
向量数据库存储：将向量化的文本存储在向量数据库中，如使用Pinecone、Elasticsearch、FAISS、Milvus等。
构建索引结构：在向量数据库中构建索引，以支持高效的向量搜索和检索操作。
相似性搜索：通过计算查询向量与数据库中向量的相似度，检索出最相关的文档向量。
优化和调整：根据需要对索引结构进行优化，以提高搜索的速度和准确性。
**摘要索引（Summary Index）**的数据处理方式：
文本摘要提取：使用文本摘要技术从文档中提取关键句子或段落，形成摘要。
关键词提取：识别文档中的关键词汇或短语，以代表文档的主要内容。
元数据生成：可能包括文档的标题、作者、发布日期等信息，以及与文档内容相关的标签或分类。
结构化存储：将摘要和元数据以结构化的形式存储在数据库中，如关系数据库或NoSQL数据库。
快速检索：通过索引快速访问文档的摘要和关键信息，以评估文档的相关性或作为生成回答的参考。
**问题-答案索引（QA Index）**数据处理方式：
问题嵌入：将问题（Q）通过嵌入模型转换成向量形式。
索引存储：将问题向量存储在索引结构中，通常与答案（A）相关联。
检索匹配：通过比较用户查询的向量表示与索引中的问题向量来检索最匹配的QA对。
答案展示：检索到相关问题后，展示对应的答案作为结果。



**RAG问答评估标准：**

**评价指标：**

• ROUGE-L 是一种基于最长公共子序列（LCS）的评估指标，主要用于衡量生成文本与参考文本之间的相似性。它关注生成文本中与参考文本匹配的最长连续子序列，**侧重于召回率（Recall），即生成文本中有多少内容与参考文本一致**。

• BERTScore 是一种基于预训练语言模型（如 BERT）的评估指标，通过计算生成文本与参考文本在语义空间中的相似性来评估质量。它使用 BERT 模型将文本编码为向量，然后计算生成文本与参考文本之间的余弦相似度。相比 ROUGE 等基于词汇重叠的指标，BERTScore 更能**捕捉语义层面的相似性，适合评估生成文本的语义准确性**。

**1.RAGAs框架**

**答案忠实度(faithfulness)**衡量了生成的答案(answer)与给定上下文(context)的事实一致性。它是根据answer和检索到的context计算得出的。**如果答案(answer)中提出的所有基本事实(claims)都可以从给定的上下文(context)中推断出来，则生成的答案被认为是忠实的**。

**答案相关性**重点评估生成的答案(`answer`)与用户问题(`question`)之间相关程度。不完整或包含冗余信息的答案将获得较低分数。

**上下文准确率**是衡量相关块在上下文中所占比例的指标`retrieved_contexts`

**上下文召回率**衡量**成功检索到的相关文档（或信息）数量**。它侧重于不遗漏重要结果。

**事实准确度（Factual Accuracy）**：量化生成答案在事实细节上与标准答案的一致性。

------

**2.ARES（Automatic RAG Evaluation System）**

- **核心机制**：结合检索质量（Retrieval Quality）和生成质量（Generation Quality）的自动化评估框架。
  - **检索质量指标**：
    - **Hit Rate（命中率）**：检索结果中包含正确答案的比例。
    - **Context Recall（上下文召回率）**：检索内容对标准答案的覆盖程度2。
  - **生成质量指标**：
    - **Faithfulness（忠实度）**：生成答案是否完全依赖检索内容。
    - **Answer Correctness（答案正确性）**：与标准答案的语义和事实对齐度。

------

**3.TruLens**

- **特点**：开源评估框架，支持多维度可解释性分析。
  - **关键指标**：
    - **Groundness**：答案与检索内容的依赖关系。
    - **Context Relevance**：检索内容与问题的相关性。
    - **Answer Relevance**：生成答案与问题的匹配度1。
  - **优势**：提供可视化分析面板，便于定位错误环节（如检索失效或生成幻觉）。

------

**4.基于 LLM 的自动化评估**

- **方法**：利用大语言模型（如 GPT-4、Claude）作为“裁判”，评估生成答案的质量。
  - **流程**：
    1. 将问题、检索上下文和生成答案输入 LLM。
    2. 要求 LLM 从准确性、相关性等维度打分1。
  - **优点**：低成本、可扩展性强，适合大规模测试。
  - **局限性**：依赖裁判模型的自身能力，可能引入评估偏差。

  

**RAG优化策略**

---

**一、 检索阶段优化策略**

核心目标：**检索到与查询最相关、对生成答案最有用的文档片段。**

1.  **查询理解与改写/扩展：**
    *   **查询改写：** 使用小模型或规则将用户原始查询改写成更清晰、更符合知识库表达习惯的形式（如同义词替换、句式调整）。
    *   **查询扩展：**
        *   **同义词/关联词扩展：** 添加查询词的同义词、近义词、相关实体或概念，增加召回率。
        *   **生成式扩展：** 利用语言模型基于原始查询生成相关问题或背景描述，丰富查询语义。
        *   **HyDE：** 让语言模型根据查询“幻想”一个假设答案，然后用这个假设答案作为新查询去检索真实文档，效果显著。
    *   **查询分解：** 对于复杂、多子问题查询，将其拆分成多个独立的子查询分别检索，再合并结果。

2.  **文档处理与索引优化：**
    *   **智能分块：**
        *   **语义分块：** 利用模型或规则，确保每个文本块在语义上相对完整（如按段落、小节、主题边界划分），避免关键信息被切断。
        *   **重叠分块：** 相邻块之间设置重叠区域，确保跨越块边界的信息也能被有效检索。
        *   **多粒度分块：** 对同一文档采用不同粒度的分块（如小节、段落、句子），建立多级索引，适应不同查询需求。
    *   **元数据增强：** 为分块添加标题、章节号、作者、日期、来源等元数据，检索时可利用这些元数据进行过滤或加权。
    *   **向量化模型选择与微调：**
        *   选用更强大的嵌入模型（如 OpenAI text-embedding-3-large, Cohere Embed, BGE-M3）。
        *   **领域微调：** 在特定领域数据上微调嵌入模型，使其更理解领域术语和语义。
        *   **多向量策略：** 除了整个文本块的向量，同时索引关键句子的向量或摘要向量，提供更细粒度的表示。
    *   **混合检索：**
        *   **融合稀疏检索与稠密检索：** 结合传统的 BM25/关键词检索（擅长精确匹配）和向量检索（擅长语义匹配），取长补短（如 Reciprocal Rank Fusion）。
        *   **多表示检索：** 为同一文档块生成多种向量表示（如不同模型），融合检索结果。

3.  **检索过程优化：**
    *   **重排序：**
        *   **两阶段检索：** 第一阶段用高效方法（如向量检索）召回大量候选文档（如 top 100）；第二阶段用更精细但耗时的模型（Cross-Encoder）对候选文档进行精排，选出最相关的 top K。
        *   **学习排序：** 训练一个模型学习文档与查询的相关性得分，用于重排序。
    *   **元数据过滤：** 在检索前或后，利用文档块的元数据（如日期范围、来源可信度、类型）进行过滤或加权。
    *   **多样化检索：** 确保返回的结果覆盖查询的不同方面，避免信息冗余。

---

**二、 生成阶段优化策略**

核心目标：**让大模型更精准、可靠、高效地利用检索到的上下文生成答案，减少幻觉和无关输出。**

1.  **上下文表示与组织：**
    *   **结构化提示：** 清晰地将检索到的文档与用户查询、指令分隔开。使用明确的标记（如 `<context>...</context>`, `<document id=1>...</document>`）。
    *   **文档排序与选择：**
        *   将最相关的文档放在提示的开头或靠近用户查询的位置。
        *   设定最大上下文长度，优先保留相关性最高的文档片段（根据检索得分）。
        *   尝试不同的文档排序策略（按相关性、时间、来源可信度等）。
    *   **文档摘要/压缩：**
        *   **Extractive 摘要：** 从检索到的文档中直接选取最关键的句子或短语。
        *   **Abstractive 摘要：** 用另一个模型（或同一个模型）对检索结果进行概括性摘要，浓缩信息。
        *   **选择性拼接：** 只拼接与查询最直接相关的文档片段，而非整个文档块。
    *   **元数据注入：** 在提示中加入文档的元数据（如来源、标题、日期），帮助模型评估信息可靠性和时效性。

2.  **提示词工程：**
    *   **明确指令：**
        *   **强调基于上下文：** 明确指令模型“严格基于提供的上下文回答问题”、“如果答案不在上下文中，请说明不知道”。
        *   **引用来源：** 要求模型在生成答案时引用具体文档片段或ID（如 `[1]`），提高可解释性和可信度。
        *   **任务特定指令：** 根据任务细化指令（如“总结”、“比较”、“分步骤解释”）。
    *   **角色设定：** 给模型设定一个合适的角色（如“知识渊博的助手”、“严谨的分析师”）。
    *   **Few-shot 示例：** 在 prompt 中提供少量输入（查询+相关上下文）和期望输出（高质量答案）的示例，指导模型行为。
    *   **思维链/分步推理：** 鼓励模型展示推理过程（“让我们一步步思考”），有助于提升复杂问题回答的准确性和可控性。

3.  **模型层面的优化：**
    *   **微调：**
        *   **指令微调：** 在包含 `(查询， 检索到的上下文， 理想答案)` 三元组的数据集上微调模型，教会它如何更好地利用上下文。
        *   **拒绝微调：** 训练模型在上下文不包含答案时明确说“我不知道”。
    *   **后处理与验证：**
        *   **一致性校验：** 让模型多次生成答案，检查一致性；或生成答案后让模型自我验证答案是否基于上下文。
        *   **事实核查：** 将生成的答案中的关键事实或主张，再次检索知识库进行验证（轻量级）。
        *   **来源引用生成：** 专门训练或提示模型生成准确的引用。

4.  **处理上下文长度限制：**
    *   上述的摘要、压缩、选择性拼接都是应对长上下文的方法。
    *   **迭代检索生成：**
        *   模型在生成初步答案或思路后，发现需要更多信息，触发新一轮检索。
        *   对于复杂问题，分解成子问题，每个子问题独立进行检索->生成。
    *   **图检索/记忆网络：** 更高级的结构，允许模型在生成过程中动态访问和关联知识库中的信息片段。

---

**关键协同点**

*   **评估驱动优化：** 两阶段的优化都需要建立明确的评估指标（检索阶段：召回率@K, MRR, NDCG；生成阶段：答案准确性、事实一致性、相关性、流畅性、引用准确率）来指导优化方向。
*   **反馈循环：** 利用生成结果或用户反馈来改进检索（如：将用户最终采纳的答案或点击的文档作为正反馈，优化检索模型或分块策略）。
*   **端到端微调：** 一些研究探索将检索器和生成器一起进行端到端微调（如 RAG-Token, RAG-Sequence），让两者更协同。

选择哪些优化策略取决于具体的应用场景、数据特性、性能要求和资源限制。通常需要结合多种策略并进行实验才能达到最佳效果。**记住：检索质量是生成质量的上限，优先保证检索效果往往事半功倍。**





### Agent

AI Agent 具备通过独立思考、调用工具去逐步完成给定目标的能力。Agent可以是物理实体（如机器人）或虚拟实体（如软件程序）。

 **AI Agent 工作流程：**

- 01-Prompt提示词:
- 提示词是Agent接收到的初始输入，它描述了Agent需要完成的任务或解决的问题。
- 02-LLM大模型:
- 大模型是Agent进行任务规划和知识推理的重要工具。利用LLM大模型对提示词进行深入分析，生成可能的解决方案。
- 03-Memory记忆:
- 可以保留当前用户输入内容；上下文内容；外部向量存储的知识库；网页信息等。
- 04-Planning规划:
- 任务规划是Agent根据提示词、大模型以及知识库进行决策和规划的过程它涉及对任务的分解、目标的设定、路径的规划等。
- 05 Action行动：
- 行动执行是Agent根据任务规划结果执行具体操作的过程。包含工具：计算器、代码解释器、搜索、API等

Function Call 可以解决大模型什么问题：

- **信息实时性**：大模型训练时使用的数据集往往有时间限制，无法包含最新的信息，如最新的新闻、实时股价等。通过Function Call，模型可以实时获取最新数据，提供更加时效的服务。

- **数据局限性**：模型的训练数据量庞大但有限，无法覆盖所有可能的查询，如医学、法律等领域的专业咨询。Function Call允许模型调用外部数据库或API，获取特定领域的详细信息。

- **功能扩展性**：大模型虽然功能强大，但不可能内置所有可能需要的功能。通过Function Call，可以轻松扩展模型能力，如调用外部工具进行复杂计算、数据分析等。



## 1、大模型分词构建词表方式

**LLM训练流程**大概可以分为以下三步：**预训练(Pre-Training，PT)、监督微调(Supervised Fine-Tuning，SFT)和 偏好优化(PreferenceOptimization，PO)**盘

| **特性**       | **BPE**                                        | **BBPE（Byte-level BPE）**                                |
| -------------- | ---------------------------------------------- | --------------------------------------------------------- |
| **基本单元**   | 基于字符（如字母、汉字）                       | 基于字节（Byte，UTF-8编码单元）                           |
| **编码方式**   | 直接操作Unicode字符                            | 先将文本拆解为字节序列，再应用BPE                         |
| **多语言支持** | 需显式处理不同语言的字符集                     | 天然支持所有UTF-8文本（无需预处理）                       |
| **罕见词处理** | 依赖字符组合，可能词表膨胀                     | 通过字节组合覆盖任意罕见词                                |
| **词表大小**   | 较大（需覆盖所有字符）                         | 更小（仅需256个字节作为基础）                             |
| **典型应用**   | GPT-2、LLaMA                                   | GPT-3/4、Qwen、CodeX                                      |
| **OOV 处理**   | 将未知词分解为已知子词，可能出现 `<UNK>` token | 总是能分解到字节，**无 OOV 问题**，不会出现 `<UNK>` token |

词汇表（vocabulary）过大和未登录词（Out-Of-Vocabulary, OOV）问题

**BPE处理方式：**

"unbelievable" 这个词没有在词汇表中，但["un", "be", "li", "ev", "able"] 都在，那么它就会被分解成这些子词

假设我们训练语料中从未出现过中文字符，现在突然来了一个词：`"你好"`。

- 传统的 BPE 可能会将其分解为：`["你", "好"]`
- 但如果分词器的词汇表中连 `你` 或 `好` 这样的字符都没有，那么它们可能会被映射为特殊的 **<UNK> (Unknown)** token： `["<UNK>", "<UNK>"]`

**BBPE处理方式：**

**UTF-8 编码：** 首先，将 `"unbelievable"` 编码成 UTF-8 字节序列。 对于英文字母，UTF-8 编码与 ASCII 码相同，每个字符对应一个字节。 所以，`"unbelievable"` 对应的字节序列是： `[117, 110, 98, 101, 108, 105, 101, 118, 97, 98, 108, 101]` (这是每个字符的ASCII值)

**BBPE 应用：** BBPE 分词器会在这个字节序列上查找最频繁出现的相邻字节对进行合并。 假设它学到了以下合并：

- `[117, 110]` -> `un` (这是字节 `u` 和 `n` 的合并，表示字符 "un")
- `[98, 101]` -> `be`
- `[108, 105]` -> `li`
- `[101, 118]` -> `ev`
- `[97, 98, 108, 101]` -> `able` (可能是一个多字节的合并)

最终，`"unbelievable"` 可能会被分解成： `["un", "be", "li", "ev", "able"]`

假设我们训练语料中从未出现过中文字符，现在突然来了一个词：`"你好"`。

1. **UTF-8 编码：** 将 `"你好"` 编码成 UTF-8 字节序列。

   - `你` (U+4F60) 在 UTF-8 中是 `[E4, BD, A0]` (三个字节)
   - `好` (U+597D) 在 UTF-8 中是 `[E5, A5, BD]` (三个字节)

   所以，`"你好"` 对应的字节序列是： `[E4, BD, A0, E5, A5, BD]` (使用十六进制表示字节值)

2. **BBPE 应用：** BBPE 分词器会在这个字节序列上查找最频繁出现的相邻字节对进行合并。

   - **情况一：** 如果 `"你"` 这个字符（或其对应的字节序列 `[E4, BD, A0]`）在训练语料中足够常见，BBPE 可能会将其合并成一个子词 token，例如 `_你`。同理，`好` 也会被合并。 最终可能分解为：`["_你", "_好"]` (注意：`_` 通常表示该 token 是一个词的开始，这是 SentencePiece 等分词器常见的约定)。
   - **情况二：** 如果 `"你"` 或 `"好"` 不够常见，BBPE 可能会合并部分字节，例如 `[E4, BD]`，或者甚至无法合并，直接保留原始字节。 例如，它可能分解为：`["E4", "BD", "A0", "E5", "A5", "BD"]` (如果没有任何字节对合并规则应用于这些字节)。

**关键区别：**

- 无论 `你` 或 `好` 是否在 BBPE 的子词词汇表中，它们**永远不会被映射为 <UNK> token**。它们总是能被分解到最基础的字节级别。
- 即使被分解成了原始字节（例如 `E4`, `BD`, `A0`），模型仍然能够通过这些字节组合来处理这个词。虽然这可能导致序列长度增加，但**信息不会丢失**。模型可以通过学习这些字节组合的模式来理解其含义。





## 2、绝对位置编码，相对位置编码

 **一、绝对位置编码**

1. 原理： 绝对位置编码是一种将序列中的每个位置进行编码的方法，它为每个位置分配一个唯一的编码向量。最常用的绝对位置编码方法是通过使用**三角函数的正弦和余弦函数来生成位置编码**。

   具体而言，绝对位置编码使用了一组固定的正弦和余弦函数，根据位置索引和维度来计算每个位置的编码向量。这样的编码向量可以提供关于输入序列中每个位置的绝对位置信息。绝对位置编码的优势在于它不依赖于序列中的其他元素，可以独立地表示每个位置的信息。

   优点：   - 简单直观，能够明确地表示每个位置的绝对位置信息。   - 对于**固定长度的序列**，编码方式相对稳定。

   缺点： 1、使用绝对位置编码的序列，不同位置对应的向量虽然不同，即可以反应序列应该有顺序关系；但是**难以反应序列字符之间的相对位置关系**。比如位置1和位置2，距离差1，位置1和位置3距离差2，位置2和位置3距离差1，这些距离远近会反应什么关系呢？绝对位置编码挖掘不到这中信息。

    2、**没有外推性**，即表示不了比预训练文本长度更长的位置向量表示， 即如果**预训练**最大长度为512的话，那么**推理预测时**最多就只能处理长度为512的句子，再长就处理不了了。当然，也可以将超过512的位置向量随机初始化，然后继续微调。

 **二、相对位置编码** 

1. 原理：相对位置编码是一种根据位置之间的相对关系来编码序列的方法。相对位置编码考虑了序列中不同位置之间的相对距离和关系，并使用可学习的参数来对这些关系进行建模。

   没有完整建模整个序列的位置信息，而是在算当前位置的Attention的时候，考虑了当前位置和被Attention位置之间的相对距离，由于自然语音通常更依赖文本之间的相对距离（离得近表示一种相依赖的关系，比如word2vec词向量模型）

   例如，旋转位置编码，**主要思想是在不增加额外参数的情况下，通过旋转向量来编码位置信息。具体来说，RoPE通过在前向传播过程中动态地调整每个词嵌入的位置信息，使得模型能够更好地捕捉到文本中词语之间的相对位置关系。**。

   RoPE的做法是**根据词语的位置，将箭头在多个二维平面上旋转不同角度**。位置越靠后的词，旋转的角度越大。

2. 优点：   - 更灵活地处理长度变化的序列，不需要随着序列长度的变化而重新计算整个位置编码。   - 对于长序列，相对位置编码通常比绝对位置编码更高效，因为它只关注相对位置关系，而不是为每个位置分配一个独立的编码向量。 

3. 缺点：   - 相对位置编码可能会丢失一些全局的位置信息，因为它只关注局部的相对位置关系。   - 设计和实现相对位置编码可能会比较复杂，需要考虑如何有效地计算和应用相对位置信息。

## 3、文本预处理

文本预处理是自然语言处理（NLP）中的一个关键步骤，旨在清洗和标准化文本数据，以便提高下游任务（如分类、聚类、情感分析等）的效果。以下是一些常见的文本预处理方法：

1. **文本清理 (Text Cleaning)**

- **去除噪声字符**: 移除特殊字符、标点符号、HTML标签、网址、邮件地址等。
- **去除空格和重复字符**: 清除多余的空格或重复的字符（例如，“hellooo”变成“hello”）。
- **大小写转换**: 将所有文本转换为小写或大写，以避免“Hello”和“hello”被当作两个不同的词处理。

2. **标记化 (Tokenization)**

- 将文本拆分成更小的单元（如单词、短语或句子）。常见的标记化方法包括：
  - **单词标记化**: 根据空格或标点符号将文本切分成单词。
  - **子词标记化**: 使用子词（如 Byte-Pair Encoding, WordPiece）将单词切分成更小的单位，以处理未登录词问题。
  - **句子标记化**: 根据句子结束符（如句号、问号）将文本切分成句子。

3. **去停用词 (Stopword Removal)**

- 移除**频率很高但对文本语义贡献较小的词**（如“这”, “是”, “在”，“的”等），以减少噪声和计算量。停用词的列表可以根据语言和任务定制。

5. **文本规范化 (Text Normalization)**

- **同义词替换**: 将同义词替换为同一个标准词，以减少词汇的多样性。
- **特殊符号标准化**: 例如将笑脸表情符号“:)”转换为“smile”。

7. **文本编码 (Text Encoding)**

- 将文本转换为计算机能够理解的数值格式。常见的方法包括：
  - **One-Hot Encoding**: 将每个词编码为一个高维稀疏向量。
  - **词袋模型 (Bag of Words)**: 统计文本中词汇的出现频率并表示为向量。
  - **TF-IDF (词频-逆文档频率)**: 根据词频和逆文档频率给每个词赋权重，以反映词对文本的重要性。
  - **词向量 (Word Embeddings)**: 使用预训练的嵌入模型（如 Word2Vec、GloVe 或 FastText）将词映射到低维向量空间。

8. **文本分块 (Chunking) 和命名实体识别 (NER)**

- **分块 (Chunking)**: 将标记化后的文本分为短语或子句，以便于后续处理。
- **命名实体识别 (Named Entity Recognition, NER)**: 识别文本中的特定实体（如人名、地名、组织名、时间等），并进行标注。

9. **文本平滑和修正**

- **删除低频词**: 移除出现频率过低的词，以减少噪声。
- **频率平滑**: 对于词频做平滑处理，避免零频词带来的问题。

10. **句法分析 (Syntactic Parsing)**

- 对句子进行语法结构分析，识别句子的主谓宾结构，分句等。帮助理解文本的复杂结构和句子的深层含义。

11. **文本摘要和抽取**

- 对于较长的文本，通过自动化工具生成摘要，提取出文本中的关键信息。

12. **情感分析预处理**

- 对于情感分析任务，可能需要进行特定的预处理，如识别情感词汇、处理表情符号、语气词等。

13. **数据增强 (Data Augmentation)**

- 通过对文本进行各种变化（如同义词替换、语序调整、拼写变化等）来生成更多的数据样本，以增强模型的泛化能力。

14. **去重 (Deduplication)**

- 移除重复的文本数据，以确保数据集的多样性和准确性。

15. **文本分词（Segmentation）**

- 在中文、日文等无空格分词的语言中，使用分词工具将句子切分为单词或短语。例如，使用中文分词工具如 `jieba` 或 `THULAC`。

**词法分析**（Lexical Analysis）-Tokens

词法分析，也称为分词是将文本切分成一个个单词，这些单元称为词或词元（**Tokens**）。在中文中，由于**缺乏空格分隔**，分词是一项关键任务。

词法分析不仅涉及切分文本，还包括识别每个词的**词性标注（如名词、动词、形容词等）**，这对于理解文本的结构和意义非常重要。这项工作通常由词法分析器（Lexer）完成，它利用正则表达式、词典匹配、统计模型或机器学习算法等技术来实现。

**句法分析**（Syntactic Analysis）

句法分析关注的是理解文本中词汇如何**组合成短语和句子的规则结构**，即研究句子的构成成分和它们之间的关系。句法分析有助于确定句子的主谓宾关系、修饰关系等，对于信息抽取、问答系统、机器翻译等领域尤为重要。

句法分析器可以使用上下文无关文法（CFG）、依存语法等理论框架，并结合统计模型或深度学习方法进行实现。

## 4、模型预训练过程

**加载数据迭代器、前向传播、计算损失、反向传播、梯度和参数（优化器、学习率）更新、打印日志**

**梯度是损失函数关于模型参数的变化率，它指明了损失函数下降最快的方向**。通过沿着梯度的负方向调整参数，我们可以不断地减小损失值，从而让模型学习到更好的参数，提高预测的准确性。损失和梯度是优化模型、驱动模型学习过程的两个相互依赖的核心要素。

1. **训练环境搭建与配置**

- 硬件基础设施：
  - **GPU/TPU集群：** 需要大规模的并行计算集群，通常是成百上千甚至上万块高性能GPU（如NVIDIA A100、H100）或TPU（如Google TPU v4）。这些计算单元之间通过高速互联（如InfiniBand）进行通信。
  - **高速存储：** 训练数据通常存储在分布式文件系统（如Ceph、Lustre、HDFS）或对象存储（如AWS S3、Google Cloud Storage）中，需要确保数据读取速度足够快，以避免成为训练瓶颈。
  - **网络带宽：** 分布式训练中，节点之间需要频繁交换梯度和模型参数，因此高速网络是必不可少的。
- 软件环境：
  - **深度学习框架：** 通常使用PyTorch或TensorFlow。这些框架提供了构建、训练和部署深度学习模型的API。
  - **分布式训练库：** 为了高效利用多GPU/多机环境，会使用专门的分布式训练库，如PyTorch的DistributedDataParallel (DDP)、Megatron-LM、DeepSpeed、FSDP (Fully Sharded Data Parallel) 等。这些库提供了数据并行、模型并行、管道并行等策略的实现。
  - **优化库：** 如CUDA、cuDNN等，用于加速GPU上的计算。
  - **监控工具：** 集成各种监控和日志工具，如TensorBoard、Weights & Biases、MLflow等，用于跟踪训练过程中的指标。

2. **数据加载与迭代**

- 分布式数据加载：
  - 在分布式训练中，通常使用`DistributedSampler`来确保每个GPU只处理数据的一个不重叠子集。这保证了数据并行时的负载均衡和正确性。
  - 数据通常是预处理好的二进制文件（如TFRecord、PyTorch的`Dataset`序列化文件），以提高加载效率。
- 数据管道优化：
  - **多进程数据加载：** 使用多个工作进程（`num_workers`）在后台加载和预处理数据，避免CPU成为GPU的瓶颈。
  - **数据缓存/预取：** 将一部分数据加载到内存或高速缓存中，或预先加载下一个批次的数据，减少等待时间。
  - **数据混洗 (Shuffling)：** 在每个epoch开始时对数据进行混洗，以增加模型的泛化能力。

**3. 前向传播 (Forward Pass)**

- **输入准备：** 每个GPU接收分配给它的批次数据（通常是token ID序列），并可能包括注意力掩码、位置编码等。
- **模型计算：** 输入数据通过模型的各个层（例如Transformer的自注意力层和前馈网络）。
- **损失计算：** 根据预训练任务（如Next Token Prediction），模型会输出预测结果。这些预测结果与真实的下一个token（或被遮盖的token）进行比较，计算出损失值（通常是交叉熵损失）。

**4. 反向传播 (Backward Pass) 与梯度计算**

- **损失梯度：** 计算损失函数对模型输出的梯度。
- **链式法则：** 利用链式法则，将损失梯度反向传播到模型的每一层，计算出每一层参数的梯度。
- **梯度累积：** 如果批大小过大而单个GPU内存不足以容纳，可以采用梯度累积。即执行多次小批量的前向和反向传播，累积其梯度，然后才进行一次参数更新。这模拟了使用更大批量的训练效果。

5. **梯度同步与参数更新**

- 分布式梯度同步：
  - 在数据并行模式下，每个GPU计算出各自的梯度。为了进行正确的参数更新，需要将所有GPU上的梯度进行聚合（求平均）。
  - 这通常通过**all-reduce**操作完成，所有GPU将自己的梯度发送给其他GPU，并接收其他GPU的梯度，最终每个GPU都得到所有GPU梯度的平均值。这是分布式训练中通信量最大的部分之一。
  - **梯度裁剪 (Gradient Clipping)：** 在参数更新之前，通常会对梯度进行裁剪，以防止梯度爆炸（即梯度变得过大，导致训练不稳定）。
- **优化器更新：** 聚合后的梯度被传递给优化器（如AdamW）。优化器根据其更新规则（包括学习率、动量等）来调整模型的参数。
- **学习率调度器：** 学习率会根据预设的调度策略（如线性衰减、余弦衰减、warmup等）动态调整，以在训练的不同阶段保持最佳的学习效率。

6. **混合精度训练 (Mixed Precision Training)**

- **背景：** 传统的浮点数（FP32）计算精度高，但占用显存大，计算慢。半精度浮点数（FP16或BF16）占用显存小，计算快。
- 实现：
  - **大部分计算使用FP16/BF16：** 模型的权重、激活值和梯度主要以FP16/BF16存储和计算。
  - **部分关键计算使用FP32：** 尤其是在计算损失和更新参数时，会切换回FP32以保持数值稳定性，避免FP16精度不足导致的溢出或下溢。
  - **Loss Scaling：** 为了解决FP16精度不足可能导致梯度下溢（梯度值太小而变成0）的问题，通常会对损失进行放大（scale），反向传播时再缩小。

7. **训练监控与日志记录**

- 实时指标：

  持续记录和显示训练过程中的关键指标，如： 

  - **损失值 (Loss)：** 训练损失（Train Loss）是核心指标，反映模型拟合训练数据的程度。理想情况下，损失值会随着训练的进行而逐渐下降。
  - **学习率 (Learning Rate)：** 跟踪学习率的变化，确保调度器按预期工作。
  - **GPU利用率/显存占用：** 监控硬件资源的使用情况，发现瓶颈或优化空间。
  - **吞吐量：** 每秒处理的token数量，反映训练效率。

- **日志记录：** 将上述指标以及其他重要信息（如训练步数、时间戳、模型保存点等）写入日志文件，方便后续分析。

- **可视化：** 使用TensorBoard、Weights & Biases等工具将指标可视化，以图表形式直观展示训练趋势。

8. **定期保存模型检查点 (Checkpointing)**

- **目的：** 为了防止训练中断（如硬件故障、停电）时前功尽弃，以及在训练过程中评估模型或进行后续微调，需要定期保存模型的权重和优化器状态。
- **策略：** 可以按训练步数、按时间间隔、或者当损失值下降到某个阈值时进行保存。
- **增量保存：** 通常会保存最新的几个检查点，或者只保存最佳性能的检查点。

9. **训练中断与恢复**

- **处理机制：** 训练系统需要具备从上次保存的检查点恢复训练的能力。当训练意外中断时，可以从最近的检查点加载模型权重、优化器状态、学习率调度器状态以及训练步数等，然后继续训练。

10. **评估 (Evaluation)**

- **离线评估：** 预训练通常不直接在下游任务上进行评估，因为预训练的目标是学习通用能力，而非特定任务。但有时会周期性地在一些简单的语言理解任务（如MMLU的一个子集、Perplexity）上进行评估，以大致了解模型学习进展。
- **困惑度 (Perplexity)：** 在语言模型中常用的一个指标，衡量模型预测下一个词的能力，困惑度越低越好。





## 5、Deepspeed训练加速框架

#### **训练框架多用llama-facotry**

Deepspeed是由微软开发的一款开源深度学习优化库，旨在提高大规模模型训练的效率和可扩展性。该框架采用多种技术手段来加速训练，其中包括模型并行化、梯度累积、动态精度缩放、本地模式混合精度等。

此外，Deepspeed还提供了一些辅助工具，如分布式训练管理、内存优化和模型压缩等，以帮助开发者更好地管理和优化大规模深度学习训练任务。

- ZeRO（Zero Redundancy Optimizer）零冗余优化器

ZeRO是一种内存优化技术，主要目标是降低训练期间的内存占用、通信开销和计算负载。

该技术可以**消除数据并行进程中的内存冗余**，通过在数据并行进程之间**划分模型状态参数、梯度和优化器状态**，而不是复制它们。此外，ZeRO还使用**动态通信调度在分布式设备之间共享必要的状态**，以保持数据并行的计算粒度和通信量。基于ZeRO，DeepSpeed实现了数据并行、流水线并行和张量切片模型并行等方式的训练，以提高显存和计算效率，并能够训练具有万亿个参数的模型。

- 3D并行

数据并行和模型并行可能会导致内存冗余，因此DeepSpeed采用基于ZeRO的3D并行来优化显存利用和计算效率。该技术将模型状态参数、梯度和优化器状态按照3D方式划分，并使用动态物理内存分配来减少内存占用。

- **ZeRO-1**：优化器状态分片。
- **ZeRO-2**：梯度分片。
- **ZeRO-3**：参数分片，支持千亿级模型训练（DeepSpeed实现）。
- 梯度累积

DeepSpeed还使用梯度累积来提高批量大小，从而提高训练效率。梯度累积可以通过多次前向传递和反向传递来累积梯度，从而实现较大的批量大小。

例如，假设原始的批量大小为1024，但由于硬件限制，只能处理128的批量大小。那么可以将1024个样本分成8个小批量，每个小批量包含128个样本。对于每个小批量，**进行一次前向传播和反向传播，但不立即更新权重**，而是将**得到的梯度累积**起来。当8个小批量都处理完毕后，将累积的梯度除以8（即小批量的数量），然后用这个**平均梯度来更新模型**参数。

## 6、微调并行化策略

一、**数据并行**（Data Parallelism）

数据并行是最直观且广泛应用的并行计算策略之一。其核心思想是将**整个数据集分割成多个小块（称为“批次”）**，每个GPU或计算节点处理一个或多个批次的数据。在每次迭代中，**每个节点独立计算梯度**，并通过某种形式的全局通信（如参数服务器或AllReduce操作）来同步这些梯度，以确保所有节点上的模型参数保持一致。

PyTorch的`DistributedDataParallel`（DDP），TensorFlow的`MirroredStrategy`。

二、**模型并行**（Model Parallelism）
模型并行则侧重于将**模型的不同部分分配到不同的计算设备上**。这通常发生在单个模型层或模块太大，无法完全放入单个GPU内存时。通过将模型拆分成多个部分，并在多个设备间分配这些部分，可以显著减少每个设备的内存需求，同时利用多设备加速计算。

- **层间并行（Pipeline Parallelism）**：按层垂直拆分（如GPU1处理前10层，GPU2处理后10层）。
- **张量并行（Tensor Parallelism）**：将单层权重矩阵横向/纵向拆分（如NVIDIA 的Megatron-LM的矩阵分块）。

三、**流水线并行**（Pipeline Parallelism）
将模型按层划分为多个**阶段（stage）**，每个阶段部署到不同设备，数据按**微批次（micro-batch）** 流式处理以提升设备利用率，形成一个流水线。**每个设备处理模型的一个或多个连续层，并将输出传递给下一个设备**。这种方式既减少了每个设备的内存需求，又通过并行处理提高了整体计算效率。

当模型较大、单卡 GPU 难以运行整个模型时，可以使用 **模型并行（Model Parallelism）** 或更现代的 **Tensor Parallelism、Pipeline Parallelism** 来进行分布式训练。下面是一些常见方法和其代码示例：

------

✅ 方式一：使用 HuggingFace + Accelerate 自动模型并行（推荐）

如果你用的是 HuggingFace Transformers，可以使用 `accelerate` 自动处理模型分布和多卡训练。

安装：

```bash
pip install accelerate
accelerate config
```

示例代码：

```python
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

# 自动分布到多个GPU上
model = AutoModelForCausalLM.from_pretrained("bigscience/bloom", device_map="auto", torch_dtype=torch.float16)
tokenizer = AutoTokenizer.from_pretrained("bigscience/bloom")

inputs = tokenizer("你好，模型并行！", return_tensors="pt").to("cuda:0")
outputs = model.generate(**inputs, max_new_tokens=50)
print(tokenizer.decode(outputs[0]))
```

------

✅ 方式二：使用 DeepSpeed 的 ZeRO + Pipeline 并行

安装 DeepSpeed：

```bash
pip install deepspeed
```

配置文件（`ds_config.json`）：

```json
{
  "train_batch_size": 8,
  "fp16": {
    "enabled": true
  },
  "zero_optimization": {
    "stage": 3,
    "offload_param": {
      "device": "cpu"
    }
  },
  "pipeline": {
    "enabled": true,
    "partitions": 2
  }
}
```

启动命令：

```bash
deepspeed --num_gpus=2 train.py --deepspeed ds_config.json
```

------

✅ 方式三：使用 PyTorch 手动模型并行（适用于简单模型）

适用于自定义模型的粗粒度模型并行：

```python
import torch
import torch.nn as nn

# 假设你有两张GPU
device0 = torch.device("cuda:0")
device1 = torch.device("cuda:1")

# 手动将模型的部分放到不同的GPU上
class ModelParallelNN(nn.Module):
    def __init__(self):
        super().__init__()
        self.layer1 = nn.Linear(1024, 2048).to(device0)
        self.relu = nn.ReLU().to(device0)
        self.layer2 = nn.Linear(2048, 1024).to(device1)

    def forward(self, x):
        x = x.to(device0)
        x = self.relu(self.layer1(x))
        x = x.to(device1)
        x = self.layer2(x)
        return x

model = ModelParallelNN()
input_data = torch.randn(8, 1024).to("cuda:0")
output = model(input_data)
```

------

✅ 方式四：使用 Megatron-LM（用于超大模型）

[**Megatron-LM**](https://github.com/NVIDIA/Megatron-LM) 支持 Tensor Parallelism + Pipeline 并行，并行效率高，但需要一定配置能力。

------

小结：

| 方案                        | 适用场景                      | 优点                       | 缺点                       |
| --------------------------- | ----------------------------- | -------------------------- | -------------------------- |
| `accelerate` + Transformers | HuggingFace 模型加载推理/微调 | 简单、自动设备映射         | 可控性差                   |
| DeepSpeed                   | 大模型训练/微调               | 高效支持 ZeRO 和 Pipeline  | 需要额外配置和工具链       |
| 手动模型并行                | 自定义小型模型                | 控制灵活                   | 手动管理设备迁移、容易出错 |
| Megatron-LM                 | 超大预训练模型                | 高效训练，多种并行方式支持 | 配置复杂、上手门槛较高     |



## 7、vLLM-模型推理框架

- 最常见的框架：**vLLM**或者**tensorRT-LLM(英伟达)**、**SGLang、LightLLM**、**PyTorch**、**TensorFlow**、**Hugging Face Transformers**、**NVIDIA Triton Inference Server**、**DeepSpeed（微软）**、**OpenVINO（英特尔）**
- vllm的使用方法（一句话：将微调后的模型用Vllm进一步封装）：
  - vLLM通过借鉴虚拟(Virtual)内存的原理，采用固定大小的块和动态映射的方式，有效地管理了内存，减少了内存浪费。从原理实现来看，我个人觉得 其命名中的 v 即这里的虚拟(Virtual)含义。
- **vllm的优势（主要优势）：**
  - PagedAttention技术
    - **内存管理**：vLLM通过**PagedAttention**技术有效管理注意力中缓存的键（K）和值（V）张量，减少内存占用。该技术将KV缓存分块存储，允许在非连续空间中高效访问，从而显著提高内存利用率
  - 动态批处理（Dynamic Batching）
    - **连续批处理**：vLLM支持动态批处理，可以在推理过程中根据输入的长度动态调整批次大小，避免了静态批处理带来的GPU算力浪费。

## 8、推理高并发实现

- **使用异步框架**：通过异步编程模型（如使用`asyncio`、`FastAPI`等）处理请求，可以在等待I/O操作时继续处理其他请求，从而提高并发处理能力，即来解耦模型推理和请求处理，避免阻塞。。
- **批量处理请求**：将多个请求合并为一个批处理请求，减少模型调用的次数，提高吞吐量。
- 示例：
  - 如果单次推理耗时 10ms，处理 10 个请求单独推理需要 100ms；而通过批处理可以将时间缩短到接近 10ms。
- **使用高效的推理框架**：选择高性能的推理框架（如vLLM、TensorRT等），能够显著提升推理速度和效率。
- **多进程部署服务**: 将模型通过不同的进程进行部署运行（也就是将请求分发到多个实例，避免单个实例的过载），然后可以使用**Nginx等负载均衡器**，协调运行。
- **缓存机制**：对于重复的输入数据，直接返回缓存的结果，避免重复计算



## 9、强化微调

**RLHF**

从**人类反馈中进行强化学习**, 该方法总体上包括三个步骤:

- 步骤1: **监督学习**, 预训练的语言模型在少量已标注的数据上进行调优, 以学习从给定的 prompt 列表生成输出的有监督的策略（即 SFT 模型）；
- 步骤2: **训练奖励模型**（reward）: 标注者们对相对大量的 SFT 模型输出结果进行收集并排序, 这就创建了一个由比较数据组成的新数据集. 在此数据集上训练新模型, 被称为训练奖励模型（Reward Model, RM）；
- 步骤3: **强化学习**（PPO算法）: RM 模型用于进一步调优和改进 SFT 模型, PPO 输出结果的是策略模式.
- 步骤 1 只进行一次, 而步骤 2 和步骤 3 可以持续重复进行: 在当前最佳策略模型上收集更多的比较数据, 用于训练新的 RM 模型, 然后训练新的策略. 接下来, 将对每一步的细节进行详述.

**近端策略优化（PPO）** 

- **样本收集**：生成响应并收集有关其相关状态和预计奖励的数据。
- **优势估计**：计算每个响应的“优势”，确定响应与平均值相比好多少或差多少。
- **策略更新**：使用 PPO 的目标函数调整 LM 的策略以最大化预期奖励。
- **剪辑机制**：通过 PPO 的剪辑机制防止策略剧烈变化，确保稳定学习。

**缺点**:训练一个全新的模型来为原始语言模型提供反馈（奖励）是昂贵且数据密集型的。这是该方法的主要挑战。

**直接偏好优化（DPO）：一种更简单的替代方案**

​	**偏好数据收集**：与 RLHF 类似，DPO 首先收集人类对模型响应的偏好。

​	**隐性奖励模型**：DPO 不是明确地训练奖励模型，而是使用二元交叉熵损失函数，通过简单的分类目标来拟合隐性奖励模型。

​	**策略更新**：DPO 通过增加首选响应相对于非首选响应的相对对数概率来更新策略。这是通过动态的、每个示例的重要性加权来实现的，以防止模型退化。

​	**优化**：通过将偏好损失直接定义为策略的函数，DPO 可以使用直接的训练技术优化策略，避免强化学习的复杂性。    

 **PPO 方法：**

- 训练单独的奖励模型来预测人类偏好。
- 使用这个奖励模型来微调语言模型。

**DPO 方法：**

- 直接使用 KL 约束推导出最优策略，无需单独的奖励模型。
- 利用推导出来的公式直接优化语言模型的策略。

背后的数学都是通过KL散度损失计算的,是一种用于量化两个连续概率分布之间差异的指标。



**RLAIF**

基于AI反馈的强化学习

RLAIF通过AI生成反馈（如LLM对齐的偏好标签），弥补了这一缺陷，同时保持与人类价值观的一致性。

大型语言模型（LLM）可自动生成偏好数据，与人类反馈结合，共同优化模型在“有助性”（helpful）和“无害性”（harmless）等目标上的表现。



**RL**

强化学习（RL）：系统会根据大模型的每次行动，给予一定的奖励或惩罚，让模型通过学习这些反馈来调整自己的策略，完全自动化执行，无需人工干预。

1、RL无需依赖人工标注数据，极大减少了模型训练成本，这让估值千亿专门做数据标注的公司Scale CEO崩不住，跳出来呼吁要对DeepSeek进行芯片管制。

2、RL更适合处理开放性和探索性任务，通过奖惩机制，模型可以尝试不同的策略，最终收敛到奖励更高的输出模式，从而生成更创新或更符合场景需求的回答。

3、RL优化长期收益和多步决策，能够权衡即时奖励与长期收益（如避免短视行为），更适合策略性规划的任务。

4、RL给出的答案甚至可以超越人类当前的水平，有时候人类已经无法评估AI的输出是好是坏，监督微调反而成为阻碍。

DeepSeek-R1模型训练过程中，设计了3套奖励函数让模型自我进化，来发展推理能力的潜力。奖励函数分别是：准确性奖励、格式奖励、语言一致性奖励。

**DeepSeek-R1-Zero**的研究发现，**通过强化学习之后，模型逐渐涌现出“顿悟”的能力，并能够自我验证、反思生成思维链（CoT）。**

DeepSeek-R1研究发现，通过强化学习获得的**复杂推理能力**（如数学解题、代码生成），通过监督微调（SFT）可以迁移到更小的密集模型（如Qwen、Llama系列）中，以低成本提升小模型的性能。

## 10、生成模型的采样策略

**决定了如何从模型的概率分布中选择下一个token**

| **策略**         | **多样性** | **连贯性** | **速度** | **典型应用**       |
| ---------------- | ---------- | ---------- | -------- | ------------------ |
| 贪心搜索         | 低         | 高         | 最快     | 代码生成、事实问答 |
| Top-k + Top-p    | 中高       | 中高       | 快       | 通用对话、创意写作 |
| 束搜索（Beam=4） | 低         | 最高       | 慢       | 机器翻译、文本摘要 |
| 对比搜索         | 高         | 高         | 中等     | 技术报告、论文生成 |

**Top-k采样**

Top-k采样是指在生成过程中，模型不是选择具有最高概率的那个词作为下一个词，而是从概率最高的k个候选词中选择。这里的"k"是一个超参数，由用户指定。**这样做可以避免总是选择最有可能的词，从而导致生成的文本过于刻板或重复。**

**示例**：

假设我们有如下单词的概率分布：

- word A: 0.1
- word B: 0.15
- word C: 0.2
- word D: 0.25
- word E: 0.3

如果我们设置k=3，则会从word C, word D, 和 word E这三个选项中随机选取一个作为下一个词。

**Top-p采样 (Nucleus Sampling)**

Top-p采样（又称为Nucleus采样）则是指选择那些**累积概率达到或超过预先设定阈值p的候选词**。换句话说，它会**选择最小**的一组词，使得这组词的概率之和至少为p。

**示例**：

继续使用上面的例子，如果设定了p=0.5，则会选择word D和word E，因为它们的概率之和达到了0.55。然后从这**两个词中按概率比例进行随机选择**。

**区别**

- **选择方式**：Top-k直接选择前k个概率最大的词汇；而Top-p选择的是那些累积概率达到某个阈值的所有词汇。
- **灵活性**：Top-p更灵活，因为它根据累积概率来确定词汇数量，而不是固定的数量。
- **多样性**：Top-p倾向于产生更多的多样性，因为它可能会包括比top-k更多的候选词，前提是这些词的累积概率超过了给定的阈值p。



## 11、向量数据库

​	数据库有很多种，**Faiss（Mate开源）**只是其中的一种，其他的向量数据库比如：**Milvus和Chroma（不是谷歌）、Elasticsearch（混合检索）、Pinecone**

- 以下是针对Faiss、Milvus、Chroma、Elasticsearch和Pinecone五大向量数据库的详细对比分析，结合其技术架构、核心特性及适用场景，助你精准选型：

  ---

  ### 📊 **一、综合对比表**
  | **维度**     | **Faiss**                 | **Milvus**                          | **Chroma**            | **Elasticsearch**              | **Pinecone**                      |
  | ------------ | ------------------------- | ----------------------------------- | --------------------- | ------------------------------ | --------------------------------- |
  | **技术架构** | 单机库（无分布式）        | 分布式云原生（存储计算分离）        | 轻量级嵌入库          | 分布式搜索引擎（非专用向量库） | 全托管云服务                      |
  | **核心优势** | GPU加速、高性能小规模搜索 | 超大规模数据、混合查询（向量+标量） | 极简API、快速原型开发 | 全文搜索+基础向量扩展          | 超低延迟、自动扩缩容              |
  | **数据规模** | ≤千万级                   | 万亿级⭐                             | ≤百万级               | ≤十亿级                        | 十亿级+                           |
  | **查询延迟** | 毫秒级（依赖硬件）        | 毫秒级（分布式优化）                | 毫秒-秒级             | 秒级                           | **亚秒级**⭐                       |
  | **学习成本** | 中（需调参）              | 高（需运维集群）                    | **低**⭐               | 高（复杂查询语法）             | **极低（开箱即用）**⭐             |
  | **部署模式** | 嵌入代码库                | 自托管/K8s                          | 本地或轻量服务        | 自托管/云托管                  | SaaS托管                          |
  | **典型场景** | 学术实验、小型RAG         | 电商多模态检索、金融风控            | 本地开发测试          | 日志分析+文本搜索              | 生成式AI实时应用（如ChatGPT增强） |

  ---

  ### 🔍 **二、各数据库深度解析**
  1. **Faiss（Meta开源）**  
     - **核心机制**：基于近似最近邻（ANN）算法（如IVF、PQ、HNSW），通过量化压缩与GPU加速提升搜索效率。  
     - **优势**：  
       - 极致性能：单机亿级向量搜索（GPU支持）。  
       - 灵活索引：支持复合索引（如`PCA+IVF+PQ`）平衡精度与内存。  
     - **局限**：  
       - 无分布式/容灾，需手动分片。  
       - 数据更新需重建索引，不适合流数据。  
     - **场景**：研究论文实验、小型知识库检索（搭配LangChain）。

  2. **Milvus（开源分布式）**  
     - **架构亮点**：  
       - 四层分离架构（接入/协调/计算/存储），支持PB级数据。  
       - Knowhere引擎整合Faiss/HNSW，优化SIMD指令与二进制向量检索。  
     - **优势**：  
       - 混合查询：支持`WHERE category='tech' AND vector_distance<0.5`类过滤。  
       - 动态分区：按业务标签分区提升查询效率。  
     - **局限**：部署复杂（依赖etcd/MinIO/Pulsar），运维成本高。  
     - **场景**：电商跨模态搜索（图+文）、生物基因序列匹配。

  3. **Chroma（轻量级嵌入库）**  
     - **定位**：简化版向量存储，专注原型开发。  
     - **优势**：  
       - 极简API：5行代码完成向量存储与检索。  
       - 无缝集成：兼容LangChain、LlamaIndex等AI工具链。  
     - **局限**：  
       - 无分布式/高级索引，性能瓶颈明显（百万级数据延迟显著）。  
       - 社区资源较少（非企业级应用导向）。  
     - **场景**：本地LLM应用测试、教学演示。

  4. **Elasticsearch（全文搜索引擎）**  
     - **向量支持**：通过`dense_vector`字段实现，非原生优化。
     -  **特点**：
       - 本质是倒排索引 + 向量检索（通过 dense_vector 类型）
       - 支持 ANN（KNN）与 hybrid 检索（向量 + 关键词混合）
       - 广泛用于日志、监控、搜索引擎
     - **优势**：  
       - 融合全文与向量搜索（如`BM25+余弦相似度`混合排序）。  
       - 生态成熟：Kibana可视化、APM监控等配套完善。  
     - **局限**：  
       - 向量检索性能弱于专用库（十亿级延迟高）。  最多支持 1024 维，性能一般。
       - 资源消耗大（内存密集型）。  
     - **场景**：日志分析中异常检测（文本+向量）、安全情报系统。

  5. **Pinecone（全托管云服务）**  
     - **核心技术**：  
       - 自动ANN索引优化（HNSW+IVF-PQ混合）。  
       - 全局分布式缓存，QPS可达10万+。  
     - **优势**：  
       - 零运维：自动扩缩容/索引优化。  
       - 元数据过滤：`filter={"user_id":"123"}`加速个性化检索。  
     - **局限**：  
       - 闭源，定制能力弱。  
       - 成本高（$70/百万向量/月）。  
     - **场景**：实时推荐系统、大模型记忆增强（如ChatGPT插件）。

  ---

  ### 🎯 **三、场景化选型建议**
  - **学术/小规模原型** → **Faiss**（GPU加速）或 **Chroma**（快速上手）。  
  - **企业级多模态系统** → **Milvus**（开源灵活，支持混合查询）。  
  - **生成式AI生产环境** → **Pinecone**（亚秒延迟，免运维）。  
  - **文本为主+向量辅助** → **Elasticsearch**（生态成熟）。  
  - **成本敏感型项目** → **自建Milvus**（避免云服务费用）。

  > 💡 **关键优化策略**：  
  > - **降维+量化**：PCA将768维→256维，PQ压缩内存（Faiss/Milvus适用）。  
  > - **冷热分离**：Milvus动态分区管理热数据，Pinecone自动分层存储。

  ---

  ### 💎 **总结**  
  五大向量数据库定位泾渭分明：**Faiss**是性能利器但需自建生态；**Milvus**是开源扛鼎者，适合复杂企业需求；**Chroma**降低入门门槛；**Elasticsearch**满足混合搜索；**Pinecone**以付费换极致效率。**2025年趋势看，云原生（Pinecone/Milvus）与AI原生索引（如动态量化）将成为主流**，选型时需权衡规模、实时性、团队运维能力。

  

  Faiss数据库**索引方式**（也就是：向量数据库查询有什么优化方式）**适合处理大规模高维数据**

  - **Flat Index**（暴力检索）：使用L2**（欧几里得）距离**进行暴力搜索。它直接在**所有向量中进行比较**，适合小型数据集。

  - **IVF（Inverted File Index）**：使用**倒排文件（IVF）将数据集划分为多个簇**，每个簇对应一个质心。搜索时只需在相关簇中查找，显著提高速度。**适合大规模数据集，搜索速度快。**需要设置簇的数量（nlist）

  - **HNSW（Hierarchical Navigable Small World Graphs）**：使用**多层次可导航小世界**（HNSW）图进行 **ANN（近似最近邻搜索）**，能够快速找到相似向量。提供高效的搜索速度和较高的准确性，适合实时应用。

  - **PQ（Product Quantization，乘积量化）**：将向量拆分为多个子向量，分别进行量化编码。存储时只保存编码结果，查询通过查表方式进行近似计算。显著降低内存使用，适合大规模数据集。

    Faiss 支持多种距离度量方式，常用的包括：

    - **L2 距离** （欧氏距离）：衡量两个向量之间的几何距离。
    - **内积（Inner Product）** ：衡量两个向量的方向相似性，常用于余弦相似度计算。

  数据以 **JSON** 文档的形式写入 **Elasticsearch**。

- Elasticsearch 提供了 **混合检索** （Hybrid Search）的能力，允许用户结合多种检索方法（如全文搜索、向量搜索、过滤条件等），从而在不同维度上对数据进行综合排序和匹配。

  | 特性                  | Milvus                         | Elasticsearch（8+）         |
  | --------------------- | ------------------------------ | --------------------------- |
  | 原生向量支持          | ✅ 专为向量设计                 | ❌（8+才引入向量支持）       |
  | 索引类型支持          | Flat, IVF, PQ, OPQ, HNSW 等    | Flat（script）+ HNSW（knn） |
  | 精度控制              | 可通过 nlist/nprobe 灵活调节   | 可通过 num_candidates 控制  |
  | 插入数据量支持        | 数十亿级                       | 较有限，百万级较稳          |
  | 多模态/结构化数据支持 | ✅（文本、图像、音频等）        | ✅（结构化强，向量弱）       |
  | 部署/维护复杂度       | 中等（需学习、依赖 Pulsar 等） | 低（企业常用）              |
  | 是否开源              | ✅（Apache 2.0）                | ✅（Elastic License）        |



## 12、FastAPI RAG 部署

我们来一步步实现一个 **支持流式输出的 FastAPI RAG系统部署方案**，包括：

1. ✅ 流式输出的 FastAPI API（StreamingResponse）
2. ✅ 容器化部署（Dockerfile）
3. ✅ Kubernetes 部署模板（Deployment + Service）

------

🔁 1. FastAPI 流式输出（StreamingResponse）

✅ 依赖：使用 OpenAI 的流式响应（`stream=True`）

```python
# main.py
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from rag import RAGRetriever
import openai
import os
import json

class QueryRequest(BaseModel):
    query: str

app = FastAPI()

docs = [
    "ChatGPT 是由 OpenAI 开发的大型语言模型。",
    "RAG 是一种结合检索与生成的方法。",
    "FAISS 是 Facebook 开源的向量搜索库。"
]
rag = RAGRetriever(docs)

@app.post("/rag/stream")
async def stream_rag_answer(request: Request):
    body = await request.json()
    query = body.get("query")

    context = "\n".join(rag.retrieve(query))
    prompt = f"根据以下文档回答问题：\n\n{context}\n\n问题：{query}\n\n答案："

    def stream_generator():
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            stream=True,
            messages=[
                {"role": "system", "content": "你是一个有用的问答助手"},
                {"role": "user", "content": prompt}
            ]
        )
        for chunk in response:
            delta = chunk["choices"][0]["delta"]
            content = delta.get("content")
            if content:
                yield content

    return StreamingResponse(stream_generator(), media_type="text/plain")
```

访问 `/rag/stream` 会以文本流方式输出模型生成的回答。

------

🐳 2. Dockerfile

✅ 示例 Dockerfile：

```Dockerfile
# 使用官方 Python 镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 复制依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动服务
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

✅ requirements.txt 示例

```
fastapi
uvicorn
openai
faiss-cpu
sentence-transformers
```

✅ 构建和运行容器

```bash
docker build -t rag-api .
docker run -p 8000:8000 -e OPENAI_API_KEY=your_openai_key rag-api
```

------

☸️ 3. Kubernetes 部署模板

✅ deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-api
  template:
    metadata:
      labels:
        app: rag-api
    spec:
      containers:
        - name: rag-api
          image: your-dockerhub-username/rag-api:latest
          ports:
            - containerPort: 8000
          env:
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: openai-secret
                  key: api_key
```

✅ service.yaml

```yaml
apiVersion: v1
kind: Service
metadata:
  name: rag-api-service
spec:
  type: LoadBalancer
  selector:
    app: rag-api
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
```

✅ secret.yaml（用于存储 OpenAI API 密钥）

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: openai-secret
type: Opaque
stringData:
  api_key: your_openai_key_here
```

✅ 部署到集群

```bash
kubectl apply -f secret.yaml
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
```

------

✅ 补充建议

- **可选前端**：用 Next.js 或 Chat UI 前端调用 `/rag/stream` 进行流式展示。
- **认证**：加入 JWT、API Key 认证。
- **日志和监控**：集成 Prometheus + Grafana 或 Sentry。
- **生产环境建议**：使用 Gunicorn 或 `uvicorn --workers` 提升并发处理。

------

**FastAPI 的优点**

1. **高性能**：FastAPI 声称其性能与 NodeJS 和 Go 等异步框架相当，甚至在某些基准测试中超过了它们。
2. **易于使用**：FastAPI 有一个直观且简洁的 API，使得编写 API 变得简单快捷。
3. **自动文档生成**：FastAPI 自动生成交互式的 API 文档，使用了两个流行的开源项目：Swagger UI 和 ReDoc。这使得开发者无需额外的工作就可以为他们的 API 创建完整的文档。
4. **类型安全**：FastAPI 利用了 Python 的类型提示来提供更好的开发体验，包括自动的数据验证和解析。
5. **异步编程**：FastAPI 是异步的，支持异步函数（`async def`），这使得它可以处理 I/O **密集型任务**而不会阻塞其他请求。
6. **依赖注入系统**：FastAPI 提供了一套强大的依赖注入系统，使得你可以轻松管理 API 中的依赖项。
7. **路径操作装饰器**：通过使用装饰器来定义 HTTP 方法和路由，可以方便地管理和扩展 API 接口。

## 16、transformer衍生的各种解答

**编码器层**

1. 多头自注意力层：

- 作用：允许模型同时关注**不同位置的信息**，能够捕捉输入序列中**不同位置之间的依赖关系**。它通过计算每个位置与其他位置的**注意力权重**，对输入序列进行**加权求和**，从而突出重要的信息。例如，在翻译任务中，当翻译一个词时，该层可以根据上下文信息确定这个词的合适翻译。
- 工作原理：将输入向量分别通过多个**不同的线性变换**得到**多个查询（Query）、键（Key）和值（Value）**向量组。然后，对于**每个查询向量(Q)，计算它与所有键(K)向量的点积**，并通过缩放和 softmax 操作得到注意力权重。最后，将**注意力权重与相应的值(V)**向量相乘并求和，得到该查询位置的输出。

2. 前馈神经网络层：

- 作用：对多头自注意力层的输出进行进一步的**非线性变换**，以**增强模型的表达能力**。它可以**学习到输入序列中的复杂模式和特征**。
- 工作原理：通常由**两个线性层和一个激活函数（如 ReLU）**组成。第一个线性层将输入向量**映射到一个高维空间**，然后通过激活函数进行非线性变换，最后再由第二个线性层将其**映射回原来的维度**。

**解码器层**

1. 带掩码的多头自注意力层（掩码版）：

- 作用：与编码器中的多头自注意力层类似，但在计算注意力权重时会**使用掩码**来**防止当前位置关注到未来的位置**。这是因为在解码过程中，模型需要按照顺序生成输出，不能提前看到未来的信息。
- 工作原理：与编码器中的多头自注意力层基本相同，但在**计算注意力权重时会根据掩码矩阵来屏蔽不合法的注意力连接**。

2. 多头注意力层（编码器-解码器注意力）：

- 作用：**建立解码器与编码器之间的联系**，使解码器能够利用编码器提取的输入序列的信息。它可以帮助解码器更好地理解输入序列，并生成更准确的输出。
- 工作原理：将**解码器的查询向量与编码器的键和值向量进行注意力计算**，得到解码器位置与输入序列中各个位置的注意力权重。然后，根据这些**权重对编码器的输出进行加权求和**，得到与当前解码器位置相关的上下文信息。
- **对多头注意力的理解**
- 多头自注意力就是把**一个token**的完整语义表示按**指定头数分成若干部分**，每个部分分别进行**自注意力的计算**，这么做的好处就是能让每个注意力机制去优化不同的特征部分，从而均衡同一种注意力机制可能产生的偏差，让词义拥有更多元的表达，提升模型的效果。

3. 前馈神经网络层：

- 作用：与编码器中的前馈神经网络层相同，对解码器的输出进行进一步的非线性变换，以增强模型的表达能力。

- 工作原理：与编码器中的前馈神经网络层基本一致。

  在 Transformer 中，残差连接和规范化层起着重要的作用：

  **残差连接（ResNets）——何凯明**

  1. 作用：

  - **缓解梯度消失问题。**在深度神经网络中，随着层数的增加，梯度在反向传播过程中可能会逐渐减小，导致较深层的权重更新困难。残差连接为信号提供了一条直接的路径，使得梯度可以更容易地反向传播到较深层，从而缓解梯度消失问题。
  - **提高模型的性能和收敛速度**。即当残差为零时，模型可以直接学习输入的恒等映射。
  - **容易捕捉更复杂的特征和模式**。允许模型学习输入和输出之间的微小变化，而不是完全学习一个新的表示，这有助于模型捕捉更复杂的特征和模式。

  2. 工作原理：

  - 在 Transformer 的每个子层（多头自注意力层和前馈神经网络层）之后，将输入直接加到子层的输出上。

  **规范化层**

  主要目的是减少内部**协变量移位**（internal covariate shift），从而加快训练速度并提高模型性能。层规范化与批量规范化（Batch Normalization）不同，它不依赖于 mini-batch，而是针对每个样本的**所有特征**进行规范化

  1. 作用：

  - **加速模型的收敛速度**。通过对输入进行规范化，可以使得模型的优化过程更加稳定，减少训练过程中的震荡，从而加速模型的收敛速度。
  - **提高模型的泛化能力**。规范化可以限制模型的参数范围，防止过拟合，提高模型在不同数据集上的泛化能力。
  - **不需要 mini-batch**:层规范化不依赖于 mini-batch 的大小，**适用于任何批量大小**，甚至可以单独对一个样本进行规范化。
  - **并行处理**:层规范化可以并行处理每个样本，这在使用 GPU 加速训练时特别有用
  - **不同层的输入分布更加稳定**。在深度神经网络中，由于每一层的参数更新都会影响后续层的输入分布，这可能导致内部协变量偏移问题。规范化层可以缓解这个问题，使得不同层的输入分布更加稳定。

  2. 工作原理：

  - 层规范化的基本思想是在每个样本的**所有特征上进行规范化**，而不是在 mini-batch （batch_size）上。
  - 这意味着对于每个样本的输入向量，层规范化计算该向量的均值和方差，并使用这些统计量来规范化向量中的每个元素

- **RMSNorm（均方根层归一化）比起LayerNorm（层归一化）**

  - 简单来说就是，虽然二者的时间复杂度一致，但是RMSNorm比起LayerNorm确实**减少了减去均值以及加上bias的计算**，这在目前大模型预训练的计算量下就能够体现出训练速度上的优势了，并且RMSNorm在模型效果上的表现并不弱于LayerNorm

  ##### **掩码**：**自注意力掩码**可以用于控制模型在自注意力机制中对哪些位置进行关注，其中包括填充掩码和因果掩码（自回归掩码）。

  - **填充掩码**帮助模型了解哪些位置是真实数据，哪些位置是填充数据；当**批量处理变长序列**时，**短序列会被填充至相同长度**。<pad>
  - **因果掩码（自回归掩码）**确保模型按照正确的顺序生成序列，即只能看到当前位置之前的信息；**解码器**需要**逐个预测**输出序列的词。
  - **任务特定掩码（如MLM）**：BERT的掩码语言模型MLM，随机掩码输入序列中的部分词（如替换为`<mask>`），模型需预测被掩码的词。

  **为什么Transformer用layernorm而不batchnorm?**

  1. **序列长度变化**

  - **BatchNorm**：BatchNorm通常用于计算机视觉任务，针对一个批次样本同一个特征进行归一化，它通过对mini-batch内的样本进行规范化来减少内部协变量偏移。这意味着BatchNorm假设输入数据具有固定的维度，例如图像的尺寸。
  - **LayerNorm**：LayerNorm则是针对一个样本的不同特征进行归一化，每一层的激活值进行规范化，而不是针对mini-batch。这意味着它适用于序列数据，即使序列长度发生变化，LayerNorm仍然能够很好地工作。

  2. **并行计算**

  - **BatchNorm**：BatchNorm需要跨样本进行统计计算，这意味着在并行处理时需要同步不同设备之间的统计数据。
  - **LayerNorm**：LayerNorm仅在单个样本内进行规范化，因此不需要跨设备同步，更适合并行计算。

  3. **适应小批量训练**

  - **BatchNorm**：BatchNorm的效果依赖于mini-batch的大小，小批量训练可能导致不稳定的结果。

  - **LayerNorm**：LayerNorm不受mini-batch大小的影响，即使在小批量训练时也能提供稳定的性能。

    4.**训练稳定性**

  - **BatchNorm**：BatchNorm可能会受到mini-batch内样本分布变化的影响，特别是在训练初期，这可能导致训练不稳定。

  - **LayerNorm**：LayerNorm在训练过程中更加稳定，因为它独立于mini-batch的统计信息。

  **Transformer 模型中，注意力计算后面使用了两个 FFN 层，为什么第一个 FFN层先把维提升，第二个 FFN 层再把维度降回原大小?**

  1. **非线性变换**：
     - 第一层通过一个线性变换（通常是全连接层）和一个激活函数（例如 ReLU 或 GELU）引入非线性。这有助于模型学习更复杂的特征表示。
  2. **增加模型容量**：
     - 提升维度可以增加模型的容量，使得模型能够学习到更多的信息和复杂的映射关系。这种“扩张-收缩”的结构让模型能够在不显著增加参数数量的情况下增加其表达能力。
  3. **减少过拟合风险**：
     - 尽管增加了中间层的维度，但通过最终将维度降回原来的大小，可以在一定程度上控制模型的整体复杂度，从而减少过拟合的风险。
  4. **并行处理优势**：
     - 在 Transformer 中，FFN 层是完全位置无关的，这意味着它们可以在不同的位置嵌入,独立的并行执行。因此，即使增加了一些额外的计算量，也可以通过并行化来抵消这部分影响。

  **Transformer架构有哪些缺陷以及有哪些优化方法**

  （1）注意力机制的优化：计算耗时大，引入稀疏注意力（2）位置编码的优化：旋转位置编码等

## 17、HMM

1.1 HMM**模型的输入和输出**

它一般以文本序列数据为输入, 以该序列对应的隐含序列为输出.

1.2 HMM**模型的作用**

- 在NLP领域, HMM用来解决文本序列标注问题. 如分词, 词性标注, 命名实体识别都可以看作是序列标注问题.
- HMM的两大假设:
- **齐次马尔科夫假设**: **任一时刻 t 的某一状态只依赖于其前一时刻的状态**, 与其它时刻的状态及观测无关, 也与时刻 t 无关.
- **观测独立性假设**: 任意时刻的观测状态只依赖于该时刻的隐藏状态, 与其他状态无关.

1.3 HMM**模型使用过程简述**：

- 首先, HMM模型表示为: lambda = HMM(A, B, pi), 其中A, B, pi都是模型的参数, 分别称作: **转移概率矩阵, 发射概率矩阵和初始概率矩阵.**
- 接着, 我们开始训练HMM模型, 语料就是事先准备好的一定数量的观测序列及其对应的隐含序列, 通过极大似然估计求得一组参数, 使由观测序列到对应隐含序列的概率最大.
  - **极大似然估计**
    根据**观测到的结果**来**估计**模型算法中的**未知参数**
     	通过**极大化概率**事件，来估计**最优**参数
- 在训练过程中, 为了简化计算, 马尔可夫提出一种假设: 隐含序列中每个单元的可能性只与上一个单元有关. 这个假设就是著名的隐含假设.
- 训练后, 我们就得到了具备预测能力的新模型: lambda = HMM(A, B, pi), 其中的模型参数已经改变.
- 之后给定输入序列(x1, x2, ..., xn), 经过模型计算lambda(x1, x2, ..., xn)得到对应隐含序列的条件概率分布.
- 最后, 使用维特比算法从隐含序列的条件概率分布中找出概率最大的一条序列路径就是我们需要的隐含序列: (y1, y2, ..., yn).

1.4 HMM**常见三个问题**

- 评估问题: 已知模型参数, 计算某一特定输出序列的概率. 通常使用前向或者后向算法解决.
- 解码问题: 已知模型参数, 求对应的概率最大的隐含状态序列. 通常使用Viterbi算法解决.
- 学习问题: 已知观测序列, 求使得该序列概率最大的模型参数, 通常使用EM算法或极大似然估计解决.

## 18、CRF

​	2.1 CRF**模型的输入和输出**

- CRF(Conditional Random Fields), 中文称作条件随机场, 同HMM一样, 它一般也以文本序列数据为输入, 以该序列对应的隐含序列为输出.

  2.2 CRF**模型的作用**

- 同HMM一样, 在NLP领域, CRF用来解决文本序列标注问题. 如分词, 词性标注, 命名实体识别.

  2.3 CRF**模型使用过程简述**

- 首先, CRF模型表示为: lambda = CRF(w1, w2, ..., wn), 其中w1到wn是模型参数.

- 接着, 我们开始训练CRF模型, 语料同样是事先准备好的一定数量的观测序列及其对应的隐含序列.

- 与此同时我们还需要做人工特征工程, 然后通过不断训练求得一组参数, 使由观测序列到对应隐含序列的概率最大.

- 训练后, 我们就得到了具备预测能力的新模型: lambda = CRF(w1, w2, ..., wn), 其中的模型参数已经改变.

- 之后给定输入序列(x1, x2, ..., xn), 经过模型计算lambda(x1, x2, ..., xn)得到对应隐含序列的条件概率分布.

- 最后, 还是使用维特比算法从隐含序列的条件概率分布中找出概率最大的一条序列路径就是我们需要的隐含序列: (y1, y2, ..., yn).

**3 .HMM与CRF模型之间区别**

- HMM属于生成式模型，CRF属于判别式模型
- HMM模型存在隐马假设, 而CRF不存在, 因此HMM的计算速度要比CRF模型快很多, 适用于对预测性能要求较高的场合.
- 同样因为隐马假设, 当预测问题中隐含序列单元并不是只与上一个单元有关时, HMM的准确率会大大降低, 而CRF不受这样限制, 准确率明显高于HMM.

## 19、常见的linux命令

 Linux查看内存、磁盘存储、io 读写、端⼝占⽤、进程等命令 

1）查看内存：top 

2）查看系统信息：

**uname**: 显示系统信息。

​	uname -a  # 显示所有信息

**df**: 显示磁盘使用情况。

​	df -h  # 以易读格式显示

**du**: 显示目录或文件的磁盘使用情况

​	du -sh folder  # 显示目录总大小

**free**: 显示内存使用情况。

​	free -h  # 以易读格式显示

**top**: 实时显示系统进程和资源使用/内存。

​	top

3）查看磁盘IO读写情况：iotop（需要安装⼀下：yum install iotop）、iotop -o（直接查看输出 ⽐较⾼的磁盘读写程序） 

4）查看端⼝占⽤情况：netstat -tunlp | grep 端⼝号 

**netstat**: 显示网络连接、路由表等。

​	netstat -tuln  # 显示监听端口

5）查看进程：**ps aux** 查看资源占用情况

**ps**: 显示当前进程。

​	ps aux  # 显示所有进程

或者`ps -ef` 是 查看进程关系和启动时间

**kill**: 终止进程。

​	kill 1234  # 终止PID为1234的进程

​	kill -9 1234  # 强制终止

**用户和权限**

**sudo**: 以管理员权限运行命令。

​	sudo rm -rf  folder

**chmod**: 修改文件权限。

​	chmod 755 file.txt  # 设置权限为rwxr-xr-x

**chown**: 修改文件所有者

​	chown user:group file.txt #指定当前的文件所有者为（user）和所属组（group）。

**apt** (Debian/Ubuntu): 包管理工具。

​	sudo apt update  # 更新包列表
​	sudo apt install package_name  # 安装软件包
​	sudo apt remove package_name  # 卸载软件包

**tar**: 打包和解包文件。

​	tar -cvf archive.tar folder  # 打包

​	tar -xvf archive.tar  # 解包

**查看GPU的使用情况：**

​	nvidia-smi

​	watch -n 10 nvidia-smi	 # 每 10s 显示一次显存的情况



## 20.过拟合、欠拟合

**过拟合**（Overfitting）是指在机器学习模型中，模型对训练数据的拟合过于紧密，以至于它无法很好地泛化到新的、未见过的数据上。换句话说，过拟合的模型学会了**训练数据中的噪声和细节**，而不是数据的普遍模式或规律，从而导致在**测试数据或真实世界**应用中**表现不佳**。**即在训练集上表现很好，测试集上表现不好。**

**过拟合的原因**

1. **模型过于复杂**：如果模型的参数太多或者结构过于复杂（如深度神经网络的层数太多，或决策树的深度太深），模型可能会完全记住训练数据中的每个细节，而忽略了泛化能力。
2. **训练数据不足**：当训练数据量较少时，模型容易记住训练样本的具体细节，而无法学习到一般性的规律。
3. **噪声数据的存在**：训练数据中可能包含噪声（错误的数据、异常值等），模型可能会把这些噪声误认为是有意义的模式，从而在训练数据上表现得很好，但在新数据上表现不佳。
4. **缺乏正则化**：正则化是防止模型变得过于复杂的一种手段，缺乏正则化项的模型更容易过拟合。
5. **过长的训练时间**：训练时间太长可能会导致模型过度学习训练数据的细节，从而产生过拟合。

**解决过拟合的方法**

1. **简化模型**：降低模型的复杂度，比如减少神经网络的层数或节点数、限制决策树的深度等，以防止模型过拟合。

2. **增加数据量**：更多的数据可以帮助模型更好地捕捉数据中的普遍模式，而不是记住每个细节。

3. **使用正则化**：

   - **L1/L2 正则化**：在损失函数中加入权重惩罚项，以防止模型的参数变得过大，L2 正则化通过加入权重平方和的惩罚项，使模型趋向于较小的权重值，L1 正则化通过加入权重绝对值的惩罚项，促使一些权重变为零，从而简化模型。
   - **Dropout**：尤其在神经网络中，Dropout 是一种常用的正则化方法，它在每次训练迭代中随机忽略一些神经元，以防止模型过度依赖某些路径。

4. **使用交叉验证**：通过将数据集划分为多个子集，并对模型进行多次训练和验证，可以更好地评估模型的泛化能力。

5. **早停法（Early Stopping）**：在训练过程中监测验证集的误差，当验证误差不再下降或开始上升时停止训练，以防止过度拟合训练数据。

6. **特征选择**：去掉不重要的特征，只保留与目标变量相关性强的特征，以减少模型的复杂度。

7. **模型集成方法**：如使用模型集成技术（例如 Bagging、Boosting 或 Stacking），通过结合多个模型的输出，可以减小单个大模型过拟合的影响。

8. **使用预训练和微调（Transfer Learning）**：在大模型中，使用在大型通用数据集上预训练的模型，然后对特定任务进行微调。通过微调，小的特定数据集可以有效地利用预训练模型已经学习到的知识，减少过拟合风险。

9. **调整学习率和优化器**：调整学习率或使用不同的优化器（如 Adam、AdamW、RMSprop）来避免模型在局部极小值上陷入过度训练。

10. **知识蒸馏（Knowledge Distillation）**：将大模型的知识“蒸馏”到一个更小的模型中，让小模型保持大模型的泛化能力，同时减少其复杂性和过拟合的风险。

    

**欠拟合**（Underfitting）是指机器学习模型无法充分学习训练数据中的模式或规律，从而在**训练数据和新数据上都表现不佳的情况**。换句话说，欠拟合的模型过于简单，无法捕捉到数据中的复杂结构或特征，导致预测性能较差。

**欠拟合的原因**

1. **模型过于简单**：如果模型的结构或参数数量太少，它可能无法捕捉到数据中的复杂关系。例如，用线性回归来拟合非线性数据就是一个典型的欠拟合情况。
2. **特征不足或不相关**：输入数据的特征（特征数量或质量）不足，或者特征与目标变量之间的相关性不强，模型就无法学习到有效的模式。
3. **过高的正则化**：使用正则化来防止过拟合是常见的手段，但如果正则化力度过大，模型的自由度被过度限制，导致模型欠拟合。
4. **训练数据不足或数据噪声大**：数据量过少可能导致模型无法充分学习数据的模式。此外，过多的噪声会导致模型难以找到数据中的有意义的模式。
5. **训练时间不足**：模型没有充分训练，可能没有完全学到数据中的规律，从而导致欠拟合。

**解决欠拟合的方法**

1. **增加模型复杂度**：
   - 选择更复杂的模型，例如从线性模型切换到非线性模型（如决策树、随机森林或神经网络），以更好地拟合数据中的复杂关系。
   - 增加神经网络的层数或节点数，使其能够学习更复杂的特征和模式。
2. **添加更多特征**：
   - 通过特征工程提取更多的有用特征，或者使用特征选择算法（如基于相关性的特征选择）来确保输入特征更相关。
   - 使用多项式特征或其他特征变换方法来引入非线性特征，使模型能够学习更复杂的模式。
3. **减少正则化力度**：
   - 减少正则化项的权重（如降低 L1 或 L2 正则化的系数），以允许模型学习到更多的特征。
   - 确保正则化的程度适中，只是在防止过拟合的基础上略微限制模型的复杂性。
4. **增加训练时间**：
   - 增加训练时间或迭代次数，以确保模型能够充分学习。
5. **优化算法和超参数调整**：
   - 选择更好的优化算法（如 Adam、RMSprop）以更好地训练模型。
   - 调整超参数（如学习率、批量大小等），确保模型在合理的时间内达到最佳性能。
6. **处理数据噪声**：
   - 如果训练数据中有噪声，可以考虑对数据进行预处理（如去噪、平滑）或使用鲁棒的损失函数（如 Huber 损失），以减少噪声对模型的影响。
7. **混合模型（Ensemble Methods）**：
   - 使用集成学习方法（如 Bagging、Boosting、随机森林等）来组合多个弱模型，使得整体模型具有更强的表达能力，从而减少欠拟合的可能性。



## 23.lora的介绍以及优缺点

LoRA技术的核心思想是通过将原始模型中的权重矩阵分解为低秩矩阵来减少需要学习的参数数量，从而在保持模型性能的同时，显著减少模型训练和部署的计算成本和存储需求。

**LoRA的基本概念**

LoRA的基本方法是在不改变原始模型权重的前提下，增加少量可训练的低秩矩阵参数。在训练过程中，仅更新这些新增的低秩矩阵，而不是原始的大规模权重矩阵。具体而言，给定一个原始模型权重矩阵\( W \)，LoRA通过引入两个较小的矩阵 \( A \) 和 \( B \) 来近似原始权重矩阵的变化部分。公式可以表示为：
**W' = W + （delt）W = W + A *B**

其中，\( A \) 和 \( B \) 是低秩矩阵，通常维度较小（例如，A的大小是[d, r]，B的大小是[r, k]，其中r远小于d或k），这使得LoRA能够用更少的参数来表达模型权重的变化。

**LoRA的优点**

1. **参数高效性**：相比于传统的全模型微调方法，LoRA仅需训练少量的参数（低秩矩阵），大大降低了内存和计算成本。
2. **存储需求低**：由于LoRA仅引入了一些低秩矩阵，它对存储的需求远小于全模型微调，从而更适合在资源有限的环境下使用。
3. **适应性强**：LoRA方法可以轻松集成到现有的大规模模型中（例如BERT、GPT等），适用于各种下游任务。
4. **迁移学习效率高**：LoRA保留了原始模型的能力，同时仅需对新增的低秩矩阵进行训练，这种特性使其在多任务迁移学习场景下具有很高的效率。
5. **减少灾难性遗忘**：因为原始模型的权重保持不变，仅通过引入低秩更新，LoRA减少了微调过程中对模型原有知识的损害。

**LoRA的缺点**

1. **模型推理复杂度增加**：在模型推理阶段，尽管LoRA并没有显著增加参数量，但它确实增加了一些额外的计算（涉及到低秩矩阵的乘法操作），这可能会影响推理速度。
2. **可能存在性能限制**：LoRA的性能高度依赖于低秩矩阵的秩（rank），在某些复杂任务中，使用低秩矩阵近似可能无法完全保留模型的原有能力。
3. **依赖于基础模型的质量**：LoRA的效果在很大程度上依赖于原始预训练模型的质量，如果基础模型表现较差，LoRA也很难显著提升性能。
4. **不适用于所有类型的模型**：LoRA技术主要适用于Transformer架构，对于其他类型的深度学习模型（如卷积神经网络等），其效果和适用性可能会有所限制。

## 24.灾难性遗忘

是指在机器学习，特别是深度学习中的一个现象：当一个神经网络在连续学习多个任务时，新任务的训练会导致模型在先前任务上的性能急剧下降。也就是说，模型会在学习新知识的过程中“遗忘”旧知识。这种现象在**连续学习**（也称为终身学习）或**迁移学习**场景中尤为常见。

**灾难性遗忘的原因**

灾难性遗忘的主要原因在于神经网络的**权重共享特性**。在训练新任务时，**更新的权重会影响到原有的权重设置**，从而破坏模型对旧任务的记忆。这种破坏性更新是由于常规的梯度下降优化方法在训练过程中并不区分新旧任务的目标，而是全局性地调整模型参数。

**解决灾难性遗忘的方法**

为了解决灾难性遗忘问题，研究人员提出了多种方法，主要包括以下几种策略：

1. **固定部分模型参数**（Freezing Layers）

在训练新任务时，保持模型中部分（或大部分）参数不变，仅训练最后几层或少量参数。这种方法减少了新任务对旧任务的干扰，但在一定程度上限制了模型对新任务的适应能力。

2. **正则化方法**（Regularization Methods）

正则化方法通过在优化过程中添加额外的约束，防止模型对新任务的过度适应。例如：

- **弹性权重保持（Elastic Weight Consolidation, EWC）：** EWC 通过在损失函数中增加一个正则项，惩罚对重要参数的显著变化，从而保护模型在旧任务上的知识。EWC 使用费舍尔信息矩阵来度量各参数的重要性，对重要的参数施加更大的惩罚。
- **L2 正则化（L2 Regularization）：** 对所有参数变化施加均匀的惩罚，尽量保持新任务和旧任务的权重接近。

3. **基于记忆的方法**（Memory-based Methods）

这些方法通过保留旧任务的部分数据或知识来缓解灾难性遗忘，例如：

- **经验回放（Experience Replay）：** 存储旧任务的数据样本（或特征），在训练新任务时将其与新任务的数据一同用于训练，以确保模型在新任务训练时仍然记得旧任务的信息。
- **生成回放（Generative Replay）：** 通过生成模型（如生成对抗网络，GAN）生成旧任务数据，在训练新任务时，结合生成数据和新任务数据一起训练。这种方法减少了对存储的依赖。

4. **基于结构的方法（Structural Methods）**

这些方法通过改变网络结构来适应新任务，而不干扰旧任务，例如：

- **网络扩展（Network Expansion）：** 在学习新任务时，增加新的神经网络层或模块，并保持旧模块不变，从而保留旧任务的知识。这种方法的缺点是会增加模型的复杂度和存储需求。
- **动态网络架构（Dynamic Architecture）：** 动态调整模型的架构，使得每个任务都能够使用不同的部分网络来进行学习，以减少不同任务之间的干扰。

5. **多任务学习（Multi-task Learning）**

同时训练多个任务，使得模型在每个任务上都有较好的表现。通过共享一个通用的表示层，模型能够学习到对所有任务都有用的特征。这种方法需要同时有多个任务的数据。

## 25.大模型生成文本重复解决方案

1. **调整生成参数**

- **温度（Temperature）**：通过增加温度值（如从默认的0.7增加到1.0或更高），可以使生成的内容更加随机，减少重复性。但是温度过高可能导致输出不太连贯。
- **Top-p（Nucleus Sampling）和Top-k采样**：这两种采样方法也能控制生成的多样性。较低的Top-k（如10）和Top-p（如0.9）可以生成更有创意和多样化的内容，但也会增加随机性。

2. **使用去重复的后处理方法**

- **文本后处理**：在生成文本后，使用自然语言处理技术检测并去除重复内容。例如，可以使用n-gram去重的方法，找到重复的n-gram（如连续3个词或5个词）并删除或替换。
- **相似度检测**：可以计算文本块之间的相似度（如余弦相似度或Jaccard相似度），若相似度过高，则删除或替换重复内容。

3. **提升模型的训练数据质量**

- **数据清洗**：在训练模型之前，确保训练数据中没有重复的句子或段落。清洗数据集以确保其多样性和高质量。
- **数据增强**：通过引入更多多样化的数据，或者对现有数据进行增强（如同义替换、重写句子等），可以帮助模型学习生成更丰富的内容。

4. **修改提示词和上下文**

- **优化提示词（Prompt Engineering）**：使用更具体和富有指导性的提示词，可以引导模型生成更少重复、更具创意的文本。例如，加入更多细节、特定指示或上下文信息，使得模型输出更具方向性。
- **分段生成**：将文本生成任务拆分成多个更小的部分，每部分都使用不同的提示词，或者基于前一部分的输出，逐步构建内容。这可以减少整体上的重复。

5. **微调模型**

- **对抗训练**：使用反例（例如包含重复的样本）来进行对抗训练，以降低模型生成重复内容的概率。
- **特定领域微调**：在特定领域数据上微调模型，使其更适应该领域的语言风格和用语习惯，从而减少通用生成中的重复现象。

6. **使用外部约束**

- **规则约束生成**：将生成过程与规则或模板结合，通过外部的约束条件来强制模型输出满足特定要求的文本。
- **后处理校验**：在生成的每一步都加入重复检测算法，如果检测到重复则进行干预或重新生成。

通过结合以上这些方法，可以有效地减少大语言模型生成文本的重复性，提升生成内容的质量和多样性。

## 26.TF-IDF

TF 词频是指某个词**在文档中出现的次数**,IDF逆文档频率是用来反映一个词在**整个文档集合中的常见程度**。

## 28.pipeline和joint联合抽取

- **pipeline方法流程**：
  - 先对输入的句子进行实体抽取，将识别出的实体分别组合；然后再进行关系分类.
  - 注意：这两个子过程是前后串联的，完全分离.
- 优点：
  - 易于实现，实体模型和关系模型使用独立的数据集，不需要同时标注实体和关系的数据集.
  - 两者相互独立，若关系抽取模型没训练好不会影响到实体抽取.

------

- 缺点：

  - 关系和实体两者是紧密相连的，互相之间的联系没有捕捉到.
  - 容易造成误差积累、实体冗余、交互缺失.

- **joint方法的原理**

  joint联合抽取方法是通过修改标注方法和模型结构直接输出文本中包含的(ei,rk,ej)三元组. Joint联合抽取方法又分为: "参数共享的联合模型" 和 "联合解码的联合模型":


**joint联合抽取方法的优缺点:**

- 优点:
  - 两个任务的表征有交互作用可能辅助任务的学习.
  - 不用训练多个模型，一个模型解决问题，减少训练与预测的gap.
- 缺点:
  - 更复杂的模型结构.
  - Joint方法提取的特征可能一致，也可能冲突会使模型学习变得混乱.

## 29.CasRel

- CasRel 本质上是基于参数共享的联合实体关系抽取方法.

- 两个步骤：
  - 第一步要识别出句子中的 subject .
  - 第二部要根据识别出的 subject, 识别出所有有可能的 relation 以及对应的 object.

------

- 三个部分：
  - 编码器部分: 可以替换为不同的编码框架，主要对句子中的词进行编码，论文最终以BERT为主，效果不错.
  - 解码器—-头实体识别部分：目的是识别出句子中的 subject.
  - 解码器—-关系与尾实体联合识别部分：根据 subject，寻找可能的 relation 和 object.

**构建的CasRel模型**

- 首先，利用一个线性层➕一个sigmoid激活函数判断每个token是不是**头实体的开始token或结束token**
- 然后，利用最**近匹配原则**将识别到的start和end配对获得候选头实体集合

**Casrel模型可以解决关系抽取中的 SEO 和 EPO 的重叠问题.**



## 30.GraphRAG

![img](C:\Users\<USER>\Documents\img\8cf4d952c677b1c6a1cda59bb2d7bd9f.png)

1. 用户查询：首先，系统接收用户查询，这可能是一个简单的问题或更复杂的查询。

2. 搜索相似 Entity：系统从知识图中识别出与用户输入语义相关的一组 Entity。这些 Entity 作为进入知识图谱的入口点。这一步骤中使用像 Milvus 这样的向量数据库进行[文本相似性搜索](https://xie.infoq.cn/link?target=https%3A%2F%2Fzilliz.com%2Flearn%2Fvector-similarity-search)。

3. Entity-文本单元映射：提取的文本单元被映射到相应的 Entity，移除原始的文本信息。

4. Entity-关系提取：这一步提取关于 Entity 及其相应关系的特定信息。

5. Entity-协变量（Covariate）映射：这一步将 Entity 映射到它们的协变量，这可能包括统计数据或其他相关属性。

6. Entity- Community 报告映射：Community 报告被整合到搜索结果中，纳入一些全局信息。

7. 利用对话历史：如果有对话历史，系统使用对话历史来更好地理解用户的意图和上下文。

8. 生成响应：最后，系统根据前几步生成的经过过滤和排序的数据生成并响应用户查询。

   

## 31.LightRAG

首先将长文本**input documents**按照指定的token大小切分为多个片段text chunks

Content：内容

Tokens：内容对应的tokens数，不能超过最大1024，重叠token为128，为了语义的连贯

Chunk_order_index:内容对应的索引

**1.JsonKVStorage**

json格式存储

key键是唯一标识，对应的是chunk块的索引；

value值是数据内容，对应的是chunk块内容；



**2.NanoVectorDBStorage**

定义Embedding模型，
embedding_dim 嵌入向量的维度
max_token_size 输入文本的长度最大长度
Id：唯一标识，向量的主键，对应embedding之后的chunk块id或者图节点id；
Vertor：向量化表示的数据，对应embedding之后的chunk块内容或者图节点数据；
Meta Fields: 元信息字段，包含元数据字段名称的列表，这些字段是用于存储与向量相关的元数据信息的。

**3.NetworkXStorage**
定义的大模型LLM
调用大模型LLM，通过提示词提取实体和关系，有实体和关系分别合并去重，最后存入图数据库中，同时部分信息存入向量数据库中。

将实体和关系存储到图数据库中
Nodes：

entity_name: 实体名称，首字母大写
entity_type: 以下类型之一：[{entity_types}]
entity_description: 实体属性和活动的综合描述
-chunk_id: 切分的块的唯一标识

Edges：

source_entity: 源实体名称，
target_entity: 目标实体名称，
relationship_description: 解释为什么认为源实体和目标实体是相关的
relationship_strength: 数值分数，表示源实体和目标实体之间关系的强度
relationship_keywords: 一个或多个高级关键词，总结关系的总体性质
-chunk_id: 切分的块的唯一标识
实体和关系存入向量数据库中，实体存储了entity_name，关系存储了src_id和tgt_id

**LightRAG 提供两种检索方式:**

1. `naive_query`: 简单向量检索
2. `kg_query`: 基于知识图谱的混合检索

1.回答流程
调用大模型从问题中提取关键词,关键词分为low_level_keywords和high_level_keywords;
其中**low_level_keywords**通过向量模型embedding之后,从向量数据库中查找相似的top_k**实体**,然后一边去kv数据库查分割后的文本chunk块,一边去图数据库中查找对应的**关系**,最后组成上下文问答.
其中**high_level_keywords**通过向量模型embedding之后,从向量数据库中查找相似的top_k**关系**,然后一边去kv数据库查分割后的文本chunk块,一边去图数据库中查找对应的**实体**,最后组成上下文问答.
2.回答模板
回答的模板是问题+实体关系chunk**图**+**实体+关系**+chunk**文本块**



## 32.指代歧义问题

**解决指代歧义的主要方法**

1. **基于规则的方法（Rule-Based Methods）**

基于规则的方法使用一系列预定义的规则和启发式来解决指代歧义。这些规则通常依赖于语言学的知识，例如语法结构、词性标注、句法树和指代词的位置。常用的规则包括：

- **最近先行词规则（Recency Rule）**：通常，代词指代的是**最近提到**的名词。
- **性别一致性规则（Gender and Number Agreement）**：例如，**“他”指代男性单数名词。**
- **语义相容性规则（Semantic Compatibility Rule）**：例如，**“它”通常不会指代人。**

基于规则的方法简单直接，**但依赖于手工制定的规则，难以处理复杂的语言现象和长距离指代问题**，且对不同语言的适应性较差。

2. **基于统计的方法**（Statistical Methods）

基于统计的方法使用机器学习技术，通过从**大量标注数据中学习指代关系的概率模型**。常用的方法包括：

- **朴素贝叶斯（Naive Bayes）**：利用指代词和先行词之间的特征（如距离、词性、性别、数等），**计算先行词是指代词的指代对象的概率**。
- **最大熵模型（Maximum Entropy Model）**：使用最大熵原理来估计指代关系的**条件概率**。
- **支持向量机（Support Vector Machine, SVM）**：利用高维特征空间中的**超平面**将**不同的先行词分类到代词的可能指代对象中**。

这些方法的优势在于，它们能**自动从数据中学习特征**，适应性强且能处理更复杂的指代情况，但需要**大量标注数据，且性能依赖于特征工程的质量**。

3. **基于深度学习的方法**（Deep Learning Methods）

深度学习方法（特别是神经网络模型）近年来在解决指代歧义问题方面表现出色。常见的深度学习模型有：

- **双向长短时记忆网络（Bi-LSTM）**：用于**捕获上下文信息和指代词的长距离**依赖关系。
- **注意力机制（Attention Mechanism）**：在**上下文中分配不同的注意力权重**，以更准确地识别指代对象。
- **Transformer模型**：如BERT可以从大规模语料库中预训练**上下文嵌入表示**，显著提高对指代关系的识别能力。
- **基于预训练语言模型的指代消解模型**：使用BERT、GPT等语言模型的**预训练嵌入作为特征输入**，能够更好地**捕获语义和上下文关系**。这些模型能够在多个自然语言处理任务上实现最新的成果。

深度学习方法能够利用大规模语料库学习**复杂的语言现象和上下文关系**，不需要大量人工制定规则。它们适用于复杂的指代关系和长距离指代问题，性能优异，但通常需要大量的训练数据和计算资源。

4. **让大模型直接判断，意图识别**



## 33.文本对齐

通常涉及将两种语言或不同来源的文本在句子或段落级别进行匹配。

​          1.**基于长度的对齐方法**

- **Gale-Church算法**：一种经典的基于动态规划的对齐算法，利用了两种语言之间句子长度的对数正态分布假设。该方法计算句子长度（通常是字符数或词数）的对数差异，以确定最有可能的对齐。
- **Moore算法**：一种改进的基于长度的方法，它考虑了段落或句子中存在的空白或标点符号等信息，提高了对齐的准确性。
  2. **基于词汇的对齐方法**
- **词汇对齐模型（Lexical Alignment Models）**：利用词汇翻译概率或词汇共现信息来进行对齐。IBM模型和HMM（隐马尔可夫模型）是常见的词对齐模型。
- **基于词典的方法**：使用现有的**双语词典或翻译词典**，将源语言的词与目标语言的词进行匹配，以便更准确地对齐句子。
  3. **基于统计的对齐方法**
- **贝叶斯方法**：利用贝叶斯统计模型，结合词对齐和句子对齐的联合概率，估计句子对齐的可能性。
- **对数线性模型（Log-linear Models）**：利用源语言和目标语言中单词的共现信息来构建对齐模型，通过最大化对数似然估计（MLE）来优化参数。
  4. **基于机器学习的方法**
- **监督学习方法**：使用已对齐的平行语料库作为训练数据，训练分类器或回归模型来预测新句对的对齐情况。常见的模型包括SVM（支持向量机）、随机森林和神经网络。
- **深度学习方法**：利用神经网络模型（如双向LSTM、Seq2Seq模型或Transformer）来学习句子的特征表示，并通过训练模型来对齐句子。
  5. **基于相似度的对齐方法**
- **余弦相似度**：计算句子向量的余弦相似度来判断其相似性。例如，使用TF-IDF、Word2Vec、BERT等生成句子的向量表示，然后计算相似度。
- **编辑距离（Edit Distance）**：计算源语言句子和目标语言句子之间的最小编辑距离，以此衡量它们的相似度。
  6. **基于注意力机制的方法**
- **注意力模型（Attention Models）**：特别是在基于神经网络的机器翻译中，使用注意力机制来学习对齐。在Transformer等模型中，注意力层能够捕捉源语言和目标语言之间的对齐关系。



## 3、知识蒸馏

知识蒸馏分为**模型蒸馏**和**数据蒸馏**。
**模型蒸馏**是为了解决模型上线时模型体积过大效率慢而产生一种在保证准确率的基础上，提高模型的运行效率。细节就是我们在已有的大模型的基础上，**让小模型在训练时不仅要拟合原始数据的硬标签,用到了交叉熵损失**，还要**拟合教师模型产生的软标签，其中用到了KL散度损失**。

**数据蒸馏**是指用大模型提取的数据，例如问答对，来预训练或微甜小模型，以突破原小模型性能。deepseek已证明。

## 11.fasttext

作为NLP工程领域常用的工具包, fasttext有两大作用:

- **进行文本分类**  默认情况下这是一个有监督的学习过程，需要带有标签的训练数据

- **训练词向量**  通常情况下是采用无监督的方式进行的。这意味着在训练过程中不需要带标签的数据

  - 基于**Skip-gram**模型或**CBOW**（Continuous Bag-of-Words）模型

  优势：

  - 使用fasttext模型训练词向量时使用**层次softmax结构**, 来提升超多类别下的模型性能. 
    - 优点：**传统的softmax**的时间复杂度为**L**(labels的数量), 但是使用**层次化softmax**之后时间复杂度的**log(L)** 
  - 由于fasttext模型过于简单无法捕捉词序特征, 因此会进行**n-gram特征**提取以弥补模型缺陷提升精度.