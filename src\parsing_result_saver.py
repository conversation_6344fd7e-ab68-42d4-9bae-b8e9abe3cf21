#!/usr/bin/env python3
"""
解析结果保存器
保留文件解析过程中的所有中间结果和最终结果
"""

import json
import pickle
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import hashlib


class ParsingResultSaver:
    """解析结果保存器"""
    
    def __init__(self, base_dir: str = "parsing_results"):
        """
        初始化解析结果保存器
        
        Args:
            base_dir: 保存结果的基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.content_dir = self.base_dir / "content"          # 保存解析内容（文本提取、OCR结果等）
        self.chunks_dir = self.base_dir / "final_chunks"      # 保存最终的文档片段
        self.metadata_dir = self.base_dir / "metadata"        # 保存解析元数据

        for dir_path in [self.content_dir, self.chunks_dir, self.metadata_dir]:
            dir_path.mkdir(exist_ok=True)
        
        print(f"✓ 解析结果保存器初始化成功，保存目录: {self.base_dir}")
    
    def generate_file_id(self, file_path: str) -> str:
        """
        为文件生成唯一ID（基于文件名，确保同一文件只有一个版本）

        Args:
            file_path: 文件路径

        Returns:
            str: 文件唯一ID
        """
        file_path = str(file_path)
        file_name = Path(file_path).name
        # 只使用文件名，不使用时间戳，确保同一文件ID一致
        return file_name.replace('.', '_')

    def _cleanup_old_files(self, file_id: str):
        """
        清理同一文件的旧版本

        Args:
            file_id: 文件ID
        """
        # 清理旧的解析结果文件
        for directory in [self.content_dir, self.chunks_dir, self.metadata_dir]:
            for file_path in directory.glob(f"{file_id}*"):
                try:
                    file_path.unlink()
                except Exception:
                    pass  # 忽略删除失败

    def save_parsing_metadata(self, file_id: str, metadata: Dict[str, Any]):
        """
        保存解析元数据

        Args:
            file_id: 文件ID
            metadata: 元数据
        """
        try:
            # 清理旧文件
            self._cleanup_old_files(file_id)

            metadata_file = self.metadata_dir / f"{file_id}_metadata.json"

            # 添加时间戳
            metadata['parsing_timestamp'] = datetime.now().isoformat()
            metadata['file_id'] = file_id

            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            print(f"✓ 保存解析元数据: {metadata_file}")

        except Exception as e:
            print(f"❌ 保存解析元数据失败: {e}")
    
    def save_raw_content(self, file_id: str, content_type: str, content: str,
                        page_num: Optional[int] = None):
        """
        保存解析内容

        Args:
            file_id: 文件ID
            content_type: 内容类型 (text_extraction, ocr_result, etc.)
            content: 内容
            page_num: 页码（可选）
        """
        try:
            if page_num is not None:
                filename = f"{file_id}_{content_type}_page_{page_num}.txt"
            else:
                filename = f"{file_id}_{content_type}.txt"

            content_file = self.content_dir / filename

            with open(content_file, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"✓ 保存解析内容: {content_file}")

        except Exception as e:
            print(f"❌ 保存解析内容失败: {e}")
    

    

    
    def save_final_chunks(self, file_id: str, chunks: List[Dict[str, Any]]):
        """
        保存最终的文档片段
        
        Args:
            file_id: 文件ID
            chunks: 文档片段列表
        """
        try:
            # 保存为JSON格式
            json_filename = f"{file_id}_final_chunks.json"
            json_file = self.chunks_dir / json_filename
            
            save_data = {
                'file_id': file_id,
                'saved_timestamp': datetime.now().isoformat(),
                'total_chunks': len(chunks),
                'chunks': chunks
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            # 保存为pickle格式（用于快速加载）
            pickle_filename = f"{file_id}_final_chunks.pkl"
            pickle_file = self.chunks_dir / pickle_filename
            
            with open(pickle_file, 'wb') as f:
                pickle.dump(chunks, f)
            
            print(f"✓ 保存最终片段: {json_file} 和 {pickle_file}")
            
        except Exception as e:
            print(f"❌ 保存最终片段失败: {e}")
    
    def save_processing_log(self, file_id: str, log_entries: List[str]):
        """
        保存处理日志
        
        Args:
            file_id: 文件ID
            log_entries: 日志条目列表
        """
        try:
            log_filename = f"{file_id}_processing.log"
            log_file = self.base_dir / log_filename
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"解析开始时间: {datetime.now().isoformat()}\n")
                f.write(f"文件ID: {file_id}\n")
                f.write("=" * 50 + "\n\n")
                
                for entry in log_entries:
                    f.write(f"{entry}\n")
            
            print(f"✓ 保存处理日志: {log_file}")
            
        except Exception as e:
            print(f"❌ 保存处理日志失败: {e}")
    
    def load_parsing_results(self, file_id: str) -> Optional[Dict[str, Any]]:
        """
        加载解析结果
        
        Args:
            file_id: 文件ID
            
        Returns:
            Optional[Dict[str, Any]]: 解析结果
        """
        try:
            results = {}
            
            # 加载元数据
            metadata_file = self.metadata_dir / f"{file_id}_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    results['metadata'] = json.load(f)
            
            # 加载最终片段
            chunks_file = self.chunks_dir / f"{file_id}_final_chunks.pkl"
            if chunks_file.exists():
                with open(chunks_file, 'rb') as f:
                    results['chunks'] = pickle.load(f)
            
            return results if results else None
            
        except Exception as e:
            print(f"❌ 加载解析结果失败: {e}")
            return None
    
    def list_parsed_files(self) -> List[Dict[str, Any]]:
        """
        列出所有已解析的文件
        
        Returns:
            List[Dict[str, Any]]: 已解析文件列表
        """
        try:
            parsed_files = []
            
            for metadata_file in self.metadata_dir.glob("*_metadata.json"):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    file_info = {
                        'file_id': metadata.get('file_id'),
                        'original_path': metadata.get('original_path'),
                        'file_type': metadata.get('file_type'),
                        'parsing_timestamp': metadata.get('parsing_timestamp'),
                        'total_pages': metadata.get('total_pages'),
                        'total_tables': metadata.get('total_tables'),
                        'total_chunks': metadata.get('total_chunks')
                    }
                    
                    parsed_files.append(file_info)
                    
                except Exception as e:
                    print(f"⚠ 读取元数据文件失败 {metadata_file}: {e}")
                    continue
            
            # 按时间排序
            parsed_files.sort(key=lambda x: x.get('parsing_timestamp', ''), reverse=True)
            
            return parsed_files
            
        except Exception as e:
            print(f"❌ 列出已解析文件失败: {e}")
            return []
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            Dict[str, Any]: 存储统计信息
        """
        try:
            stats = {
                'total_files': len(list(self.metadata_dir.glob("*_metadata.json"))),
                'content_files': len(list(self.content_dir.glob("*.txt"))),
                'chunk_files': len(list(self.chunks_dir.glob("*.json"))),
                'storage_path': str(self.base_dir),
                'last_updated': datetime.now().isoformat()
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取存储统计失败: {e}")
            return {}


# 全局实例
_result_saver = None

def get_result_saver() -> ParsingResultSaver:
    """获取全局解析结果保存器实例"""
    global _result_saver
    if _result_saver is None:
        _result_saver = ParsingResultSaver()
    return _result_saver
