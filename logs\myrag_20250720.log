2025-07-20 17:31:51,305 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 17:31:51,305 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 17:31:51,305 - MyRAG - INFO - info:72 - 🔄 开始 完整文档解析 | file_path=data/documents/北京市社会保险个人社保参保证明.pdf
2025-07-20 17:31:51,305 - MyRAG - ERROR - error:84 - ❌ 完整文档解析 失败 | elapsed_time=0.000s | error=文件不存在: data\documents\北京市社会保险个人社保参保证明.pdf | file_path=data/documents/北京市社会保险个人社保参保证明.pdf
2025-07-20 17:31:51,305 - MyRAG - ERROR - error:84 - 解析失败: 文件不存在: data\documents\北京市社会保险个人社保参保证明.pdf
2025-07-20 17:32:41,457 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 17:32:41,457 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 17:32:41,457 - MyRAG - INFO - info:72 - 🔄 开始 完整文档解析 | file_path=data/documents/小米集团2024年中期报告.pdf
2025-07-20 17:32:41,458 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\小米集团2024年中期报告.pdf | file_size=1,427,588 bytes | file_type=.pdf
2025-07-20 17:32:41,458 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:32:41,474 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.017s
2025-07-20 17:32:41,474 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=scan
2025-07-20 17:32:57,466 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=15.991s | content_type=scan
2025-07-20 17:32:57,467 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:32:57,467 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 17:32:57,471 - MyRAG - INFO - info:72 - ✅ 完整文档解析 完成 | elapsed_time=16.014s | file_path=data/documents/小米集团2024年中期报告.pdf
2025-07-20 17:32:57,473 - MyRAG - INFO - info:72 - 📊 性能报告已保存: logs\performance_report_20250720_173257.json
2025-07-20 17:40:37,860 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 17:40:37,861 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 17:40:37,869 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 17:40:37,869 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:40:37,919 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.050s
2025-07-20 17:40:37,920 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 17:40:40,088 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.168s | content_type=text
2025-07-20 17:40:40,089 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:40:40,090 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 17:41:00,980 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 17:41:00,980 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:41:00,996 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.016s
2025-07-20 17:41:00,996 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 17:41:02,752 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.756s | content_type=text
2025-07-20 17:41:02,754 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:41:02,755 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 17:41:25,600 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421165.PDF | file_size=190,052 bytes | file_type=.PDF
2025-07-20 17:41:25,600 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:41:25,618 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.018s
2025-07-20 17:41:25,618 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 17:41:27,469 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.851s | content_type=text
2025-07-20 17:41:27,470 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:41:27,471 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 17:41:52,691 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\W020250328670513818195.pdf | file_size=255,314 bytes | file_type=.pdf
2025-07-20 17:41:52,691 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:41:52,706 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.016s
2025-07-20 17:41:52,706 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 17:41:53,394 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=0.688s | content_type=text
2025-07-20 17:41:53,395 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:41:53,395 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 17:52:26,691 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 17:52:26,691 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 17:52:26,691 - MyRAG - INFO - info:72 - 🔄 开始 完整文档解析 | file_path=data/documents/贵州茅台酒股份有限公司2025年第一季度报告.pdf
2025-07-20 17:52:26,692 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\贵州茅台酒股份有限公司2025年第一季度报告.pdf | file_size=154,608 bytes | file_type=.pdf
2025-07-20 17:52:26,692 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 17:52:26,707 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.015s
2025-07-20 17:52:26,707 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 17:52:27,793 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.085s | content_type=text
2025-07-20 17:52:27,794 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 17:52:27,794 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 17:52:27,797 - MyRAG - INFO - info:72 - ✅ 完整文档解析 完成 | elapsed_time=1.106s | file_path=data/documents/贵州茅台酒股份有限公司2025年第一季度报告.pdf
2025-07-20 17:52:27,799 - MyRAG - INFO - info:72 - 📊 性能报告已保存: logs\performance_report_20250720_175227.json
2025-07-20 18:09:35,291 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 18:09:35,291 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 18:10:34,400 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 18:10:34,400 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 18:11:35,017 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 18:11:35,017 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled
2025-07-20 18:11:35,022 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 18:11:35,022 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 18:11:35,054 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.032s
2025-07-20 18:11:35,054 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 18:11:37,156 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.101s | content_type=text
2025-07-20 18:11:37,157 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 18:11:37,158 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 18:12:04,808 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 18:12:04,808 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 18:12:04,823 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.015s
2025-07-20 18:12:04,823 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 18:12:06,680 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.857s | content_type=text
2025-07-20 18:12:06,682 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 18:12:06,683 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 18:12:36,201 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421165.PDF | file_size=190,052 bytes | file_type=.PDF
2025-07-20 18:12:36,201 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 18:12:36,214 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.013s
2025-07-20 18:12:36,214 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 18:12:37,929 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.715s | content_type=text
2025-07-20 18:12:37,930 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 18:12:37,930 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 18:13:08,478 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\W020250328670513818195.pdf | file_size=255,314 bytes | file_type=.pdf
2025-07-20 18:13:08,478 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 18:13:08,492 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.014s
2025-07-20 18:13:08,493 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 18:13:09,151 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=0.658s | content_type=text
2025-07-20 18:13:09,152 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 18:13:09,152 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 18:13:19,164 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\贵州茅台酒股份有限公司2025年第一季度报告.pdf | file_size=154,608 bytes | file_type=.pdf
2025-07-20 18:13:19,164 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 18:13:19,177 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.013s
2025-07-20 18:13:19,177 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 18:13:20,241 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.064s | content_type=text
2025-07-20 18:13:20,242 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 18:13:20,242 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 19:54:24,325 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 19:54:24,326 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1496 字符
2025-07-20 20:03:09,158 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:03:09,159 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:03:09,159 - MyRAG - INFO - info:72 - 🔄 开始 完整文档解析 | file_path=data/documents/1223412432.PDF
2025-07-20 20:03:09,159 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 20:03:09,159 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:03:09,196 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.037s
2025-07-20 20:03:09,196 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:03:11,452 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.256s | content_type=text
2025-07-20 20:03:11,454 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:03:11,455 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:03:11,460 - MyRAG - INFO - info:72 - ✅ 完整文档解析 完成 | elapsed_time=2.301s | file_path=data/documents/1223412432.PDF
2025-07-20 20:03:11,462 - MyRAG - INFO - info:72 - 📊 性能报告已保存: logs\performance_report_20250720_200311.json
2025-07-20 20:04:10,911 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:04:10,911 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:04:10,911 - MyRAG - INFO - info:72 - 🔄 开始 完整文档解析 | file_path=data/documents/1223412432.PDF
2025-07-20 20:04:10,912 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 20:04:10,912 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:04:10,943 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.031s
2025-07-20 20:04:10,943 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:04:13,159 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.216s | content_type=text
2025-07-20 20:04:13,160 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:04:13,160 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 20:04:13,163 - MyRAG - INFO - info:72 - ✅ 完整文档解析 完成 | elapsed_time=2.252s | file_path=data/documents/1223412432.PDF
2025-07-20 20:04:13,164 - MyRAG - INFO - info:72 - 📊 性能报告已保存: logs\performance_report_20250720_200413.json
2025-07-20 20:06:36,798 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:06:36,798 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:06:55,485 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:06:55,486 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1496 字符
2025-07-20 20:10:08,595 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:10:08,595 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:10,757 - MyRAG - INFO - info:72 - 🔧 并发文档处理器 系统信息 | max_workers=4 | execution_mode=多线程 | cpu_count=16
2025-07-20 20:16:10,759 - MyRAG - INFO - info:72 - 找到 5 个文档，开始并发处理
2025-07-20 20:16:10,759 - MyRAG - INFO - info:72 - 开始并发处理 5 个文档
2025-07-20 20:16:10,759 - MyRAG - INFO - info:72 - 🔄 开始 并发文档处理 | file_count=5 | max_workers=4 | mode=多线程
2025-07-20 20:16:10,763 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:10,764 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 20:16:10,765 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:10,765 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:10,765 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:10,766 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421165.PDF | file_size=190,052 bytes | file_type=.PDF
2025-07-20 20:16:10,766 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:10,766 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\W020250328670513818195.pdf | file_size=255,314 bytes | file_type=.pdf
2025-07-20 20:16:10,784 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:10,845 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.080s
2025-07-20 20:16:10,845 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:10,846 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.061s
2025-07-20 20:16:10,846 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:10,846 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.079s
2025-07-20 20:16:10,846 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:10,847 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.081s
2025-07-20 20:16:10,847 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:15,721 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=4.876s | content_type=text
2025-07-20 20:16:15,820 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=4.975s | content_type=text
2025-07-20 20:16:15,933 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:15,986 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.054s
2025-07-20 20:16:16,017 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:16,108 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.092s
2025-07-20 20:16:16,171 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=5.325s | content_type=text
2025-07-20 20:16:16,274 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:16,312 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.038s
2025-07-20 20:16:16,827 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:16,858 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:16,905 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\贵州茅台酒股份有限公司2025年第一季度报告.pdf | file_size=154,608 bytes | file_type=.pdf
2025-07-20 20:16:16,936 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:17,387 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.451s
2025-07-20 20:16:17,511 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:17,785 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=6.938s | content_type=text
2025-07-20 20:16:17,787 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:17,788 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:16:19,193 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.682s | content_type=text
2025-07-20 20:16:19,194 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:19,194 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 20:16:19,200 - MyRAG - INFO - info:72 - ✅ 并发文档处理 完成 | elapsed_time=8.441s | file_count=5 | max_workers=4 | mode=多线程
2025-07-20 20:16:19,200 - MyRAG - INFO - info:72 - 并发处理完成: 成功 5, 失败 0
2025-07-20 20:16:19,200 - MyRAG - INFO - info:72 - 目录处理完成: 成功 5, 失败 0, 总耗时 8.44秒, 平均 1.69秒/文件
2025-07-20 20:16:19,202 - MyRAG - INFO - info:72 - 📊 性能报告已保存: logs\performance_report_20250720_201619.json
2025-07-20 20:16:36,994 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:36,994 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,005 - MyRAG - INFO - info:72 - 🔧 并发文档处理器 系统信息 | max_workers=32 | execution_mode=多线程 | cpu_count=16
2025-07-20 20:16:37,007 - MyRAG - INFO - info:72 - 开始并发处理 5 个文档
2025-07-20 20:16:37,007 - MyRAG - INFO - info:72 - 🔄 开始 并发文档处理 | file_count=5 | max_workers=32 | mode=多线程
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,009 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:16:37,010 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 20:16:37,011 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:37,011 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421165.PDF | file_size=190,052 bytes | file_type=.PDF
2025-07-20 20:16:37,011 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:37,011 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\W020250328670513818195.pdf | file_size=255,314 bytes | file_type=.pdf
2025-07-20 20:16:37,011 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\贵州茅台酒股份有限公司2025年第一季度报告.pdf | file_size=154,608 bytes | file_type=.pdf
2025-07-20 20:16:37,012 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:37,012 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:37,012 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:37,081 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.069s
2025-07-20 20:16:37,082 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:37,082 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.071s
2025-07-20 20:16:37,082 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:37,084 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.073s
2025-07-20 20:16:37,084 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:37,096 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.085s
2025-07-20 20:16:37,097 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.085s
2025-07-20 20:16:37,097 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:37,098 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:40,297 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=3.215s | content_type=text
2025-07-20 20:16:40,502 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:40,653 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.150s
2025-07-20 20:16:41,907 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=4.809s | content_type=text
2025-07-20 20:16:42,202 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:42,343 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=5.260s | content_type=text
2025-07-20 20:16:42,404 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.203s
2025-07-20 20:16:42,561 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:42,608 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.047s
2025-07-20 20:16:42,919 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=5.834s | content_type=text
2025-07-20 20:16:43,034 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:43,112 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.078s
2025-07-20 20:16:44,419 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=7.322s | content_type=text
2025-07-20 20:16:44,420 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:44,421 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:16:44,426 - MyRAG - INFO - info:72 - ✅ 并发文档处理 完成 | elapsed_time=7.419s | file_count=5 | max_workers=32 | mode=多线程
2025-07-20 20:16:44,426 - MyRAG - INFO - info:72 - 并发处理完成: 成功 5, 失败 0
2025-07-20 20:16:44,426 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\W020250328670513818195.pdf | file_size=255,314 bytes | file_type=.pdf
2025-07-20 20:16:44,427 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:44,438 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.011s
2025-07-20 20:16:44,438 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:45,086 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=0.648s | content_type=text
2025-07-20 20:16:45,087 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:45,088 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:16:56,756 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\贵州茅台酒股份有限公司2025年第一季度报告.pdf | file_size=154,608 bytes | file_type=.pdf
2025-07-20 20:16:56,756 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:16:56,773 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.017s
2025-07-20 20:16:56,774 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:16:58,092 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.317s | content_type=text
2025-07-20 20:16:58,093 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:16:58,093 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 20:17:13,108 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 20:17:13,108 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:17:13,122 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.014s
2025-07-20 20:17:13,123 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:17:14,868 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.745s | content_type=text
2025-07-20 20:17:14,869 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:17:14,870 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:17:38,041 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421165.PDF | file_size=190,052 bytes | file_type=.PDF
2025-07-20 20:17:38,042 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:17:38,057 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.016s
2025-07-20 20:17:38,058 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:17:39,921 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.863s | content_type=text
2025-07-20 20:17:39,922 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:17:39,922 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.000s
2025-07-20 20:18:04,587 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 20:18:04,588 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:18:04,635 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.047s
2025-07-20 20:18:04,635 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:18:07,194 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.559s | content_type=text
2025-07-20 20:18:07,195 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:18:07,196 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:19:14,455 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:19:14,456 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1496 字符
2025-07-20 20:19:58,467 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:19:58,468 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1496 字符
2025-07-20 20:21:57,955 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:21:57,956 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1436 字符
2025-07-20 20:22:37,913 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:22:37,914 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1304 字符
2025-07-20 20:29:13,492 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:29:13,493 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1304 字符
2025-07-20 20:34:36,741 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:34:36,742 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1304 字符
2025-07-20 20:37:26,118 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:37:26,119 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 844 字符
2025-07-20 20:44:15,190 - MyRAG - INFO - info:72 - 找到 5 个匹配 '伊利' 的结果
2025-07-20 20:44:15,191 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:44:15,191 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1024 字符
2025-07-20 20:46:26,918 - MyRAG - INFO - info:72 - 找到 5 个匹配 '伊利' 的结果
2025-07-20 20:46:26,918 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:46:26,919 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1024 字符
2025-07-20 20:47:25,669 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:47:25,670 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1590 字符
2025-07-20 20:50:50,830 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:50:50,830 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:51:05,337 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:51:05,337 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:52:04,205 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 20:52:04,205 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 20:52:04,207 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223421150.PDF | file_size=172,860 bytes | file_type=.PDF
2025-07-20 20:52:04,207 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 20:52:04,219 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.013s
2025-07-20 20:52:04,220 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 20:52:05,892 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=1.672s | content_type=text
2025-07-20 20:52:05,893 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 20:52:05,894 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 20:52:45,925 - MyRAG - INFO - info:72 - 找到 5 个匹配 '伊利' 的结果
2025-07-20 20:52:45,925 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:52:45,926 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1024 字符
2025-07-20 20:57:46,679 - MyRAG - INFO - info:72 - 找到 5 个匹配 '伊利' 的结果
2025-07-20 20:57:46,679 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:57:46,680 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1075 字符
2025-07-20 20:58:15,731 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 20:58:15,732 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1518 字符
2025-07-20 21:04:15,685 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:04:15,686 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1518 字符
2025-07-20 21:05:05,038 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:05:05,039 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1518 字符
2025-07-20 21:06:24,461 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 30
2025-07-20 21:06:24,463 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 3989 字符
2025-07-20 21:07:44,691 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 10
2025-07-20 21:07:44,692 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 2984 字符
2025-07-20 21:09:53,747 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['营业收入'], 数值=['202', '5', '2025'], 公司=伊利
2025-07-20 21:09:53,748 - MyRAG - INFO - info:72 - 找到 69 个匹配的财务文档
2025-07-20 21:09:53,750 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['营业收入'], 数值=['32,938,299,808.43', '808.43', '32', '938', '299', '808', '43'], 公司=None
2025-07-20 21:09:53,750 - MyRAG - INFO - info:72 - 找到 47 个匹配的财务文档
2025-07-20 21:09:53,753 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['营业收入'], 数值=[], 公司=美的
2025-07-20 21:09:53,753 - MyRAG - INFO - info:72 - 找到 0 个匹配的财务文档
2025-07-20 21:09:53,754 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['净利润'], 数值=[], 公司=None
2025-07-20 21:09:53,754 - MyRAG - INFO - info:72 - 找到 23 个匹配的财务文档
2025-07-20 21:11:52,259 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['营业收入'], 数值=['202', '5', '2025'], 公司=伊利
2025-07-20 21:11:52,260 - MyRAG - INFO - info:72 - 找到 69 个匹配的财务文档
2025-07-20 21:11:52,260 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:11:52,260 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1634 字符
2025-07-20 21:13:01,597 - MyRAG - INFO - info:72 - 财务数据检索: 指标=['营业收入'], 数值=['202', '5', '2025'], 公司=美的
2025-07-20 21:13:01,597 - MyRAG - INFO - info:72 - 找到 0 个匹配的财务文档
2025-07-20 21:13:01,937 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:13:01,938 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 999 字符
2025-07-20 21:17:28,378 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:17:28,378 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:17:28,690 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:17:28,690 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='伊利', top_k=5
2025-07-20 21:17:28,691 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:17:29,007 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:17:29,008 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='净利润', 公司='伊利', top_k=5
2025-07-20 21:17:29,008 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:17:29,764 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:17:29,764 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='美的集团营业收入', 公司='美的', top_k=5
2025-07-20 21:17:29,764 - MyRAG - WARNING - warning:80 - 未找到公司 '美的' 的文档
2025-07-20 21:17:29,765 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='None', top_k=5
2025-07-20 21:17:29,765 - MyRAG - INFO - info:72 - 未指定公司，使用所有 69 个文档
2025-07-20 21:17:30,113 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:19:00,446 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:19:00,446 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:19:00,842 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:19:00,842 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='伊利', top_k=5
2025-07-20 21:19:00,842 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:19:01,158 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:19:01,159 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='净利润', 公司='伊利', top_k=5
2025-07-20 21:19:01,159 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:19:01,458 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:19:01,458 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='美的集团营业收入', 公司='美的', top_k=5
2025-07-20 21:19:01,458 - MyRAG - WARNING - warning:80 - 未找到公司 '美的' 的文档
2025-07-20 21:19:01,459 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='None', top_k=5
2025-07-20 21:19:01,459 - MyRAG - INFO - info:72 - 未指定公司，使用所有 69 个文档
2025-07-20 21:19:01,814 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 0 个结果
2025-07-20 21:20:08,807 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:20:08,807 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:20:09,150 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:20:09,152 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='伊利', top_k=5
2025-07-20 21:20:09,153 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:20:09,448 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:20:09,450 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='净利润', 公司='伊利', top_k=5
2025-07-20 21:20:09,450 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:20:09,770 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:20:09,773 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='美的集团营业收入', 公司='美的', top_k=5
2025-07-20 21:20:09,773 - MyRAG - WARNING - warning:80 - 未找到公司 '美的' 的文档
2025-07-20 21:20:09,773 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='None', top_k=5
2025-07-20 21:20:09,773 - MyRAG - INFO - info:72 - 未指定公司，使用所有 69 个文档
2025-07-20 21:20:10,087 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:21:01,614 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='内蒙古伊利实业集团股份有限公司 营业收入 2025年 伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:21:01,614 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:21:02,126 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:21:02,126 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:21:02,127 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1716 字符
2025-07-20 21:22:25,031 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:22:25,031 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:22:25,388 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:22:25,391 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='伊利', top_k=5
2025-07-20 21:22:25,391 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:22:25,719 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:22:25,721 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='净利润', 公司='伊利', top_k=5
2025-07-20 21:22:25,721 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:22:26,026 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:22:26,028 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='美的集团营业收入', 公司='美的', top_k=5
2025-07-20 21:22:26,028 - MyRAG - WARNING - warning:80 - 未找到公司 '美的' 的文档
2025-07-20 21:22:26,029 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='营业收入', 公司='None', top_k=5
2025-07-20 21:22:26,029 - MyRAG - INFO - info:72 - 未指定公司，使用所有 69 个文档
2025-07-20 21:22:26,438 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:22:46,806 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='内蒙古伊利实业集团股份有限公司 营业收入 2025年 伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:22:46,806 - MyRAG - INFO - info:72 - 第一阶段筛选: 找到 69 个 '伊利' 的文档
2025-07-20 21:22:47,286 - MyRAG - INFO - info:72 - 两阶段检索完成: 返回 5 个结果
2025-07-20 21:22:47,286 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:22:47,287 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1774 字符
2025-07-20 21:23:15,696 - MyRAG - INFO - info:72 - 开始两阶段检索: 查询='美的集团股份有限公司 营业收入 2025年 美的集团2025年第一季度营业收入是多少？', 公司='美的', top_k=5
2025-07-20 21:23:15,696 - MyRAG - WARNING - warning:80 - 未找到公司 '美的' 的文档
2025-07-20 21:29:27,173 - MyRAG - INFO - info:72 - 🔧 OCR处理器 系统信息 | mode=本地MinerU | api_key_available=True
2025-07-20 21:29:27,173 - MyRAG - INFO - info:72 - 🔧 简化文档处理器 系统信息 | ocr_processor=UnifiedOCRProcessor | content_detector=ContentTypeDetector | result_saver=enabled | doc_info_extractor=DocumentInfoExtractor
2025-07-20 21:29:27,177 - MyRAG - INFO - info:72 - 📄 开始处理文件 | file_path=data\documents\1223412432.PDF | file_size=640,817 bytes | file_type=.PDF
2025-07-20 21:29:27,177 - MyRAG - INFO - info:72 - 🔄 开始 内容类型检测
2025-07-20 21:29:27,208 - MyRAG - INFO - info:72 - ✅ 内容类型检测 完成 | elapsed_time=0.032s
2025-07-20 21:29:27,208 - MyRAG - INFO - info:72 - 🔄 开始 内容处理 | content_type=text
2025-07-20 21:29:29,256 - MyRAG - INFO - info:72 - ✅ 内容处理 完成 | elapsed_time=2.047s | content_type=text
2025-07-20 21:29:29,257 - MyRAG - INFO - info:72 - 🔄 开始 文档分块
2025-07-20 21:29:29,258 - MyRAG - INFO - info:72 - ✅ 文档分块 完成 | elapsed_time=0.001s
2025-07-20 21:30:07,462 - MyRAG - INFO - info:72 - 开始优化检索: 查询='伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:30:07,463 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 69 个 '伊利' 的文档
2025-07-20 21:30:07,850 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 15 个候选结果
2025-07-20 21:30:07,851 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 15 个结果
2025-07-20 21:30:07,853 - MyRAG - INFO - info:72 - 开始优化检索: 查询='美的集团2025年第一季度营业收入', 公司='美的', top_k=5
2025-07-20 21:30:07,853 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:08,163 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 15 个候选结果
2025-07-20 21:30:08,163 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 15 个结果
2025-07-20 21:30:08,165 - MyRAG - INFO - info:72 - 开始优化检索: 查询='营业收入 127,838,538', 公司='美的', top_k=5
2025-07-20 21:30:08,165 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:08,531 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 15 个候选结果
2025-07-20 21:30:08,531 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 15 个结果
2025-07-20 21:30:08,534 - MyRAG - INFO - info:72 - 开始优化检索: 查询='净利润', 公司='伊利', top_k=5
2025-07-20 21:30:08,534 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 69 个 '伊利' 的文档
2025-07-20 21:30:08,889 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 15 个候选结果
2025-07-20 21:30:08,889 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 15 个结果
2025-07-20 21:30:08,892 - MyRAG - INFO - info:72 - 开始优化检索: 查询='营业收入', 公司='None', top_k=5
2025-07-20 21:30:08,892 - MyRAG - INFO - info:72 - 未指定公司，使用所有 128 个文档
2025-07-20 21:30:09,375 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 15 个候选结果
2025-07-20 21:30:09,376 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 15 个结果
2025-07-20 21:30:09,382 - MyRAG - INFO - info:72 - 开始优化检索: 查询='美的集团营业收入', 公司='美的', top_k=3
2025-07-20 21:30:09,382 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:09,785 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 9 个候选结果
2025-07-20 21:30:09,786 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 9 个结果
2025-07-20 21:30:09,786 - MyRAG - INFO - info:72 - 开始优化检索: 查询='美的集团2025年第一季度营业收入', 公司='美的', top_k=3
2025-07-20 21:30:09,786 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:10,155 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 9 个候选结果
2025-07-20 21:30:10,155 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 9 个结果
2025-07-20 21:30:10,155 - MyRAG - INFO - info:72 - 开始优化检索: 查询='营业收入 127,838,538', 公司='美的', top_k=3
2025-07-20 21:30:10,156 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:10,499 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 9 个候选结果
2025-07-20 21:30:10,499 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 9 个结果
2025-07-20 21:30:10,500 - MyRAG - INFO - info:72 - 开始优化检索: 查询='127,838,538 千元', 公司='美的', top_k=3
2025-07-20 21:30:10,500 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:30:10,870 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 9 个候选结果
2025-07-20 21:30:10,870 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 9 个结果
2025-07-20 21:31:15,292 - MyRAG - INFO - info:72 - 开始优化检索: 查询='美的集团股份有限公司 营业收入 2025年 美的集团2025年第一季度营业收入是多少？', 公司='美的', top_k=5
2025-07-20 21:31:15,293 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 59 个 '美的' 的文档
2025-07-20 21:31:15,715 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 50 个候选结果
2025-07-20 21:31:15,715 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 50 个结果
2025-07-20 21:31:15,715 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:31:15,716 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 2251 字符
2025-07-20 21:31:46,521 - MyRAG - INFO - info:72 - 开始优化检索: 查询='内蒙古伊利实业集团股份有限公司 营业收入 2025年 伊利集团2025年第一季度营业收入是多少？', 公司='伊利', top_k=5
2025-07-20 21:31:46,521 - MyRAG - INFO - info:72 - 第一阶段筛选: 根据document_info找到 69 个 '伊利' 的文档
2025-07-20 21:31:46,873 - MyRAG - INFO - info:72 - 第二阶段向量检索: 在筛选文档中找到 50 个候选结果
2025-07-20 21:31:46,874 - MyRAG - INFO - info:72 - 第三阶段关键词优化: 最终返回 50 个结果
2025-07-20 21:31:46,874 - MyRAG - INFO - info:72 - 开始格式化上下文，文档数量: 5
2025-07-20 21:31:46,875 - MyRAG - INFO - info:72 - 上下文格式化完成，最终长度: 1985 字符
2025-07-20 21:32:29,794 - MyRAG - INFO - info:72 - 开始优化检索: 查询='中国长江电力股份有限公司 营业收入 2025年 中国长江电力股份有限公司2025年第一季度营业收入是多少？', 公司='中国长江电力股份有限公司', top_k=5
2025-07-20 21:32:29,794 - MyRAG - WARNING - warning:80 - 未找到公司 '中国长江电力股份有限公司' 的文档
