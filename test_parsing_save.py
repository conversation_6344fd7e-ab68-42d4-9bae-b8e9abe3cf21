#!/usr/bin/env python3
"""
测试解析结果保存功能
"""

from src.simplified_document_processor import SimplifiedDocumentProcessor
from pathlib import Path

def test_parsing_save():
    """测试解析结果保存"""
    # 测试处理一个文档
    processor = SimplifiedDocumentProcessor()
    file_path = Path('data/documents/内蒙古伊利实业集团股份有限公司 2025年第一季度报告.PDF')

    print(f'处理文件: {file_path}')
    chunks = processor.process_file(file_path, save_results=True)
    print(f'生成 {len(chunks)} 个片段')

    # 检查保存结果
    content_dir = Path('parsing_results/content')
    chunks_dir = Path('parsing_results/final_chunks')

    print(f'\nContent目录文件数: {len(list(content_dir.glob("*")))}')
    print(f'Chunks目录文件数: {len(list(chunks_dir.glob("*")))}')

    for f in content_dir.glob('*'):
        print(f'  Content: {f.name}')
        
    for f in chunks_dir.glob('*'):
        print(f'  Chunks: {f.name}')

if __name__ == "__main__":
    test_parsing_save()
