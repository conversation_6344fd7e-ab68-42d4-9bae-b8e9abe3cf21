#!/usr/bin/env python3
"""
测试财务数据检索器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.hybrid_vector_store import HybridVectorStore
from src.financial_data_retriever import FinancialDataRetriever


def test_financial_retriever():
    """测试财务数据检索器"""
    print("🔧 初始化财务数据检索器...")
    
    # 初始化
    store = HybridVectorStore()
    retriever = FinancialDataRetriever()
    
    print(f"✅ 知识库加载完成，共 {len(store.metadata)} 个文档")
    
    # 测试查询
    test_queries = [
        ("伊利集团2025年第一季度营业收入是多少？", "伊利"),
        ("营业收入 32,938,299,808.43", None),
        ("美的集团营业收入", "美的"),
        ("净利润", None),
    ]
    
    for query, company in test_queries:
        print(f"\n🔍 测试查询: {query}")
        if company:
            print(f"🏢 目标公司: {company}")
        
        # 使用财务数据检索器
        results = retriever.get_best_financial_matches(
            store.metadata, query, company, top_k=5
        )
        
        print(f"📊 找到 {len(results)} 个匹配结果")
        
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            source = result.get('source', '')
            score = result.get('financial_match_score', 0)
            doc_info = result.get('document_info', '')
            
            print(f"\n📄 结果 {i}:")
            print(f"   📁 来源: {source}")
            print(f"   📊 匹配分数: {score:.2f}")
            print(f"   🏷️  文档信息: {doc_info}")
            print(f"   📝 内容: {content[:200]}...")
        
        print("-" * 80)


if __name__ == "__main__":
    test_financial_retriever()
