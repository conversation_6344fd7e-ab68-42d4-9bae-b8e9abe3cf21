#!/usr/bin/env python3
"""
内容类型检测器
判断文件是纯文本、扫描版还是混合版
"""

import os
import fitz  # PyMuPDF
from pathlib import Path
from typing import Tuple, Dict, Any
from docx import Document

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class ContentTypeDetector:
    """内容类型检测器"""
    
    def __init__(self):
        """初始化内容类型检测器"""
        print("✓ 内容类型检测器初始化成功")
    
    def detect_content_type(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """
        检测文件的内容类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[str, Dict[str, Any]]: (内容类型, 详细信息)
            内容类型: 'text', 'scan', 'mixed'
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()
        
        print(f"🔍 检测文件内容类型: {file_path.name}")
        
        if file_ext == '.pdf':
            return self._detect_pdf_type(file_path)
        elif file_ext in ['.docx', '.doc']:
            return self._detect_word_type(file_path)
        elif file_ext in ['.txt', '.md']:
            return self._detect_text_type(file_path)
        elif file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']:
            return self._detect_image_type(file_path)
        else:
            return 'text', {'reason': '未知文件类型，默认为文本'}
    
    def _detect_pdf_type(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """检测PDF文件类型"""
        try:
            # 使用PyMuPDF检测
            doc = fitz.open(str(file_path))
            total_pages = len(doc)
            text_pages = 0
            image_pages = 0
            mixed_pages = 0
            
            total_text_length = 0
            total_images = 0
            
            for page_num in range(min(5, total_pages)):  # 检测前5页
                page = doc[page_num]
                
                # 检测文本
                text = page.get_text().strip()
                text_length = len(text)
                total_text_length += text_length
                
                # 检测图片
                image_list = page.get_images()
                image_count = len(image_list)
                total_images += image_count
                
                # 分类页面
                if text_length > 100 and image_count > 0:
                    mixed_pages += 1
                elif text_length > 100:
                    text_pages += 1
                elif image_count > 0:
                    image_pages += 1
            
            doc.close()
            
            # 判断逻辑
            avg_text_per_page = total_text_length / min(5, total_pages)
            avg_images_per_page = total_images / min(5, total_pages)
            
            info = {
                'total_pages': total_pages,
                'sampled_pages': min(5, total_pages),
                'text_pages': text_pages,
                'image_pages': image_pages,
                'mixed_pages': mixed_pages,
                'avg_text_per_page': avg_text_per_page,
                'avg_images_per_page': avg_images_per_page,
                'total_text_length': total_text_length,
                'total_images': total_images
            }
            
            if avg_text_per_page > 500 and avg_images_per_page < 1:
                return 'text', {**info, 'reason': '主要包含文本内容'}
            elif avg_text_per_page < 100 and avg_images_per_page > 0:
                return 'scan', {**info, 'reason': '主要是扫描图片'}
            elif mixed_pages > text_pages and mixed_pages > image_pages:
                return 'mixed', {**info, 'reason': '包含文本和图片的混合内容'}
            elif avg_text_per_page > 200:
                return 'text', {**info, 'reason': '文本内容较多'}
            else:
                return 'scan', {**info, 'reason': '文本内容较少，可能需要OCR'}
                
        except Exception as e:
            return 'text', {'reason': f'检测失败，默认为文本: {e}'}
    
    def _detect_word_type(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """检测Word文档类型"""
        try:
            doc = Document(file_path)
            
            total_text_length = 0
            total_images = 0
            total_paragraphs = len(doc.paragraphs)
            
            # 统计文本
            for paragraph in doc.paragraphs:
                total_text_length += len(paragraph.text)
            
            # 统计图片和表格
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    total_images += 1
            
            total_tables = len(doc.tables)
            
            info = {
                'total_paragraphs': total_paragraphs,
                'total_text_length': total_text_length,
                'total_images': total_images,
                'total_tables': total_tables,
                'avg_text_per_paragraph': total_text_length / max(1, total_paragraphs)
            }
            
            if total_text_length > 1000 and total_images < 5:
                return 'text', {**info, 'reason': '主要包含文本内容'}
            elif total_text_length < 500 and total_images > 3:
                return 'scan', {**info, 'reason': '主要包含图片，可能需要OCR'}
            else:
                return 'mixed', {**info, 'reason': '包含文本和图片的混合内容'}
                
        except Exception as e:
            return 'text', {'reason': f'检测失败，默认为文本: {e}'}
    
    def _detect_text_type(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """检测纯文本文件类型"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            info = {
                'file_size': len(content),
                'line_count': len(content.split('\n'))
            }
            
            return 'text', {**info, 'reason': '纯文本文件'}
            
        except Exception as e:
            return 'text', {'reason': f'检测失败，默认为文本: {e}'}
    
    def _detect_image_type(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """检测图片文件类型"""
        try:
            info = {
                'file_size': file_path.stat().st_size
            }

            return 'scan', {**info, 'reason': '图片文件，需要OCR识别'}

        except Exception as e:
            return 'scan', {'reason': f'图片检测失败: {e}'}
    
    def print_detection_result(self, content_type: str, info: Dict[str, Any], file_path: str):
        """打印检测结果"""
        print(f"\n📋 文件内容类型检测结果:")
        print(f"   📁 文件: {Path(file_path).name}")
        
        if content_type == 'text':
            print(f"   📝 类型: 纯文本内容")
            print(f"   🔧 处理: 直接文本提取")
        elif content_type == 'scan':
            print(f"   🖼️  类型: 扫描版内容")
            print(f"   🔧 处理: PaddleOCR识别")
        elif content_type == 'mixed':
            print(f"   🔀 类型: 混合版内容")
            print(f"   🔧 处理: 文本提取 + OCR识别")
        
        print(f"   💡 原因: {info.get('reason', '未知')}")
        
        # 打印详细信息
        if 'total_pages' in info:
            print(f"   📄 页数: {info['total_pages']}")
            print(f"   📝 平均文本/页: {info['avg_text_per_page']:.0f} 字符")
            print(f"   🖼️  平均图片/页: {info['avg_images_per_page']:.1f} 个")
        
        if 'total_text_length' in info:
            print(f"   📏 总文本长度: {info['total_text_length']} 字符")
        
        if 'total_images' in info:
            print(f"   🖼️  总图片数: {info['total_images']} 个")
        
        print("-" * 50)
