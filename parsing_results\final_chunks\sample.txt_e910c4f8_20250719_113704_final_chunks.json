{"file_id": "sample.txt_e910c4f8_20250719_113704", "saved_timestamp": "2025-07-19T11:37:04.135848", "total_chunks": 3, "chunks": [{"content": "1. 轻量级设计\n   - 使用FAISS进行向量存储\n   - 支持API调用，减少本地资源消耗\n   - 适合8G GPU环境", "source": "E:\\A_Test_Project\\A_test\\MyRAG\\data\\documents\\sample.txt", "type": "text", "chunk_id": 3, "traditional_converted": false, "original_content": null, "processing_method": "simplified_markdown"}, {"content": "2. 多格式支持\n   - PDF文档\n   - Word文档\n   - Markdown文件\n   - 纯文本文件", "source": "E:\\A_Test_Project\\A_test\\MyRAG\\data\\documents\\sample.txt", "type": "text", "chunk_id": 4, "traditional_converted": false, "original_content": null, "processing_method": "simplified_markdown"}, {"content": "使用方法：\n1. 将文档放入data/documents目录\n2. 设置API密钥\n3. 运行python examples/demo.py", "source": "E:\\A_Test_Project\\A_test\\MyRAG\\data\\documents\\sample.txt", "type": "text", "chunk_id": 6, "traditional_converted": false, "original_content": null, "processing_method": "simplified_markdown"}]}