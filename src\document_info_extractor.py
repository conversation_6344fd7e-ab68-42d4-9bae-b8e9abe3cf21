#!/usr/bin/env python3
"""
文档信息提取器
从文档内容中提取主要信息，用于生成document_info字段
"""

import re
from typing import Optional, Dict, Any
from pathlib import Path


class DocumentInfoExtractor:
    """文档信息提取器"""
    
    def __init__(self):
        # 定义公司名称模式
        self.company_patterns = [
            r'([^，。\n]*?(?:股份有限公司|有限公司|集团股份有限公司|实业集团股份有限公司))',
            r'([^，。\n]*?(?:集团|公司)(?:股份有限公司)?)',
        ]
        
        # 定义报告类型模式
        self.report_patterns = [
            r'(\d{4}年.*?(?:第[一二三四]季度|年度|半年度|中期).*?报告)',
            r'(\d{4}年.*?(?:季度|年报|半年报|中报))',
            r'(第[一二三四]季度报告)',
            r'(年度报告)',
            r'(半年度报告|中期报告)',
        ]
        
        # 定义其他重要信息模式
        self.other_patterns = [
            r'(招股说明书)',
            r'(公开发行.*?说明书)',
            r'(债券.*?说明书)',
            r'(重大资产重组.*?报告)',
            r'(收购报告书)',
        ]
    
    def extract_document_info(self, content: str, file_path: str = "") -> str:
        """
        提取文档主要信息

        Args:
            content: 文档内容
            file_path: 文件路径

        Returns:
            str: 文档主要信息
        """
        if not content:
            return self._extract_from_filename(file_path)

        # 优先从前几行提取标题信息
        lines = content.split('\n')
        title_lines = []

        # 查找前10行中的有效标题信息
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            if line and len(line) > 5:  # 过滤太短的行
                # 检查是否包含公司名称和报告信息
                if any(keyword in line for keyword in ['股份有限公司', '集团', '公司']) and \
                   any(keyword in line for keyword in ['报告', '年度', '季度']):
                    title_lines.append(line)
                    break  # 找到第一个匹配的标题行就停止

        # 如果找到标题行，直接使用
        if title_lines:
            result = title_lines[0]
            # 清理和简化
            result = self._clean_document_info(result)
            return result

        # 如果没有找到明确的标题行，使用原来的方法
        # 取前500字符进行分析
        header_content = content[:500]

        # 提取公司名称
        company_name = self._extract_company_name(header_content)

        # 提取报告类型
        report_type = self._extract_report_type(header_content)

        # 提取其他重要信息
        other_info = self._extract_other_info(header_content)

        # 组合信息
        info_parts = []

        if company_name:
            info_parts.append(company_name)

        if report_type:
            info_parts.append(report_type)
        elif other_info:
            info_parts.append(other_info)

        # 如果没有提取到有效信息，尝试从文件名提取
        if not info_parts:
            filename_info = self._extract_from_filename(file_path)
            if filename_info:
                return filename_info

        # 组合最终结果
        if info_parts:
            result = " ".join(info_parts)
            # 清理结果
            result = self._clean_document_info(result)
            return result

        return "未知文档"
    
    def _extract_company_name(self, content: str) -> Optional[str]:
        """提取公司名称"""
        # 清理内容，移除多余的换行和空格
        cleaned_content = re.sub(r'\s+', ' ', content)
        
        for pattern in self.company_patterns:
            matches = re.findall(pattern, cleaned_content)
            if matches:
                # 选择最长的匹配（通常更完整）
                company_name = max(matches, key=len).strip()
                
                # 验证公司名称的合理性
                if self._is_valid_company_name(company_name):
                    return company_name
        
        return None
    
    def _extract_report_type(self, content: str) -> Optional[str]:
        """提取报告类型"""
        cleaned_content = re.sub(r'\s+', ' ', content)
        
        for pattern in self.report_patterns:
            matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
            if matches:
                # 选择最具体的匹配
                report_type = max(matches, key=len).strip()
                return report_type
        
        return None
    
    def _extract_other_info(self, content: str) -> Optional[str]:
        """提取其他重要信息"""
        cleaned_content = re.sub(r'\s+', ' ', content)
        
        for pattern in self.other_patterns:
            matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
            if matches:
                return matches[0].strip()
        
        return None
    
    def _extract_from_filename(self, file_path: str) -> str:
        """从文件名提取信息"""
        if not file_path:
            return "未知文档"
        
        filename = Path(file_path).stem
        
        # 如果文件名是数字，返回通用描述
        if re.match(r'^\d+$', filename):
            return "财务报告文档"
        
        # 清理文件名
        cleaned_name = filename.replace('_', ' ').replace('-', ' ')
        
        # 尝试从文件名中提取公司和报告信息
        company_match = None
        for pattern in self.company_patterns:
            matches = re.findall(pattern, cleaned_name)
            if matches:
                company_match = matches[0]
                break
        
        report_match = None
        for pattern in self.report_patterns:
            matches = re.findall(pattern, cleaned_name, re.IGNORECASE)
            if matches:
                report_match = matches[0]
                break
        
        if company_match and report_match:
            return f"{company_match} {report_match}"
        elif company_match:
            return f"{company_match} 报告"
        elif report_match:
            return report_match
        else:
            return cleaned_name if len(cleaned_name) < 50 else "财务报告文档"
    
    def _is_valid_company_name(self, name: str) -> bool:
        """验证公司名称的合理性"""
        if not name or len(name) < 3:
            return False
        
        # 排除一些明显不是公司名称的内容
        invalid_patterns = [
            r'^第\d+',  # 第几页、第几章等
            r'^\d+年',  # 年份开头
            r'^报告',   # 报告开头
            r'^附录',   # 附录开头
            r'^目录',   # 目录开头
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, name):
                return False
        
        # 必须包含公司相关关键词
        company_keywords = ['公司', '集团', '股份', '有限', '实业']
        if not any(keyword in name for keyword in company_keywords):
            return False
        
        return True
    
    def _clean_document_info(self, info: str) -> str:
        """清理文档信息"""
        # 移除多余的空格和换行
        info = re.sub(r'\s+', ' ', info).strip()
        
        # 移除重复的公司名称
        words = info.split()
        seen = set()
        cleaned_words = []
        
        for word in words:
            if word not in seen or len(word) <= 2:  # 保留短词（如年份）
                cleaned_words.append(word)
                seen.add(word)
        
        result = ' '.join(cleaned_words)
        
        # 限制长度
        if len(result) > 100:
            result = result[:100] + "..."
        
        return result
    
    def extract_multiple_info(self, content: str, file_path: str = "") -> Dict[str, Any]:
        """
        提取多种文档信息
        
        Args:
            content: 文档内容
            file_path: 文件路径
            
        Returns:
            Dict[str, Any]: 包含多种信息的字典
        """
        header_content = content[:500] if content else ""
        
        return {
            'document_info': self.extract_document_info(content, file_path),
            'company_name': self._extract_company_name(header_content),
            'report_type': self._extract_report_type(header_content),
            'other_info': self._extract_other_info(header_content),
            'filename': Path(file_path).stem if file_path else ""
        }
