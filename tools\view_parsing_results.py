#!/usr/bin/env python3
"""
查看解析结果工具
查看已保存的文件解析结果
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.parsing_result_saver import get_result_saver


def main():
    """主函数"""
    print("📊 查看解析结果")
    print("=" * 50)
    
    result_saver = get_result_saver()
    
    # 显示存储统计
    stats = result_saver.get_storage_stats()
    print(f"存储统计:")
    print(f"  总解析文件数: {stats.get('total_files', 0)}")
    print(f"  原始内容文件: {stats.get('raw_content_files', 0)}")
    print(f"  表格数据文件: {stats.get('table_files', 0)}")
    print(f"  语义增强文件: {stats.get('enhanced_files', 0)}")
    print(f"  最终片段文件: {stats.get('chunk_files', 0)}")
    print(f"  存储路径: {stats.get('storage_path')}")
    
    # 列出已解析文件
    parsed_files = result_saver.list_parsed_files()
    
    if not parsed_files:
        print("\n暂无已解析的文件")
        return
    
    print(f"\n📋 已解析文件列表 ({len(parsed_files)} 个):")
    for i, file_info in enumerate(parsed_files, 1):
        print(f"\n{i}. {file_info.get('file_name', 'Unknown')}")
        print(f"   文件ID: {file_info.get('file_id')}")
        print(f"   解析时间: {file_info.get('parsing_timestamp')}")
        print(f"   片段数: {file_info.get('total_chunks', 0)}")
        
        # 如果有文件ID，显示详细信息
        file_id = file_info.get('file_id')
        if file_id:
            results = result_saver.load_parsing_results(file_id)
            if results:
                chunks = results.get('chunks', [])
                if chunks:
                    # 统计片段类型
                    chunk_types = {}
                    enhanced_count = 0
                    
                    for chunk in chunks:
                        chunk_type = chunk.get('type', 'unknown')
                        chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
                        
                        if chunk.get('semantic_enhanced'):
                            enhanced_count += 1
                    
                    print(f"   片段类型:")
                    for chunk_type, count in sorted(chunk_types.items()):
                        print(f"     {chunk_type}: {count} 个")
                    
                    print(f"   语义增强片段: {enhanced_count} 个")
    
    # 检查parsing_results目录结构
    parsing_dir = Path("parsing_results")
    if parsing_dir.exists():
        print(f"\n📁 解析结果目录结构:")
        for subdir in parsing_dir.iterdir():
            if subdir.is_dir():
                file_count = len(list(subdir.glob("*")))
                print(f"   {subdir.name}/: {file_count} 个文件")


if __name__ == "__main__":
    main()
