#!/usr/bin/env python3
"""
测试MinerU输出处理脚本
直接使用已经生成的MinerU输出文件
"""

import sys
import os
import json
import re
from pathlib import Path
from datetime import datetime

def create_document_chunks(markdown_content, file_name):
    """创建文档片段"""
    chunks = []

    # 按标题分割内容
    sections = re.split(r'\n(?=# )', markdown_content)

    for i, section in enumerate(sections):
        if section.strip():
            chunks.append({
                'type': 'document',
                'content': section.strip(),
                'metadata': {
                    'chunk_id': i,
                    'source_file': file_name,
                    'char_count': len(section.strip()),
                    'word_count': len(section.strip().split())
                }
            })

    return chunks

def save_results(file_path, markdown_content, chunks, metadata):
    """保存处理结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_id = f"{Path(file_path).stem}_{timestamp}"

    # 创建保存目录
    results_dir = Path("parsing_results")
    results_dir.mkdir(exist_ok=True)

    # 保存Markdown内容
    md_file = results_dir / f"{file_id}_content.md"
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)

    # 保存片段
    chunks_file = results_dir / f"{file_id}_chunks.json"
    with open(chunks_file, 'w', encoding='utf-8') as f:
        json.dump(chunks, f, ensure_ascii=False, indent=2)

    # 保存元数据
    meta_file = results_dir / f"{file_id}_metadata.json"
    with open(meta_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)

    return file_id

def test_mineru_output():
    """测试MinerU输出处理"""

    # MinerU输出文件路径
    mineru_md_file = "temp_mineru_output/小米集团2024年中期报告/auto/小米集团2024年中期报告.md"
    mineru_json_file = "temp_mineru_output/小米集团2024年中期报告/auto/小米集团2024年中期报告_content_list.json"

    if not os.path.exists(mineru_md_file):
        print(f"❌ MinerU Markdown文件不存在: {mineru_md_file}")
        return

    if not os.path.exists(mineru_json_file):
        print(f"❌ MinerU JSON文件不存在: {mineru_json_file}")
        return

    print("🔍 测试MinerU输出处理")
    print("=" * 50)
    
    # 读取MinerU输出
    print(f"📄 读取MinerU Markdown文件: {mineru_md_file}")
    with open(mineru_md_file, 'r', encoding='utf-8') as f:
        markdown_content = f.read()

    print(f"📄 读取MinerU JSON文件: {mineru_json_file}")
    with open(mineru_json_file, 'r', encoding='utf-8') as f:
        content_list = json.load(f)

    print(f"📊 MinerU输出统计:")
    print(f"   📝 Markdown长度: {len(markdown_content):,} 字符")
    print(f"   📋 内容项目数: {len(content_list):,} 个")

    # 分析内容类型
    text_items = [item for item in content_list if item.get('type') == 'text']
    image_items = [item for item in content_list if item.get('type') == 'image']
    table_items = [item for item in content_list if item.get('type') == 'table']

    print(f"   📝 文本项目: {len(text_items)} 个")
    print(f"   🖼️  图片项目: {len(image_items)} 个")
    print(f"   📊 表格项目: {len(table_items)} 个")

    print("📊 步骤1: 创建文档片段...")
    chunks = create_document_chunks(markdown_content, "小米集团2024年中期报告.pdf")

    print(f"✅ 处理完成!")
    print(f"   生成片段: {len(chunks)} 个")

    # 显示前几个片段
    for i, chunk in enumerate(chunks[:5]):
        print(f"\n片段 {i+1}:")
        print(f"  类型: {chunk.get('type', 'unknown')}")
        content_preview = chunk.get('content', '')[:200]
        if len(chunk.get('content', '')) > 200:
            content_preview += "..."
        print(f"  内容: {content_preview}")

    if len(chunks) > 5:
        print(f"\n... 还有 {len(chunks) - 5} 个片段")

    # 保存结果
    print("\n💾 保存处理结果...")
    file_id = save_results(
        "小米集团2024年中期报告.pdf",
        markdown_content,
        chunks,
        {
            'file_type': 'pdf',
            'content_type': 'scanned',
            'processing_method': 'mineru_direct',
            'total_chunks': len(chunks),
            'total_chars': len(markdown_content),
            'mineru_items': len(content_list),
            'text_items': len(text_items),
            'image_items': len(image_items),
            'table_items': len(table_items)
        }
    )

    print(f"✅ 结果已保存，文件ID: {file_id}")

if __name__ == "__main__":
    test_mineru_output()
