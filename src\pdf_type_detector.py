#!/usr/bin/env python3
"""
PDF类型检测器
判断PDF是纯文本、扫描版还是混合版
"""

import fitz  # PyMuPDF
from pathlib import Path
from typing import Tuple, Dict, Any
import re

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class PDFTypeDetector:
    """PDF类型检测器"""
    
    def __init__(self):
        """初始化PDF类型检测器"""
        self.text_threshold = 50  # 每页最少文本字符数阈值
        self.text_ratio_threshold = 0.3  # 文本页面比例阈值
        
    def detect_pdf_type(self, pdf_path: str) -> Dict[str, Any]:
        """
        检测PDF类型
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            Dict: 包含PDF类型和详细信息的字典
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        print(f"🔍 开始检测PDF类型: {pdf_path.name}")
        
        # 使用PyMuPDF进行检测
        detection_result = self._detect_with_pymupdf(pdf_path)
        
        # 如果可用，使用pdfplumber进行补充检测
        if PDFPLUMBER_AVAILABLE:
            plumber_result = self._detect_with_pdfplumber(pdf_path)
            detection_result = self._merge_detection_results(detection_result, plumber_result)
        
        # 确定最终类型
        final_type = self._determine_final_type(detection_result)
        
        result = {
            'pdf_type': final_type,
            'total_pages': detection_result['total_pages'],
            'text_pages': detection_result['text_pages'],
            'scan_pages': detection_result['scan_pages'],
            'mixed_pages': detection_result['mixed_pages'],
            'text_ratio': detection_result['text_ratio'],
            'details': detection_result['page_details']
        }
        
        print(f"📊 检测结果:")
        print(f"   PDF类型: {final_type}")
        print(f"   总页数: {result['total_pages']}")
        print(f"   文本页: {result['text_pages']} 页")
        print(f"   扫描页: {result['scan_pages']} 页")
        print(f"   混合页: {result['mixed_pages']} 页")
        print(f"   文本比例: {result['text_ratio']:.1%}")
        
        return result
    
    def _detect_with_pymupdf(self, pdf_path: Path) -> Dict[str, Any]:
        """使用PyMuPDF检测PDF类型"""
        doc = fitz.open(str(pdf_path))
        
        page_details = []
        text_pages = 0
        scan_pages = 0
        mixed_pages = 0
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # 提取文本
            text = page.get_text()
            text_length = len(text.strip())
            
            # 获取图像信息
            image_list = page.get_images()
            image_count = len(image_list)
            
            # 计算图像覆盖面积比例
            page_rect = page.rect
            page_area = page_rect.width * page_rect.height
            image_area_ratio = 0
            
            if image_list:
                total_image_area = 0
                for img_index, img in enumerate(image_list):
                    try:
                        # 获取图像的显示矩形
                        img_rects = page.get_image_rects(img[0])
                        for rect in img_rects:
                            total_image_area += rect.width * rect.height
                    except:
                        # 如果无法获取精确面积，使用估算
                        total_image_area += page_area * 0.1  # 估算每个图像占10%
                
                image_area_ratio = min(total_image_area / page_area, 1.0)
            
            # 判断页面类型
            page_type = self._classify_page_type(text_length, image_count, image_area_ratio)
            
            page_info = {
                'page_num': page_num + 1,
                'type': page_type,
                'text_length': text_length,
                'image_count': image_count,
                'image_area_ratio': image_area_ratio,
                'has_meaningful_text': text_length > self.text_threshold
            }
            
            page_details.append(page_info)
            
            if page_type == 'text':
                text_pages += 1
            elif page_type == 'scan':
                scan_pages += 1
            else:
                mixed_pages += 1
        
        doc.close()
        
        total_pages = len(page_details)
        text_ratio = text_pages / total_pages if total_pages > 0 else 0
        
        return {
            'total_pages': total_pages,
            'text_pages': text_pages,
            'scan_pages': scan_pages,
            'mixed_pages': mixed_pages,
            'text_ratio': text_ratio,
            'page_details': page_details
        }
    
    def _detect_with_pdfplumber(self, pdf_path: Path) -> Dict[str, Any]:
        """使用pdfplumber进行补充检测"""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_details = []
                
                for page_num, page in enumerate(pdf.pages):
                    # 提取文本
                    text = page.extract_text() or ""
                    text_length = len(text.strip())
                    
                    # 检测表格
                    tables = page.extract_tables()
                    table_count = len(tables)
                    
                    # 获取页面对象信息
                    chars = page.chars
                    char_count = len(chars)
                    
                    page_info = {
                        'page_num': page_num + 1,
                        'text_length': text_length,
                        'table_count': table_count,
                        'char_count': char_count,
                        'has_structured_content': table_count > 0 or char_count > 0
                    }
                    
                    page_details.append(page_info)
                
                return {
                    'page_details': page_details,
                    'method': 'pdfplumber'
                }
        except Exception as e:
            print(f"⚠ pdfplumber检测失败: {e}")
            return {'page_details': [], 'method': 'pdfplumber_failed'}
    
    def _merge_detection_results(self, pymupdf_result: Dict, plumber_result: Dict) -> Dict:
        """合并两种检测方法的结果"""
        # 以PyMuPDF结果为主，用pdfplumber结果补充
        merged_result = pymupdf_result.copy()
        
        if plumber_result.get('page_details'):
            plumber_pages = {p['page_num']: p for p in plumber_result['page_details']}
            
            for page_info in merged_result['page_details']:
                page_num = page_info['page_num']
                if page_num in plumber_pages:
                    plumber_info = plumber_pages[page_num]
                    # 补充表格信息
                    page_info['table_count'] = plumber_info.get('table_count', 0)
                    page_info['char_count'] = plumber_info.get('char_count', 0)
                    page_info['has_structured_content'] = plumber_info.get('has_structured_content', False)
        
        return merged_result
    
    def _classify_page_type(self, text_length: int, image_count: int, image_area_ratio: float) -> str:
        """
        分类页面类型
        
        Args:
            text_length: 文本长度
            image_count: 图像数量
            image_area_ratio: 图像面积比例
            
        Returns:
            str: 页面类型 ('text', 'scan', 'mixed')
        """
        # 纯文本页面：有足够文本，图像很少或面积很小
        if (text_length > self.text_threshold and 
            (image_count == 0 or image_area_ratio < 0.1)):
            return 'text'
        
        # 扫描页面：文本很少，图像很多或面积很大
        elif (text_length < self.text_threshold and 
              (image_count > 0 and image_area_ratio > 0.5)):
            return 'scan'
        
        # 混合页面：其他情况
        else:
            return 'mixed'
    
    def _determine_final_type(self, detection_result: Dict) -> str:
        """
        确定PDF的最终类型
        
        Args:
            detection_result: 检测结果
            
        Returns:
            str: PDF类型 ('text', 'scan', 'mixed')
        """
        text_ratio = detection_result['text_ratio']
        scan_pages = detection_result['scan_pages']
        mixed_pages = detection_result['mixed_pages']
        total_pages = detection_result['total_pages']
        
        # 如果80%以上是文本页面，认为是纯文本PDF
        if text_ratio >= 0.8:
            return 'text'
        
        # 如果80%以上是扫描页面，认为是扫描版PDF
        elif (scan_pages + mixed_pages) / total_pages >= 0.8 and text_ratio < 0.2:
            return 'scan'
        
        # 其他情况认为是混合版PDF
        else:
            return 'mixed'
