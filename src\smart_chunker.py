#!/usr/bin/env python3
"""
智能分块器
基于语义和内容类型进行更智能的文档分块
"""

import re
from typing import List, Dict, Any, Tuple
from .logger import get_logger


class SmartChunker:
    """智能分块器"""
    
    def __init__(self, max_chunk_size: int = 1000, min_chunk_size: int = 100, overlap_size: int = 50):
        """
        初始化智能分块器
        
        Args:
            max_chunk_size: 最大块大小
            min_chunk_size: 最小块大小  
            overlap_size: 重叠大小
        """
        self.max_chunk_size = max_chunk_size
        self.min_chunk_size = min_chunk_size
        self.overlap_size = overlap_size
        self.logger = get_logger()
        
        # 定义语义边界标识符
        self.semantic_boundaries = [
            r'^#+\s+',  # Markdown标题
            r'^\d+[\.、]\s+',  # 编号列表
            r'^[一二三四五六七八九十]+[、\.]\s+',  # 中文编号
            r'^第[一二三四五六七八九十\d]+[章节条款]\s+',  # 章节标识
            r'^\s*表\s*\d+',  # 表格标识
            r'^\s*图\s*\d+',  # 图片标识
            r'^\s*附录',  # 附录标识
        ]
        
        # 定义重要内容模式
        self.important_patterns = [
            r'营业收入.*?\d+',
            r'净利润.*?\d+', 
            r'总资产.*?\d+',
            r'同比.*?\d+%',
            r'增长.*?\d+%',
            r'毛利率.*?\d+%',
            r'主营业务.*?收入',
            r'分部.*?收入'
        ]
    
    def smart_split(self, content: str, source: str = "") -> List[Dict[str, Any]]:
        """
        智能分块
        
        Args:
            content: 要分块的内容
            source: 内容来源
            
        Returns:
            List[Dict[str, Any]]: 分块结果列表
        """
        if not content or len(content) < self.min_chunk_size:
            return []
        
        with self.logger.timer("智能分块", source=source, content_length=len(content)):
            # 1. 预处理：识别文档结构
            structured_content = self._analyze_document_structure(content)
            
            # 2. 基于结构进行分块
            chunks = self._structure_based_chunking(structured_content, source)
            
            # 3. 优化分块质量
            optimized_chunks = self._optimize_chunks(chunks)
            
            self.logger.info(f"智能分块完成，生成 {len(optimized_chunks)} 个块")
            
            return optimized_chunks
    
    def _analyze_document_structure(self, content: str) -> Dict[str, Any]:
        """分析文档结构"""
        lines = content.split('\n')
        
        structure = {
            'lines': lines,
            'headers': [],
            'tables': [],
            'lists': [],
            'paragraphs': [],
            'important_sections': []
        }
        
        current_section = []
        section_type = 'paragraph'
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是标题
            if self._is_header(line):
                if current_section:
                    structure[f'{section_type}s'].append({
                        'content': '\n'.join(current_section),
                        'start_line': i - len(current_section),
                        'end_line': i - 1
                    })
                    current_section = []
                
                structure['headers'].append({
                    'content': line,
                    'line_number': i,
                    'level': self._get_header_level(line)
                })
                section_type = 'paragraph'
            
            # 检查是否是表格
            elif self._is_table_line(line):
                if section_type != 'table':
                    if current_section:
                        structure[f'{section_type}s'].append({
                            'content': '\n'.join(current_section),
                            'start_line': i - len(current_section),
                            'end_line': i - 1
                        })
                        current_section = []
                    section_type = 'table'
                current_section.append(line)
            
            # 检查是否是列表
            elif self._is_list_item(line):
                if section_type != 'list':
                    if current_section:
                        structure[f'{section_type}s'].append({
                            'content': '\n'.join(current_section),
                            'start_line': i - len(current_section),
                            'end_line': i - 1
                        })
                        current_section = []
                    section_type = 'list'
                current_section.append(line)
            
            # 检查是否包含重要信息
            elif self._contains_important_info(line):
                if current_section:
                    structure[f'{section_type}s'].append({
                        'content': '\n'.join(current_section),
                        'start_line': i - len(current_section),
                        'end_line': i - 1
                    })
                    current_section = []
                
                structure['important_sections'].append({
                    'content': line,
                    'line_number': i,
                    'importance_score': self._calculate_importance_score(line)
                })
                section_type = 'paragraph'
            
            else:
                if section_type != 'paragraph':
                    if current_section:
                        structure[f'{section_type}s'].append({
                            'content': '\n'.join(current_section),
                            'start_line': i - len(current_section),
                            'end_line': i - 1
                        })
                        current_section = []
                    section_type = 'paragraph'
                current_section.append(line)
        
        # 处理最后一个section
        if current_section:
            structure[f'{section_type}s'].append({
                'content': '\n'.join(current_section),
                'start_line': len(lines) - len(current_section),
                'end_line': len(lines) - 1
            })
        
        return structure
    
    def _structure_based_chunking(self, structure: Dict[str, Any], source: str) -> List[Dict[str, Any]]:
        """基于结构的分块"""
        chunks = []
        chunk_id = 0
        
        # 优先处理重要信息
        for important_section in structure['important_sections']:
            chunk_id += 1
            chunks.append({
                'content': important_section['content'],
                'source': source,
                'type': 'important',
                'chunk_id': chunk_id,
                'processing_method': 'smart_chunking',
                'content_type': 'important_info',
                'importance_score': important_section['importance_score']
            })
        
        # 处理表格
        for table in structure['tables']:
            if len(table['content']) >= self.min_chunk_size:
                chunk_id += 1
                chunks.append({
                    'content': table['content'],
                    'source': source,
                    'type': 'table',
                    'chunk_id': chunk_id,
                    'processing_method': 'smart_chunking',
                    'content_type': 'table'
                })
        
        # 处理标题和相关段落
        for i, header in enumerate(structure['headers']):
            section_content = [header['content']]
            
            # 查找该标题下的内容
            header_line = header['line_number']
            next_header_line = (structure['headers'][i + 1]['line_number'] 
                              if i + 1 < len(structure['headers']) else len(structure['lines']))
            
            # 收集该标题下的段落
            for paragraph in structure['paragraphs']:
                if header_line < paragraph['start_line'] < next_header_line:
                    section_content.append(paragraph['content'])
            
            section_text = '\n'.join(section_content)
            
            if len(section_text) >= self.min_chunk_size:
                # 如果section太长，进一步分块
                if len(section_text) > self.max_chunk_size:
                    sub_chunks = self._split_long_content(section_text, source, chunk_id)
                    chunks.extend(sub_chunks)
                    chunk_id += len(sub_chunks)
                else:
                    chunk_id += 1
                    chunks.append({
                        'content': section_text,
                        'source': source,
                        'type': 'section',
                        'chunk_id': chunk_id,
                        'processing_method': 'smart_chunking',
                        'content_type': 'section',
                        'header_level': header['level']
                    })
        
        # 处理剩余的段落
        for paragraph in structure['paragraphs']:
            if len(paragraph['content']) >= self.min_chunk_size:
                chunk_id += 1
                chunks.append({
                    'content': paragraph['content'],
                    'source': source,
                    'type': 'text',
                    'chunk_id': chunk_id,
                    'processing_method': 'smart_chunking',
                    'content_type': 'paragraph'
                })
        
        return chunks
    
    def _split_long_content(self, content: str, source: str, start_chunk_id: int) -> List[Dict[str, Any]]:
        """分割长内容"""
        chunks = []
        sentences = re.split(r'[。！？\n]', content)
        
        current_chunk = []
        current_length = 0
        chunk_id = start_chunk_id
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if current_length + len(sentence) > self.max_chunk_size and current_chunk:
                chunk_id += 1
                chunks.append({
                    'content': ''.join(current_chunk),
                    'source': source,
                    'type': 'text',
                    'chunk_id': chunk_id,
                    'processing_method': 'smart_chunking',
                    'content_type': 'split_content'
                })
                
                # 保留重叠
                if len(current_chunk) > 1:
                    current_chunk = current_chunk[-1:]
                    current_length = len(current_chunk[0])
                else:
                    current_chunk = []
                    current_length = 0
            
            current_chunk.append(sentence + '。')
            current_length += len(sentence) + 1
        
        # 处理最后一个chunk
        if current_chunk:
            chunk_id += 1
            chunks.append({
                'content': ''.join(current_chunk),
                'source': source,
                'type': 'text',
                'chunk_id': chunk_id,
                'processing_method': 'smart_chunking',
                'content_type': 'split_content'
            })
        
        return chunks
    
    def _optimize_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化分块质量"""
        optimized = []
        
        for chunk in chunks:
            content = chunk['content']
            
            # 移除过短的chunk
            if len(content) < self.min_chunk_size:
                continue
            
            # 清理内容
            cleaned_content = self._clean_chunk_content(content)
            if not cleaned_content:
                continue
            
            chunk['content'] = cleaned_content
            chunk['char_count'] = len(cleaned_content)
            chunk['word_count'] = len(cleaned_content.split())
            
            optimized.append(chunk)
        
        return optimized
    
    def _clean_chunk_content(self, content: str) -> str:
        """清理chunk内容"""
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 移除行首行尾空格
        lines = [line.strip() for line in content.split('\n')]
        content = '\n'.join(line for line in lines if line)
        
        return content.strip()
    
    def _is_header(self, line: str) -> bool:
        """检查是否是标题"""
        for pattern in self.semantic_boundaries:
            if re.match(pattern, line):
                return True
        return False
    
    def _get_header_level(self, line: str) -> int:
        """获取标题级别"""
        if line.startswith('#'):
            return line.count('#')
        elif re.match(r'^\d+[\.、]\s+', line):
            return 2
        elif re.match(r'^[一二三四五六七八九十]+[、\.]\s+', line):
            return 3
        return 1
    
    def _is_table_line(self, line: str) -> bool:
        """检查是否是表格行"""
        return '|' in line or re.match(r'^\s*表\s*\d+', line)
    
    def _is_list_item(self, line: str) -> bool:
        """检查是否是列表项"""
        return re.match(r'^\s*[\d\-\*•]\s+', line) is not None
    
    def _contains_important_info(self, line: str) -> bool:
        """检查是否包含重要信息"""
        for pattern in self.important_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                return True
        return False
    
    def _calculate_importance_score(self, line: str) -> float:
        """计算重要性分数"""
        score = 0.0
        
        # 财务数据权重更高
        financial_keywords = ['营业收入', '净利润', '总资产', '净资产']
        for keyword in financial_keywords:
            if keyword in line:
                score += 0.3
        
        # 增长数据
        growth_keywords = ['同比', '增长', '下降', '变动']
        for keyword in growth_keywords:
            if keyword in line:
                score += 0.2
        
        # 包含数字
        if re.search(r'\d+', line):
            score += 0.1
        
        # 包含百分比
        if '%' in line:
            score += 0.1
        
        return min(score, 1.0)
