#!/usr/bin/env python3
"""
调试chunk 48为什么没有被检索到
"""

from src.hybrid_vector_store import HybridVectorStore
from src.embedding_model import EmbeddingModel
from src.enhanced_retriever import EnhancedRetriever
import numpy as np

def debug_chunk_48():
    """调试chunk 48"""
    store = HybridVectorStore()
    embedding_model = EmbeddingModel()
    retriever = EnhancedRetriever(store, embedding_model)

    # 检查chunk 48
    chunk_48 = store.metadata[48]
    print('Chunk 48内容:')
    print(chunk_48.get('content', '')[:500])
    print()

    # 检查是否在伊利集团筛选范围内
    document_info = chunk_48.get('document_info', '')
    print(f'Document info: {document_info}')
    print(f'包含伊利公司名称: {"内蒙古伊利实业集团股份有限公司" in document_info}')
    print(f'包含2025年第一季度: {"2025年第一季度" in document_info}')
    print()

    # 测试关键词匹配分数
    query = "稀释每股收益"
    content = chunk_48.get('content', '')
    keyword_score = retriever._calculate_keyword_score(query, content)
    print(f'关键词匹配分数: {keyword_score}')

    # 测试向量相似度
    query_vectors = embedding_model.encode_batch([query])
    query_vector = np.array(query_vectors[0]).reshape(1, -1)

    results = store.search(query_vector, limit=50)
    
    chunk_48_rank = None
    chunk_48_score = None
    
    for i, result in enumerate(results):
        vector_id = result.get('vector_id', -1)
        if vector_id == 48:
            chunk_48_rank = i + 1
            chunk_48_score = result.get('score', 0)
            break
    
    print(f'Chunk 48在向量检索中的排名: {chunk_48_rank}')
    print(f'Chunk 48的向量相似度分数: {chunk_48_score}')
    
    if chunk_48_score:
        hybrid_score = chunk_48_score + (keyword_score * 2.0)
        print(f'Chunk 48的混合分数: {hybrid_score}')

if __name__ == "__main__":
    debug_chunk_48()
