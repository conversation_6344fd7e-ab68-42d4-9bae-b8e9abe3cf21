#!/usr/bin/env python3
"""
测试两阶段检索器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.hybrid_vector_store import HybridVectorStore
from src.embedding_model import EmbeddingModel
from src.two_stage_retriever import TwoStageRetriever


def test_two_stage_retriever():
    """测试两阶段检索器"""
    print("🔧 初始化两阶段检索器...")
    
    # 初始化组件
    vector_store = HybridVectorStore()
    embedding_model = EmbeddingModel()
    retriever = TwoStageRetriever(vector_store, embedding_model)
    
    print(f"✅ 知识库加载完成，共 {len(vector_store.metadata)} 个文档")
    
    # 获取公司文档统计
    company_stats = retriever.get_company_document_stats()
    print(f"\n📊 公司文档统计:")
    for company, count in company_stats.items():
        print(f"   {company}: {count} 个文档")
    
    # 预览伊利集团的文档
    print(f"\n📋 伊利集团文档预览:")
    yili_docs = retriever.preview_company_documents("伊利", limit=5)
    for i, doc in enumerate(yili_docs, 1):
        print(f"   文档 {i}:")
        print(f"      索引: {doc['index']}")
        print(f"      文档信息: {doc['document_info']}")
        print(f"      chunk_id: {doc['chunk_id']}")
        print(f"      内容预览: {doc['content_preview']}")
        print()
    
    # 测试查询
    test_queries = [
        ("伊利集团2025年第一季度营业收入是多少？", "伊利"),
        ("营业收入", "伊利"),
        ("净利润", "伊利"),
        ("美的集团营业收入", "美的"),
        ("营业收入", None),  # 不指定公司
    ]
    
    for query, company in test_queries:
        print(f"\n{'='*60}")
        print(f"🔍 测试查询: {query}")
        if company:
            print(f"🏢 目标公司: {company}")
        
        # 使用两阶段检索
        results = retriever.two_stage_search(
            query=query,
            company_name=company,
            top_k=5,
            use_rerank=False  # 先不使用重排序测试
        )
        
        print(f"📊 检索结果: {len(results)} 个文档")
        
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            source = result.get('source', '')
            score = result.get('score', 0)
            doc_info = result.get('document_info', '')
            method = result.get('retrieval_method', '')
            
            print(f"\n📄 结果 {i}:")
            print(f"   📁 来源: {source}")
            print(f"   📊 相似度分数: {score:.4f}")
            print(f"   🔍 检索方法: {method}")
            print(f"   🏷️  文档信息: {doc_info}")
            print(f"   📝 内容: {content[:200]}...")
        
        print("-" * 60)


if __name__ == "__main__":
    test_two_stage_retriever()
