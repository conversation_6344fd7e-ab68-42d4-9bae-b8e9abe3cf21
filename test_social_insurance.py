#!/usr/bin/env python3
"""
测试社保证明文档的解析和查询
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.simplified_document_processor import SimplifiedDocumentProcessor
from src.knowledge_base import KnowledgeBase

def test_social_insurance_parsing():
    """测试社保证明文档解析"""
    print("🔍 测试社保证明文档解析")
    print("=" * 50)
    
    # 初始化处理器
    processor = SimplifiedDocumentProcessor()
    
    # 解析文档
    file_path = "data/documents/北京市社会保险个人社保参保证明.pdf"
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        chunks = processor.process_file(file_path, save_results=True)
        print(f"✅ 解析成功，生成 {len(chunks)} 个片段")
        
        # 显示片段内容
        for i, chunk in enumerate(chunks, 1):
            print(f"\n📄 片段 {i}:")
            print(f"   类型: {chunk['type']}")
            print(f"   长度: {len(chunk['content'])} 字符")
            print(f"   内容预览: {chunk['content'][:200]}...")
            
            # 检查是否包含关键信息
            content = chunk['content']
            if "王志扬" in content:
                print("   ✅ 包含姓名信息")
            if "130728199802137012" in content:
                print("   ✅ 包含社会保险号码")
            if "社会保险" in content or "社保" in content:
                print("   ✅ 包含社保关键词")
        
        return chunks
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_knowledge_base_search():
    """测试知识库搜索"""
    print("\n🔍 测试知识库搜索")
    print("=" * 50)
    
    try:
        # 初始化知识库
        kb = KnowledgeBase()
        
        # 搜索测试
        test_queries = [
            "王志扬",
            "社会保险号码",
            "130728199802137012",
            "北京市社会保险",
            "参保证明"
        ]
        
        for query in test_queries:
            print(f"\n🔍 搜索: {query}")
            try:
                results = kb.search(query, top_k=3)
                print(f"   找到 {len(results)} 个结果")
                
                for i, result in enumerate(results, 1):
                    print(f"   📄 结果 {i}:")
                    print(f"      来源: {result.get('source', 'unknown')}")
                    print(f"      分数: {result.get('score', 0):.4f}")
                    content = result.get('content', '')
                    print(f"      内容: {content[:100]}...")
                    
            except Exception as e:
                print(f"   ❌ 搜索失败: {e}")
    
    except Exception as e:
        print(f"❌ 知识库初始化失败: {e}")

def check_parsing_results():
    """检查解析结果文件"""
    print("\n📁 检查解析结果文件")
    print("=" * 50)
    
    results_dir = Path("parsing_results")
    
    # 查找社保相关的解析结果
    social_files = []
    for file_path in results_dir.rglob("*社保*"):
        social_files.append(file_path)
    
    for file_path in results_dir.rglob("*北京市社会保险*"):
        social_files.append(file_path)
    
    if not social_files:
        print("❌ 未找到社保相关的解析结果文件")
        return
    
    print(f"✅ 找到 {len(social_files)} 个相关文件:")
    for file_path in social_files:
        print(f"   📄 {file_path}")
        
        # 如果是JSON文件，显示内容
        if file_path.suffix == '.json':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list) and data:
                    print(f"      包含 {len(data)} 个片段")
                    first_chunk = data[0]
                    if 'content' in first_chunk:
                        content = first_chunk['content']
                        print(f"      首个片段内容: {content[:100]}...")
                        
                        # 检查关键信息
                        if "王志扬" in content:
                            print("      ✅ 包含姓名")
                        if "130728199802137012" in content:
                            print("      ✅ 包含社保号码")
                            
            except Exception as e:
                print(f"      ❌ 读取失败: {e}")

def main():
    """主函数"""
    print("🧪 社保证明文档测试")
    print("=" * 60)
    
    # 1. 测试文档解析
    chunks = test_social_insurance_parsing()
    
    # 2. 检查解析结果文件
    check_parsing_results()
    
    # 3. 测试知识库搜索
    test_knowledge_base_search()
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
