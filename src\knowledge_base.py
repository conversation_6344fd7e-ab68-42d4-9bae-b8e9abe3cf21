"""
知识库构建和管理模块
负责文档处理、向量化和FAISS存储
"""

import os
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from .simplified_document_processor import SimplifiedDocumentProcessor
from .text_splitter import TextSplitter
from .embedding_model import EmbeddingModel
from .hybrid_vector_store import HybridVectorStore
from config.config import DATA_DIR, FAISS_INDEX_PATH


class KnowledgeBase:
    """知识库管理类"""
    
    def __init__(self):
        """初始化知识库"""
        self.doc_processor = SimplifiedDocumentProcessor()
        self.text_splitter = TextSplitter()
        self.embedding_model = EmbeddingModel()
        self.vector_store = HybridVectorStore()
        
        # 知识库元数据文件
        self.metadata_file = FAISS_INDEX_PATH / "kb_metadata.json"
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载知识库元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠ 加载元数据失败: {e}")
        
        return {
            "created_at": datetime.now().isoformat(),
            "documents": {},
            "total_chunks": 0,
            "last_updated": None
        }
    
    def _save_metadata(self):
        """保存知识库元数据"""
        try:
            FAISS_INDEX_PATH.mkdir(parents=True, exist_ok=True)
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠ 保存元数据失败: {e}")
    
    def add_document(self, file_path: Path) -> bool:
        """
        添加单个文档到知识库
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            bool: 是否成功添加
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            # 检查文件是否已经处理过
            file_key = str(file_path.resolve())
            file_stat = file_path.stat()
            file_mtime = file_stat.st_mtime
            
            if file_key in self.metadata["documents"]:
                stored_mtime = self.metadata["documents"][file_key].get("mtime", 0)
                if file_mtime <= stored_mtime:
                    print(f"⏭ 文档未修改，跳过: {file_path.name}")
                    return True
            
            print(f"📄 处理文档: {file_path.name}")

            # 处理文档
            print(f"  🔄 步骤1/4: 解析文档内容...")
            documents = self.doc_processor.process_file(file_path)
            if not documents:
                print(f"⚠ 文档无内容: {file_path.name}")
                return False

            # 分割文档
            print(f"  🔄 步骤2/4: 分割文档（{len(documents)} 个原始块）...")
            split_documents = self.text_splitter.split_documents(documents)
            print(f"  ✅ 分割完成，生成 {len(split_documents)} 个文本片段")

            # 获取文本内容
            print(f"  🔄 步骤3/4: 准备文本内容...")
            texts = []
            for doc in split_documents:
                content = doc["content"]
                if isinstance(content, dict):
                    content = str(content)
                texts.append(content)

            # 生成向量嵌入（使用批量处理）
            print(f"  🔄 步骤4/4: 生成向量嵌入...")
            if len(texts) >= 10:
                print(f"    🚀 文本数量较多（{len(texts)}），使用批量异步处理...")
                embeddings = self.embedding_model.encode_batch(texts, use_batch_api=True)
            else:
                embeddings = self.embedding_model.encode(texts)
            
            # 存储到混合向量数据库
            self.vector_store.add_vectors(embeddings, split_documents)
            
            # 更新元数据
            self.metadata["documents"][file_key] = {
                "name": file_path.name,
                "path": str(file_path),
                "mtime": file_mtime,
                "size": file_stat.st_size,
                "chunks": len(split_documents),
                "added_at": datetime.now().isoformat()
            }
            
            self.metadata["total_chunks"] += len(split_documents)
            self.metadata["last_updated"] = datetime.now().isoformat()
            self._save_metadata()
            
            print(f"✅ 成功添加文档: {file_path.name} ({len(split_documents)} 个片段)")
            return True
            
        except Exception as e:
            print(f"❌ 处理文档失败 {file_path}: {str(e)}")
            return False
    
    def add_documents_from_directory(self, directory: Path, recursive: bool = True) -> Dict[str, int]:
        """
        从目录批量添加文档
        
        Args:
            directory: 文档目录路径
            recursive: 是否递归处理子目录
            
        Returns:
            Dict[str, int]: 处理结果统计
        """
        directory = Path(directory)
        if not directory.exists():
            print(f"❌ 目录不存在: {directory}")
            return {"success": 0, "failed": 0, "skipped": 0}
        
        print(f"📚 扫描文档目录: {directory}")
        
        # 支持的文件扩展名
        from config.config import SUPPORTED_EXTENSIONS
        supported_exts = []
        for exts in SUPPORTED_EXTENSIONS.values():
            supported_exts.extend(exts)
        
        # 收集文件
        files = []
        if recursive:
            for ext in supported_exts:
                files.extend(directory.rglob(f"*{ext}"))
        else:
            for ext in supported_exts:
                files.extend(directory.glob(f"*{ext}"))
        
        # 处理文件
        stats = {"success": 0, "failed": 0, "skipped": 0}
        
        for file_path in files:
            if file_path.is_file():
                if self.add_document(file_path):
                    stats["success"] += 1
                else:
                    stats["failed"] += 1
        
        print(f"📊 处理完成: 成功 {stats['success']}, 失败 {stats['failed']}")
        return stats
    
    def remove_document(self, file_path: Path) -> bool:
        """
        从知识库中移除文档
        注意：这只会从元数据中移除，FAISS索引需要重建
        """
        file_key = str(Path(file_path).resolve())
        if file_key in self.metadata["documents"]:
            doc_info = self.metadata["documents"].pop(file_key)
            self.metadata["total_chunks"] -= doc_info.get("chunks", 0)
            self.metadata["last_updated"] = datetime.now().isoformat()
            self._save_metadata()
            print(f"✅ 已从知识库移除: {file_path}")
            print("⚠ 注意：需要重建索引以完全移除向量数据")
            return True
        else:
            print(f"❌ 文档不在知识库中: {file_path}")
            return False
    
    def rebuild_index(self):
        """重建整个知识库索引"""
        print("🔄 开始重建知识库索引...")
        
        # 清空现有索引
        self.vector_store.clear()
        
        # 重新处理所有文档
        documents_to_process = list(self.metadata["documents"].keys())
        self.metadata["documents"] = {}
        self.metadata["total_chunks"] = 0
        
        success_count = 0
        for file_path in documents_to_process:
            if Path(file_path).exists():
                if self.add_document(Path(file_path)):
                    success_count += 1
            else:
                print(f"⚠ 文件不存在，跳过: {file_path}")
        
        print(f"✅ 索引重建完成: {success_count} 个文档")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        vector_stats = self.vector_store.get_stats()
        
        return {
            "documents_count": len(self.metadata["documents"]),
            "total_chunks": self.metadata["total_chunks"],
            "vector_count": vector_stats["total_vectors"],
            "vector_dim": vector_stats["vector_dim"],
            "created_at": self.metadata["created_at"],
            "last_updated": self.metadata["last_updated"]
        }
    
    def list_documents(self) -> List[Dict[str, Any]]:
        """列出知识库中的所有文档"""
        return list(self.metadata["documents"].values())
    
    def clear(self):
        """清空整个知识库"""
        self.vector_store.clear()
        self.metadata = {
            "created_at": datetime.now().isoformat(),
            "documents": {},
            "total_chunks": 0,
            "last_updated": None
        }
        self._save_metadata()
        print("✅ 知识库已清空")
