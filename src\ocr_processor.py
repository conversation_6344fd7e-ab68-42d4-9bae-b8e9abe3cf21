#!/usr/bin/env python3
"""
OCR处理器
简化版本，使用基本的PDF文本提取
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Any

# 简化版OCR处理器
MARKER_AVAILABLE = True  # 使用简化实现


class OCRProcessor:
    """简化OCR处理器"""

    def __init__(self, use_gpu: bool = True):
        """
        初始化OCR处理器

        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu
        print(f"✓ 简化OCR处理器初始化成功")

    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return MARKER_AVAILABLE
    
    def extract_text_from_pdf_images(self, file_path: str) -> List[Dict[str, Any]]:
        """
        使用marker-pdf从PDF提取文字和结构

        Args:
            file_path: PDF文件路径

        Returns:
            List[Dict[str, Any]]: 处理结果
        """
        if not self.is_available():
            return []

        try:
            print(f"📄 开始使用marker-pdf处理: {Path(file_path).name}")
            start_time = time.time()

            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_file = os.path.join(temp_dir, "output.md")

                # 使用命令行调用marker-pdf（避免numpy版本冲突）
                cmd = [
                    'python', '-c',
                    f'''
import sys
sys.path.insert(0, r"E:/miniconda3/envs/myrag/Lib/site-packages")
try:
    from marker.convert import convert_single_pdf
    from marker.models import load_all_models

    print("🔧 加载marker模型...")
    model_lst = load_all_models()

    print("🔧 开始转换PDF...")
    full_text, _, _ = convert_single_pdf(r"{file_path}", model_lst)

    with open(r"{output_file}", "w", encoding="utf-8") as f:
        f.write(full_text)

    print("✅ 转换完成")
except Exception as e:
    print(f"❌ 转换失败: {{e}}")
    sys.exit(1)
'''
                ]

                print(f"🔧 执行marker-pdf转换...")

                # 执行命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )

                if result.returncode != 0:
                    print(f"❌ marker-pdf执行失败:")
                    print(f"   stdout: {result.stdout}")
                    print(f"   stderr: {result.stderr}")
                    return []

                # 读取输出文件
                if os.path.exists(output_file):
                    with open(output_file, 'r', encoding='utf-8') as f:
                        markdown_content = f.read()
                else:
                    print(f"❌ 输出文件不存在: {output_file}")
                    return []

                # 统计信息
                char_count = len(markdown_content)
                word_count = len(markdown_content.split())
                line_count = len(markdown_content.split('\n'))

                print(f"✅ marker-pdf处理完成:")
                print(f"   📝 字符数: {char_count}")
                print(f"   📝 单词数: {word_count}")
                print(f"   📝 行数: {line_count}")

                # 返回统一格式的结果
                return [{
                    'page': 'all',
                    'ocr_text': markdown_content,
                    'confidence': 1.0,  # marker不提供置信度，设为1.0
                    'word_count': word_count,
                    'char_count': char_count,
                    'line_count': line_count,
                    'processing_time': processing_time,
                    'processing_method': 'marker-pdf'
                }]

        except subprocess.TimeoutExpired:
            processing_time = time.time() - start_time
            print(f"❌ marker-pdf处理超时 ({processing_time:.2f}秒)")
            return []
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"❌ marker-pdf处理失败 ({processing_time:.2f}秒): {e}")
            return []
    
    def extract_text_from_image(self, file_path: str) -> Dict[str, Any]:
        """
        从图片文件提取文字（简化版，主要用于marker-pdf）

        Args:
            file_path: 图片文件路径

        Returns:
            Dict[str, Any]: OCR结果
        """
        print(f"⚠ 图片OCR功能已简化，建议将图片转换为PDF后使用marker-pdf处理")
        return {
            'text': f'图片文件: {Path(file_path).name}',
            'confidence': 0.0,
            'processing_method': 'placeholder'
        }
    

    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]],
                                     file_name: str) -> str:
        """
        将marker-pdf结果格式化为Markdown

        Args:
            ocr_results: marker-pdf结果列表
            file_name: 文件名

        Returns:
            str: Markdown格式的内容
        """
        if not ocr_results:
            return f"# {Path(file_name).stem}\n\n无法处理此文件。"

        # marker-pdf直接返回markdown内容
        result = ocr_results[0]
        markdown_content = result.get('ocr_text', '')

        # 添加处理信息
        header_lines = [
            f"# {Path(file_name).stem} (marker-pdf处理结果)\n",
            f"**处理时间**: {result.get('processing_time', 0):.2f}秒",
            f"**字符数**: {result.get('char_count', 0)}",
            f"**单词数**: {result.get('word_count', 0)}",
            f"**行数**: {result.get('line_count', 0)}\n",
            "---\n"
        ]

        return "\n".join(header_lines) + markdown_content
