#!/usr/bin/env python3
"""
OCR处理器
使用PaddleOCR进行文字识别
"""

import os
import cv2
import numpy as np
import fitz  # PyMuPDF
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
import io

try:
    import paddleocr
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self, use_gpu: bool = True):
        """
        初始化OCR处理器
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu
        self.ocr = None
        
        if PADDLEOCR_AVAILABLE:
            try:
                print(f"🔧 初始化PaddleOCR (GPU: {use_gpu})...")
                self.ocr = paddleocr.PaddleOCR(
                    use_angle_cls=True,  # 使用角度分类器
                    lang='ch',          # 中文识别
                    use_gpu=use_gpu,    # GPU加速
                    show_log=False      # 不显示详细日志
                )
                print("✓ PaddleOCR初始化成功")
            except Exception as e:
                print(f"❌ PaddleOCR初始化失败: {e}")
                self.ocr = None
        else:
            print("❌ PaddleOCR未安装")
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return self.ocr is not None
    
    def extract_text_from_pdf_images(self, file_path: str) -> List[Dict[str, Any]]:
        """
        从PDF中的图片提取文字
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            List[Dict[str, Any]]: 每页的OCR结果
        """
        if not self.is_available():
            return []
        
        results = []
        
        try:
            doc = fitz.open(file_path)
            print(f"📄 开始OCR处理PDF: {Path(file_path).name} ({len(doc)} 页)")
            
            for page_num in range(len(doc)):
                print(f"   🔍 处理第 {page_num + 1} 页...")
                page = doc[page_num]
                
                # 将页面转换为图片
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放提高OCR精度
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # 转换为OpenCV格式
                nparr = np.frombuffer(img_data, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                # OCR识别
                ocr_result = self._ocr_image(image, f"第{page_num + 1}页")
                
                results.append({
                    'page': page_num + 1,
                    'ocr_text': ocr_result['text'],
                    'confidence': ocr_result['confidence'],
                    'word_count': len(ocr_result['text'].split()),
                    'char_count': len(ocr_result['text'])
                })
            
            doc.close()
            
            total_chars = sum(r['char_count'] for r in results)
            print(f"✅ PDF OCR完成，总计识别 {total_chars} 个字符")
            
            return results
            
        except Exception as e:
            print(f"❌ PDF OCR处理失败: {e}")
            return []
    
    def extract_text_from_image(self, file_path: str) -> Dict[str, Any]:
        """
        从图片文件提取文字
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Dict[str, Any]: OCR结果
        """
        if not self.is_available():
            return {'text': '', 'confidence': 0.0}
        
        try:
            print(f"🖼️  开始OCR处理图片: {Path(file_path).name}")
            
            # 读取图片
            image = cv2.imread(file_path)
            if image is None:
                print(f"❌ 无法读取图片: {file_path}")
                return {'text': '', 'confidence': 0.0}
            
            # OCR识别
            result = self._ocr_image(image, Path(file_path).name)
            
            print(f"✅ 图片OCR完成，识别 {len(result['text'])} 个字符")
            return result
            
        except Exception as e:
            print(f"❌ 图片OCR处理失败: {e}")
            return {'text': '', 'confidence': 0.0}
    
    def _ocr_image(self, image: np.ndarray, source_name: str = "") -> Dict[str, Any]:
        """
        对单个图片进行OCR识别
        
        Args:
            image: OpenCV图片数组
            source_name: 来源名称（用于日志）
            
        Returns:
            Dict[str, Any]: OCR结果
        """
        try:
            # 图片预处理
            processed_image = self._preprocess_image(image)
            
            # PaddleOCR识别
            ocr_results = self.ocr.ocr(processed_image, cls=True)
            
            if not ocr_results or not ocr_results[0]:
                return {'text': '', 'confidence': 0.0}
            
            # 解析结果
            text_lines = []
            confidences = []
            
            for line in ocr_results[0]:
                if line:
                    text = line[1][0]  # 识别的文字
                    confidence = line[1][1]  # 置信度
                    
                    text_lines.append(text)
                    confidences.append(confidence)
            
            # 合并文本
            full_text = '\n'.join(text_lines)
            avg_confidence = np.mean(confidences) if confidences else 0.0
            
            if source_name:
                print(f"      📝 {source_name}: 识别 {len(full_text)} 字符，平均置信度 {avg_confidence:.2f}")
            
            return {
                'text': full_text,
                'confidence': float(avg_confidence),
                'line_count': len(text_lines)
            }
            
        except Exception as e:
            print(f"❌ OCR识别失败 ({source_name}): {e}")
            return {'text': '', 'confidence': 0.0}
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图片预处理，提高OCR识别率
        
        Args:
            image: 原始图片
            
        Returns:
            np.ndarray: 预处理后的图片
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 去噪
        denoised = cv2.medianBlur(gray, 3)
        
        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return binary
    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]], 
                                     file_name: str) -> str:
        """
        将OCR结果格式化为Markdown
        
        Args:
            ocr_results: OCR结果列表
            file_name: 文件名
            
        Returns:
            str: Markdown格式的内容
        """
        markdown_lines = [f"# {Path(file_name).stem} (OCR识别结果)\n"]
        
        for result in ocr_results:
            if 'page' in result:
                # PDF页面结果
                markdown_lines.append(f"## 第{result['page']}页\n")
                markdown_lines.append(f"**识别置信度**: {result['confidence']:.2f}\n")
                markdown_lines.append(f"**字符数**: {result['char_count']}\n")
                markdown_lines.append(f"{result['ocr_text']}\n")
            else:
                # 单个图片结果
                markdown_lines.append(f"**识别置信度**: {result['confidence']:.2f}\n")
                markdown_lines.append(f"{result['text']}\n")
        
        return "\n".join(markdown_lines)
