#!/usr/bin/env python3
"""
OCR处理器
使用marker-pdf进行PDF文字识别和转换
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple

try:
    from marker import convert_pdf
    MARKER_AVAILABLE = True
except ImportError:
    MARKER_AVAILABLE = False


class OCRProcessor:
    """OCR处理器 - 使用marker-pdf"""

    def __init__(self, use_gpu: bool = True):
        """
        初始化OCR处理器

        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu

        if MARKER_AVAILABLE:
            print(f"✓ marker-pdf可用 (GPU: {use_gpu})")
        else:
            print("❌ marker-pdf未安装")
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return MARKER_AVAILABLE
    
    def extract_text_from_pdf_images(self, file_path: str) -> List[Dict[str, Any]]:
        """
        使用marker-pdf从PDF提取文字和结构

        Args:
            file_path: PDF文件路径

        Returns:
            List[Dict[str, Any]]: 处理结果
        """
        if not self.is_available():
            return []

        try:
            print(f"📄 开始使用marker-pdf处理: {Path(file_path).name}")
            start_time = time.time()

            # 使用marker-pdf转换PDF
            markdown_content, metadata = convert_pdf(file_path)

            processing_time = time.time() - start_time
            print(f"⏱️  marker-pdf处理耗时: {processing_time:.2f}秒")

            # 统计信息
            char_count = len(markdown_content)
            word_count = len(markdown_content.split())
            line_count = len(markdown_content.split('\n'))

            print(f"✅ marker-pdf处理完成:")
            print(f"   📝 字符数: {char_count}")
            print(f"   📝 单词数: {word_count}")
            print(f"   📝 行数: {line_count}")

            # 返回统一格式的结果
            return [{
                'page': 'all',
                'ocr_text': markdown_content,
                'confidence': 1.0,  # marker-pdf不提供置信度，设为1.0
                'word_count': word_count,
                'char_count': char_count,
                'line_count': line_count,
                'processing_time': processing_time,
                'metadata': metadata
            }]

        except Exception as e:
            print(f"❌ marker-pdf处理失败: {e}")
            return []
    
    def extract_text_from_image(self, file_path: str) -> Dict[str, Any]:
        """
        从图片文件提取文字（简化版，主要用于marker-pdf）

        Args:
            file_path: 图片文件路径

        Returns:
            Dict[str, Any]: OCR结果
        """
        print(f"⚠ 图片OCR功能已简化，建议将图片转换为PDF后使用marker-pdf处理")
        return {
            'text': f'图片文件: {Path(file_path).name}',
            'confidence': 0.0,
            'processing_method': 'placeholder'
        }
    

    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]],
                                     file_name: str) -> str:
        """
        将marker-pdf结果格式化为Markdown

        Args:
            ocr_results: marker-pdf结果列表
            file_name: 文件名

        Returns:
            str: Markdown格式的内容
        """
        if not ocr_results:
            return f"# {Path(file_name).stem}\n\n无法处理此文件。"

        # marker-pdf直接返回markdown内容
        result = ocr_results[0]
        markdown_content = result.get('ocr_text', '')

        # 添加处理信息
        header_lines = [
            f"# {Path(file_name).stem} (marker-pdf处理结果)\n",
            f"**处理时间**: {result.get('processing_time', 0):.2f}秒",
            f"**字符数**: {result.get('char_count', 0)}",
            f"**单词数**: {result.get('word_count', 0)}",
            f"**行数**: {result.get('line_count', 0)}\n",
            "---\n"
        ]

        return "\n".join(header_lines) + markdown_content
