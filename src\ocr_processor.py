"""
OCR处理模块
使用GPU加速的PaddleOCR处理扫描版PDF
"""

import os
import cv2
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from paddleocr import PaddleOCR
    import fitz  # PyMuPDF
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

from .text_converter import TextConverter
from .table_detector import TableDetector


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self, use_gpu: bool = True):
        """
        初始化OCR处理器
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        self.use_gpu = use_gpu
        self.ocr = None
        self.text_converter = TextConverter()

        # 初始化表格检测器
        try:
            self.table_detector = TableDetector()
            self.table_detection_enabled = True
            print("✓ 表格检测功能已启用")
        except ImportError as e:
            print(f"⚠ 表格检测功能不可用: {e}")
            self.table_detector = None
            self.table_detection_enabled = False
        
        if PADDLEOCR_AVAILABLE:
            try:
                print(f"🔧 初始化PaddleOCR (GPU: {use_gpu})...")

                # 设置GPU环境
                if use_gpu:
                    import paddle
                    if paddle.is_compiled_with_cuda():
                        paddle.device.set_device('gpu:0')
                        print(f"✅ 设置Paddle使用GPU:0")
                    else:
                        print(f"⚠ Paddle未编译CUDA支持，回退到CPU")
                        use_gpu = False
                        self.use_gpu = False

                # 初始化PaddleOCR（明确指定GPU参数）
                self.ocr = PaddleOCR(
                    use_gpu=use_gpu,
                    gpu_mem=2000,  # 增加GPU内存分配
                    lang='ch',
                    show_log=False
                )

                print(f"✅ PaddleOCR初始化成功 (GPU: {use_gpu})")

            except Exception as e:
                print(f"❌ PaddleOCR初始化失败: {e}")
                # 尝试CPU模式
                try:
                    print("🔄 尝试CPU模式...")
                    self.ocr = PaddleOCR(
                        use_gpu=False,
                        lang='ch',
                        show_log=False
                    )
                    self.use_gpu = False
                    print("✅ PaddleOCR CPU模式初始化成功")
                except Exception as e2:
                    print(f"❌ CPU模式也失败: {e2}")
                    self.ocr = None
        else:
            print("❌ PaddleOCR不可用，请安装: pip install paddleocr")
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        return self.ocr is not None
    
    def extract_text_from_pdf(self, file_path: Path, max_pages: int = None) -> List[Dict[str, Any]]:
        """
        从PDF中提取文本（使用OCR）
        
        Args:
            file_path: PDF文件路径
            max_pages: 最大处理页数（None表示处理所有页）
            
        Returns:
            List[Dict[str, Any]]: 提取的文本块列表
        """
        if not self.is_available():
            print("❌ OCR不可用")
            return []
        
        try:
            print(f"📄 开始OCR处理: {file_path.name}")
            
            # 打开PDF
            pdf_document = fitz.open(file_path)
            total_pages = len(pdf_document)
            
            if max_pages:
                total_pages = min(total_pages, max_pages)
            
            print(f"📄 PDF共 {len(pdf_document)} 页，处理前 {total_pages} 页")
            
            chunks = []
            chunk_id = 0
            
            for page_num in range(total_pages):
                print(f"🔄 OCR处理第 {page_num + 1}/{total_pages} 页...")
                
                try:
                    # 将PDF页面转换为图像
                    page = pdf_document[page_num]
                    
                    # 设置较高的分辨率以提高OCR精度
                    mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")
                    
                    # 转换为OpenCV格式
                    nparr = np.frombuffer(img_data, np.uint8)
                    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if img is None:
                        print(f"    ⚠ 第{page_num + 1}页图像转换失败")
                        continue
                    
                    # 执行OCR
                    ocr_result = self.ocr.ocr(img, cls=True)
                    
                    if not ocr_result or not ocr_result[0]:
                        print(f"    ⚠ 第{page_num + 1}页OCR无结果")
                        continue
                    
                    # 提取文本
                    page_text = self._extract_text_from_ocr_result(ocr_result[0])

                    if page_text.strip():
                        # 繁简转换
                        original_text = page_text
                        converted_text = self.text_converter.convert_traditional_to_simplified(page_text)

                        # 检查是否进行了转换
                        has_conversion = original_text != converted_text

                        chunks.append({
                            "content": converted_text,
                            "page": page_num + 1,
                            "source": str(file_path),
                            "type": "text",
                            "chunk_id": chunk_id,
                            "ocr_processed": True,
                            "traditional_converted": has_conversion,
                            "original_content": original_text if has_conversion else None
                        })
                        chunk_id += 1

                        conversion_info = "（已转换繁体）" if has_conversion else ""
                        print(f"    ✅ 第{page_num + 1}页OCR完成，提取 {len(converted_text)} 字符{conversion_info}")
                    else:
                        print(f"    ⚠ 第{page_num + 1}页无有效文本")
                
                except Exception as e:
                    print(f"    ❌ 第{page_num + 1}页OCR失败: {e}")
                    continue
            
            pdf_document.close()
            
            print(f"✅ OCR处理完成，共提取 {len(chunks)} 个文本块")
            return chunks
            
        except Exception as e:
            print(f"❌ OCR处理失败: {e}")
            return []
    
    def _extract_text_from_ocr_result(self, ocr_result: List) -> str:
        """
        从OCR结果中提取文本
        
        Args:
            ocr_result: PaddleOCR的识别结果
            
        Returns:
            str: 提取的文本
        """
        text_lines = []
        
        for line in ocr_result:
            if len(line) >= 2:
                # line[0] 是坐标信息，line[1] 是 (文本, 置信度)
                text_info = line[1]
                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                    text = text_info[0]
                    confidence = text_info[1]
                    
                    # 只保留置信度较高的文本
                    if confidence > 0.5 and text.strip():
                        text_lines.append(text.strip())
        
        return '\n'.join(text_lines)
    
    def extract_text_from_image(self, image_path: Path) -> str:
        """
        从单个图像中提取文本
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            str: 提取的文本
        """
        if not self.is_available():
            return ""
        
        try:
            print(f"🔄 OCR处理图像: {image_path.name}")
            
            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"❌ 无法读取图像: {image_path}")
                return ""
            
            # 执行OCR
            ocr_result = self.ocr.ocr(img, cls=True)
            
            if not ocr_result or not ocr_result[0]:
                print(f"⚠ 图像OCR无结果")
                return ""
            
            # 提取文本
            text = self._extract_text_from_ocr_result(ocr_result[0])

            # 繁简转换
            converted_text = self.text_converter.convert_traditional_to_simplified(text)
            has_conversion = text != converted_text

            conversion_info = "（已转换繁体）" if has_conversion else ""
            print(f"✅ 图像OCR完成，提取 {len(converted_text)} 字符{conversion_info}")
            return converted_text
            
        except Exception as e:
            print(f"❌ 图像OCR失败: {e}")
            return ""
    
    def test_gpu_availability(self) -> Dict[str, Any]:
        """
        测试GPU可用性
        
        Returns:
            Dict[str, Any]: GPU测试结果
        """
        result = {
            'gpu_available': False,
            'gpu_count': 0,
            'gpu_info': [],
            'paddle_gpu': False
        }
        
        try:
            # 检查CUDA
            import paddle
            
            if paddle.is_compiled_with_cuda():
                result['paddle_gpu'] = True
                
                # 获取GPU信息
                gpu_count = paddle.device.cuda.device_count()
                result['gpu_count'] = gpu_count
                
                if gpu_count > 0:
                    result['gpu_available'] = True
                    
                    for i in range(gpu_count):
                        gpu_name = paddle.device.cuda.get_device_name(i)
                        result['gpu_info'].append({
                            'id': i,
                            'name': gpu_name
                        })
            
        except Exception as e:
            result['error'] = str(e)
        
        return result

    def _ocr_region(self, page, bbox: tuple) -> str:
        """
        对页面指定区域进行OCR识别

        Args:
            page: PDF页面对象
            bbox: 区域边界框 (x0, y0, x1, y1)

        Returns:
            str: 识别的文本
        """
        try:
            # 裁剪指定区域
            rect = fitz.Rect(bbox)
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat, clip=rect)
            img_data = pix.tobytes("png")

            # 转换为OpenCV格式
            nparr = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                return ""

            # 执行OCR
            ocr_result = self.ocr.ocr(img, cls=True)

            if not ocr_result or not ocr_result[0]:
                return ""

            # 提取文本
            return self._extract_text_from_ocr_result(ocr_result[0])

        except Exception as e:
            print(f"⚠ 区域OCR失败: {e}")
            return ""

    def _ocr_full_page(self, page) -> str:
        """
        对整个页面进行OCR识别

        Args:
            page: PDF页面对象

        Returns:
            str: 识别的文本
        """
        try:
            # 设置较高的分辨率以提高OCR精度
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")

            # 转换为OpenCV格式
            nparr = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                return ""

            # 执行OCR
            ocr_result = self.ocr.ocr(img, cls=True)

            if not ocr_result or not ocr_result[0]:
                return ""

            # 提取文本
            return self._extract_text_from_ocr_result(ocr_result[0])

        except Exception as e:
            print(f"⚠ 整页OCR失败: {e}")
            return ""

    def extract_text_from_pdf_with_tables(self, file_path: Path, max_pages: int = None) -> List[Dict[str, Any]]:
        """
        从PDF中提取文本，支持表格检测和Markdown转换

        Args:
            file_path: PDF文件路径
            max_pages: 最大处理页数（None表示处理所有页）

        Returns:
            List[Dict[str, Any]]: 提取的文本块列表
        """
        if not self.is_available():
            print("❌ OCR不可用")
            return []

        try:
            print(f"📄 开始OCR处理: {file_path.name}")
            print(f"📊 表格检测: {'启用' if self.table_detection_enabled else '禁用'}")

            # 打开PDF
            pdf_document = fitz.open(file_path)
            total_pages = len(pdf_document)

            if max_pages:
                total_pages = min(total_pages, max_pages)

            print(f"📄 PDF共 {len(pdf_document)} 页，处理前 {total_pages} 页")

            chunks = []
            chunk_id = 0

            for page_num in range(total_pages):
                print(f"🔄 OCR处理第 {page_num + 1}/{total_pages} 页...")

                try:
                    page = pdf_document[page_num]

                    # 1. 表格检测（如果启用）
                    tables = []
                    if self.table_detection_enabled:
                        tables = self.table_detector.detect_tables_in_page(str(file_path), page_num)
                        if tables:
                            print(f"    📊 检测到 {len(tables)} 个表格")

                    # 2. 处理表格区域
                    table_chunks = []
                    for table in tables:
                        # 转换表格为Markdown
                        markdown_table = self.table_detector.convert_table_to_markdown(
                            table['data'], table['table_id']
                        )

                        if markdown_table.strip():
                            # 繁简转换
                            original_markdown = markdown_table
                            converted_markdown = self.text_converter.convert_traditional_to_simplified(markdown_table)
                            has_conversion = original_markdown != converted_markdown

                            table_chunks.append({
                                "content": converted_markdown,
                                "page": page_num + 1,
                                "source": str(file_path),
                                "type": "table",
                                "chunk_id": chunk_id,
                                "ocr_processed": True,
                                "traditional_converted": has_conversion,
                                "original_content": original_markdown if has_conversion else None,
                                "table_info": {
                                    "table_id": table['table_id'],
                                    "rows": table['rows'],
                                    "cols": table['cols'],
                                    "bbox": table['bbox']
                                }
                            })
                            chunk_id += 1

                            conversion_info = "（已转换繁体）" if has_conversion else ""
                            print(f"    ✅ 表格 {table['table_id']} 转换完成{conversion_info}")

                    # 3. 处理非表格区域（OCR识别）
                    text_content = ""
                    if self.table_detection_enabled and tables:
                        # 获取非表格区域
                        non_table_regions = self.table_detector.extract_non_table_regions(
                            str(file_path), page_num, tables
                        )

                        # 对每个非表格区域进行OCR
                        region_texts = []
                        for region_bbox in non_table_regions:
                            region_text = self._ocr_region(page, region_bbox)
                            if region_text.strip():
                                region_texts.append(region_text)

                        text_content = "\n\n".join(region_texts)
                    else:
                        # 整页OCR
                        text_content = self._ocr_full_page(page)

                    # 4. 处理文本内容
                    if text_content.strip():
                        # 繁简转换
                        original_text = text_content
                        converted_text = self.text_converter.convert_traditional_to_simplified(text_content)
                        has_conversion = original_text != converted_text

                        chunks.append({
                            "content": converted_text,
                            "page": page_num + 1,
                            "source": str(file_path),
                            "type": "text",
                            "chunk_id": chunk_id,
                            "ocr_processed": True,
                            "traditional_converted": has_conversion,
                            "original_content": original_text if has_conversion else None
                        })
                        chunk_id += 1

                    # 5. 添加表格片段到总列表
                    chunks.extend(table_chunks)

                    # 统计信息
                    total_chars = len(text_content) + sum(len(chunk['content']) for chunk in table_chunks)
                    conversion_info = f"（表格: {len(table_chunks)}, 文本: {len(text_content)} 字符）"
                    print(f"    ✅ 第{page_num + 1}页处理完成，提取 {total_chars} 字符{conversion_info}")

                except Exception as e:
                    print(f"    ❌ 第{page_num + 1}页处理失败: {e}")
                    continue

            pdf_document.close()

            print(f"✅ OCR处理完成，共生成 {len(chunks)} 个片段")
            return chunks

        except Exception as e:
            print(f"❌ OCR处理失败: {e}")
            return []
