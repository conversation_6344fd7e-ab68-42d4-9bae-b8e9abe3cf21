#!/usr/bin/env python3
"""
语义增强处理器
使用DeepSeek LLM将表格数据转换为语义丰富的自然语言描述
"""

import json
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path


class SemanticEnhancer:
    """语义增强处理器"""
    
    def __init__(self):
        """初始化语义增强处理器"""
        self.api_key = self._load_api_key()
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.model = "deepseek-chat"
        
        print("✓ DeepSeek语义增强处理器初始化成功")
    
    def _load_api_key(self) -> Optional[str]:
        """加载DeepSeek API密钥"""
        try:
            # 优先从统一配置文件加载
            try:
                import sys
                from pathlib import Path
                sys.path.insert(0, str(Path(__file__).parent.parent))
                from config.config import DEEPSEEK_API_KEY
                if DEEPSEEK_API_KEY:
                    return DEEPSEEK_API_KEY
            except ImportError:
                pass

            # 尝试从JSON配置文件加载
            config_file = Path("config/api_keys.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    api_key = config.get('deepseek_api_key')
                    if api_key and api_key != 'YOUR_DEEPSEEK_API_KEY_HERE':
                        return api_key

            # 尝试从环境变量加载
            import os
            return os.getenv('DEEPSEEK_API_KEY')

        except Exception as e:
            print(f"⚠ 无法加载DeepSeek API密钥: {e}")
            return None
    
    def is_available(self) -> bool:
        """检查DeepSeek API是否可用"""
        return self.api_key is not None
    
    def enhance_table_row(self, headers: List[str], row_data: List[str], 
                         table_context: Dict[str, Any]) -> List[str]:
        """
        使用DeepSeek LLM增强表格行的语义描述
        
        Args:
            headers: 表头列表
            row_data: 行数据列表
            table_context: 表格上下文信息
            
        Returns:
            List[str]: 语义增强后的描述列表
        """
        if not self.is_available():
            print("⚠ DeepSeek API不可用，使用基础描述")
            return self._generate_basic_descriptions(headers, row_data)
        
        try:
            # 构建提示词
            prompt = self._build_enhancement_prompt(headers, row_data, table_context)
            
            # 调用DeepSeek API
            enhanced_descriptions = self._call_deepseek_api(prompt)
            
            if enhanced_descriptions:
                return enhanced_descriptions
            else:
                print("⚠ DeepSeek API调用失败，使用基础描述")
                return self._generate_basic_descriptions(headers, row_data)
                
        except Exception as e:
            print(f"❌ 语义增强失败: {e}")
            return self._generate_basic_descriptions(headers, row_data)
    
    def _build_enhancement_prompt(self, headers: List[str], row_data: List[str],
                                table_context: Dict[str, Any]) -> str:
        """
        构建DeepSeek API的提示词 - 优化版，确保每行每列都有明确的主谓语关系

        Args:
            headers: 表头列表
            row_data: 行数据列表
            table_context: 表格上下文信息

        Returns:
            str: 构建的提示词
        """
        # 获取上下文信息
        source = table_context.get('source', '财务报告')
        page = table_context.get('page', '未知')
        table_id = table_context.get('table_id', '未知')

        # 构建详细的表格数据描述，包含每个单元格的位置信息
        table_structure = []
        main_item = row_data[0] if row_data else "未知项目"

        for i, (header, value) in enumerate(zip(headers, row_data)):
            if value and value.strip():
                table_structure.append({
                    'column_index': i,
                    'header': header,
                    'value': value,
                    'is_main_item': i == 0
                })

        # 构建结构化描述
        structure_desc = f"主要项目：{main_item}\n"
        for item in table_structure[1:]:  # 跳过主项目
            structure_desc += f"- {item['header']}：{item['value']}\n"

        prompt = f"""你是一个专业的财务数据分析师。请将以下表格数据转换为多个具有明确主谓语关系的完整句子，确保表格中的所有隐含信息都被明确表达出来。

**重要要求：每个句子必须有明确的主语、谓语和宾语，将表格的隐含关系显性化表达。**

**上下文信息：**
- 数据来源：{source}
- 页面：第{page}页
- 表格ID：{table_id}

**表格结构化数据：**
{structure_desc}

**语义增强要求：**
1. **主谓语明确**：每个句子都要有明确的主语（公司/项目）、谓语（是/达到/增长/下降）、宾语（具体数值/结果）
2. **覆盖每行每列**：确保表格中每个数据点都在某个句子中被明确表达
3. **隐含信息显性化**：将表格中的对比关系、增长关系、时间关系等隐含信息明确表达
4. **多角度描述**：从绝对数值、相对变化、同比分析、占比关系等多个角度描述
5. **语义完整性**：每个句子都是完整的、可独立理解的语义单元

**句子类型要求：**
- 绝对数值句：明确说明具体数值和单位
- 对比分析句：明确说明对比基准和变化幅度
- 时间关系句：明确说明时间期间和变化趋势
- 占比关系句：如果涉及占比，明确说明占比关系

**输出格式：**
请直接输出句子列表，每行一个句子，不需要编号。

**优化示例：**
贵州茅台酒股份有限公司在2025年第一季度的营业收入达到50,600,957,885.78元。
贵州茅台酒股份有限公司在2024年同期的营业收入为45,775,517,043.29元。
贵州茅台酒股份有限公司的营业收入从2024年同期的45,775,517,043.29元增长到2025年第一季度的50,600,957,885.78元。
贵州茅台酒股份有限公司2025年第一季度营业收入相比2024年同期增长了10.54%。
贵州茅台酒股份有限公司营业收入的同比增长率为10.54%，显示出积极的业务增长态势。

现在请处理上述表格数据，确保每个数据点都有明确的主谓语关系表达："""

        return prompt
    
    def _call_deepseek_api(self, prompt: str) -> Optional[List[str]]:
        """
        调用DeepSeek API
        
        Args:
            prompt: 提示词
            
        Returns:
            Optional[List[str]]: 增强后的描述列表
        """
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 解析返回的句子列表
                sentences = [line.strip() for line in content.split('\n') 
                           if line.strip() and not line.strip().startswith('**')]
                
                return sentences[:5]  # 最多返回5个句子
            else:
                print(f"❌ DeepSeek API调用失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ DeepSeek API调用异常: {e}")
            return None
    
    def _generate_basic_descriptions(self, headers: List[str], row_data: List[str]) -> List[str]:
        """
        生成基础描述（备用方案）
        
        Args:
            headers: 表头列表
            row_data: 行数据列表
            
        Returns:
            List[str]: 基础描述列表
        """
        descriptions = []
        
        if not row_data:
            return descriptions
        
        item_name = row_data[0] if row_data[0] else "未知项目"
        
        # 基础完整描述
        desc_parts = [f"项目: {item_name}"]
        for header, value in zip(headers[1:], row_data[1:]):
            if value and value.strip():
                desc_parts.append(f"{header}: {value}")
        
        if len(desc_parts) > 1:
            descriptions.append(", ".join(desc_parts))
        
        # 针对财务指标的特殊描述
        financial_keywords = ['营业收入', '净利润', '总资产', '股东权益', '每股收益']
        if any(keyword in item_name for keyword in financial_keywords):
            for i, (header, value) in enumerate(zip(headers[1:], row_data[1:]), 1):
                if value and value.strip():
                    descriptions.append(f"{item_name}在{header}为{value}")
        
        return descriptions
    
    def enhance_table_context(self, markdown_content: str, 
                            table_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强表格上下文信息
        
        Args:
            markdown_content: Markdown表格内容
            table_info: 原始表格信息
            
        Returns:
            Dict[str, Any]: 增强后的表格信息
        """
        enhanced_info = table_info.copy()
        
        # 添加语义标签
        enhanced_info['semantic_tags'] = self._extract_semantic_tags(markdown_content)
        
        # 添加表格摘要
        enhanced_info['table_summary'] = self._generate_table_summary(markdown_content, table_info)
        
        # 添加检索关键词
        enhanced_info['search_keywords'] = self._extract_search_keywords(markdown_content)
        
        return enhanced_info
    
    def _extract_semantic_tags(self, markdown_content: str) -> List[str]:
        """提取语义标签"""
        tags = []
        
        financial_terms = ['营业收入', '净利润', '总资产', '股东权益', '现金流', '每股收益']
        period_terms = ['本报告期', '上年同期', '同比', '环比', '增长率']
        
        for term in financial_terms + period_terms:
            if term in markdown_content:
                tags.append(term)
        
        return tags
    
    def _generate_table_summary(self, markdown_content: str, 
                              table_info: Dict[str, Any]) -> str:
        """生成表格摘要"""
        rows = table_info.get('rows', 0)
        cols = table_info.get('cols', 0)
        table_id = table_info.get('table_id', '未知')
        
        return f"表格{table_id}包含{rows}行{cols}列的财务数据，主要展示了相关财务指标的对比信息。"
    
    def _extract_search_keywords(self, markdown_content: str) -> List[str]:
        """提取检索关键词"""
        keywords = []
        
        # 提取数值
        import re
        numbers = re.findall(r'[\d,]+\.?\d*', markdown_content)
        keywords.extend(numbers[:5])  # 最多5个数值
        
        # 提取百分比
        percentages = re.findall(r'\d+\.?\d*%', markdown_content)
        keywords.extend(percentages)
        
        # 提取财务术语
        financial_terms = ['营业收入', '净利润', '总资产', '增长率', '同比']
        for term in financial_terms:
            if term in markdown_content:
                keywords.append(term)
        
        return list(set(keywords))  # 去重
