{"file_id": "小米集团2024年中期报告.pdf_aae2e350_20250719_201216", "saved_timestamp": "2025-07-19T20:12:19.331366", "total_chunks": 6, "chunks": [{"content": "**处理信息**: 3.00秒\n**字符数**: 1500\n**单词数**: 300\n**行数**: 50", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 1, "processing_method": "scan_processing", "content_type": "scan"}, {"content": "### 公司基本信息\n- 公司名称: 示例公司\n- 报告期间: 2024年中期\n- 报告类型: 中期财务报告", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 4, "processing_method": "scan_processing", "content_type": "scan"}, {"content": "### 主要财务数据\n| 项目 | 金额 (万元) | 同比变化 |\n|------|-------------|----------|\n| 营业收入 | 1,234,567 | +15.2% |\n| 净利润 | 234,567 | +12.8% |\n| 总资产 | 5,678,901 | +8.5% |", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 5, "processing_method": "scan_processing", "content_type": "scan"}, {"content": "**注意**: 这是简化OCR演示版本。\n实际部署时需要配置完整的OCR环境（如marker-pdf、PaddleOCR等）。", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 7, "processing_method": "scan_processing", "content_type": "scan"}, {"content": "### 技术说明\n- 当前版本: 简化演示版\n- 建议配置: marker-pdf 或 PaddleOCR\n- GPU加速: True\n- 处理方式: 模拟OCR识别", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 8, "processing_method": "scan_processing", "content_type": "scan"}, {"content": "### 下一步优化\n1. 配置完整的OCR环境\n2. 解决numpy版本冲突\n3. 启用GPU加速\n4. 提高识别准确率", "source": "data\\documents\\小米集团2024年中期报告.pdf", "type": "text", "chunk_id": 9, "processing_method": "scan_processing", "content_type": "scan"}]}