#!/usr/bin/env python3
"""
优化检索器
实现关键词筛选 + 向量检索 + 结果优化的三阶段检索策略
"""

import numpy as np
import re
from typing import List, Dict, Any, Optional, Tuple
from .logger import get_logger


class OptimizedRetriever:
    """优化检索器"""
    
    def __init__(self, vector_store, embedding_model):
        """
        初始化优化检索器
        
        Args:
            vector_store: 向量存储
            embedding_model: 嵌入模型
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.logger = get_logger()
        
        # 公司名称映射表
        self.company_mappings = {
            "伊利": "内蒙古伊利实业集团股份有限公司",
            "伊利集团": "内蒙古伊利实业集团股份有限公司",
            "伊利股份": "内蒙古伊利实业集团股份有限公司",
            "内蒙古伊利": "内蒙古伊利实业集团股份有限公司",

            "美的": "美的集团股份有限公司",
            "美的集团": "美的集团股份有限公司",

            "茅台": "贵州茅台酒股份有限公司",
            "贵州茅台": "贵州茅台酒股份有限公司",
            "茅台酒": "贵州茅台酒股份有限公司",

            "长江电力": "中国长江电力股份有限公司",
            "中国长江电力": "中国长江电力股份有限公司",
            "中国长江电力股份有限公司": "中国长江电力股份有限公司",
        }
        
        # 财务关键词权重
        self.financial_keywords = {
            "营业收入": 3.0,
            "净利润": 3.0,
            "总资产": 2.5,
            "净资产": 2.5,
            "现金流": 2.0,
            "毛利率": 2.0,
            "每股收益": 2.0,
            "资产负债率": 1.5,
        }
        
        # 时间关键词权重
        self.time_keywords = {
            "2025年": 2.0,
            "2024年": 1.5,
            "第一季度": 2.0,
            "第二季度": 1.5,
            "第三季度": 1.5,
            "第四季度": 1.5,
        }
    
    def optimized_search(self, query: str, company_name: Optional[str] = None, 
                        top_k: int = 5, use_rerank: bool = True) -> List[Dict[str, Any]]:
        """
        优化检索主方法
        
        Args:
            query: 查询内容
            company_name: 公司名称
            top_k: 返回结果数量
            use_rerank: 是否使用重排序
            
        Returns:
            List[Dict[str, Any]]: 检索结果
        """
        self.logger.info(f"开始优化检索: 查询='{query}', 公司='{company_name}', top_k={top_k}")
        
        # 第一阶段：关键词筛选document_info
        if company_name:
            filtered_indices = self._filter_by_document_info(company_name)
            if not filtered_indices:
                self.logger.warning(f"未找到公司 '{company_name}' 的文档")
                return []
            
            self.logger.info(f"第一阶段筛选: 根据document_info找到 {len(filtered_indices)} 个 '{company_name}' 的文档")
        else:
            filtered_indices = list(range(len(self.vector_store.metadata)))
            self.logger.info(f"未指定公司，使用所有 {len(filtered_indices)} 个文档")
        
        # 第二阶段：向量检索（增加候选数量）
        vector_results = self._vector_search_in_filtered_docs(query, filtered_indices, min(len(filtered_indices), top_k * 10))
        self.logger.info(f"第二阶段向量检索: 在筛选文档中找到 {len(vector_results)} 个候选结果")
        
        # 第三阶段：关键词优化排序
        optimized_results = self._optimize_with_keywords(query, vector_results, top_k)
        self.logger.info(f"第三阶段关键词优化: 最终返回 {len(optimized_results)} 个结果")
        
        # 重排序（如果启用）
        if use_rerank and len(optimized_results) > 1:
            optimized_results = self._rerank_results(query, optimized_results)
        
        return optimized_results[:top_k]
    
    def _filter_by_document_info(self, company_name: str) -> List[int]:
        """
        第一阶段：根据document_info字段筛选公司文档
        
        Args:
            company_name: 公司名称
            
        Returns:
            List[int]: 匹配的文档索引列表
        """
        # 获取完整公司名称
        full_company_name = self.company_mappings.get(company_name, company_name)
        
        filtered_indices = []
        
        for i, doc in enumerate(self.vector_store.metadata):
            document_info = doc.get("document_info", "")
            
            # 严格匹配document_info字段
            if (full_company_name in document_info or 
                company_name in document_info):
                filtered_indices.append(i)
        
        self.logger.debug(f"document_info筛选: '{company_name}' -> {len(filtered_indices)} 个文档")
        
        return filtered_indices
    
    def _vector_search_in_filtered_docs(self, query: str, filtered_indices: List[int], 
                                       limit: int) -> List[Dict[str, Any]]:
        """
        第二阶段：在筛选后的文档中进行向量检索
        
        Args:
            query: 查询内容
            filtered_indices: 筛选后的文档索引
            limit: 返回结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 向量检索结果
        """
        if not filtered_indices:
            return []
        
        # 向量化查询
        query_vectors = self.embedding_model.encode_batch([query])
        query_vector = np.array(query_vectors[0]).reshape(1, -1)
        
        # 获取所有向量检索结果
        all_results = self.vector_store.search(query_vector, limit=len(self.vector_store.metadata))
        
        # 筛选出在filtered_indices中的结果
        filtered_results = []
        for result in all_results:
            vector_id = result.get('vector_id', -1)
            if vector_id in filtered_indices:
                # 获取完整的文档信息
                doc = self.vector_store.metadata[vector_id].copy()
                doc['index'] = vector_id
                doc['vector_score'] = result.get('score', 0)
                doc['retrieval_method'] = 'optimized_vector'
                filtered_results.append(doc)
                
                if len(filtered_results) >= limit:
                    break
        
        self.logger.debug(f"向量检索筛选: 从 {len(all_results)} 个结果中筛选出 {len(filtered_results)} 个")
        
        return filtered_results
    
    def _optimize_with_keywords(self, query: str, vector_results: List[Dict[str, Any]], 
                               top_k: int) -> List[Dict[str, Any]]:
        """
        第三阶段：使用关键词优化排序
        
        Args:
            query: 查询内容
            vector_results: 向量检索结果
            top_k: 返回结果数量
            
        Returns:
            List[Dict[str, Any]]: 优化后的结果
        """
        optimized_results = []
        
        for result in vector_results:
            content = result.get('content', '')
            vector_score = result.get('vector_score', 0)
            
            # 计算关键词匹配分数
            keyword_score = self._calculate_keyword_score(query, content)
            
            # 组合分数：向量分数 + 关键词分数
            combined_score = vector_score + (keyword_score * 0.8)  # 关键词权重0.8
            
            result['keyword_score'] = keyword_score
            result['combined_score'] = combined_score
            result['score'] = combined_score  # 更新最终分数
            
            optimized_results.append(result)
        
        # 按组合分数排序
        optimized_results.sort(key=lambda x: x['combined_score'], reverse=True)
        
        self.logger.debug(f"关键词优化完成: 重新排序 {len(optimized_results)} 个结果")
        
        return optimized_results
    
    def _calculate_keyword_score(self, query: str, content: str) -> float:
        """
        计算关键词匹配分数
        
        Args:
            query: 查询内容
            content: 文档内容
            
        Returns:
            float: 关键词分数
        """
        score = 0.0
        query_lower = query.lower()
        content_lower = content.lower()
        
        # 财务关键词匹配
        for keyword, weight in self.financial_keywords.items():
            if keyword in query and keyword in content:
                score += weight
        
        # 时间关键词匹配
        for keyword, weight in self.time_keywords.items():
            if keyword in query and keyword in content:
                score += weight
        
        # 数值精确匹配（最高权重）
        query_numbers = re.findall(r'\d{1,3}(?:,\d{3})*(?:\.\d+)?', query)
        for number in query_numbers:
            if number in content:
                score += 5.0  # 数值匹配权重最高
        
        # 表格内容加分
        if '|' in content and ('---' in content or '项目' in content):
            score += 1.0
        
        # 货币单位加分
        currency_units = ['千元', '万元', '亿元', '元']
        for unit in currency_units:
            if unit in content:
                score += 0.5
                break
        
        return score
    
    def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        重排序结果
        
        Args:
            query: 查询内容
            results: 检索结果
            
        Returns:
            List[Dict[str, Any]]: 重排序后的结果
        """
        try:
            from .rerank_model import RerankModel
            rerank_model = RerankModel()
            
            # 准备重排序数据
            documents = []
            for result in results:
                documents.append({
                    'content': result.get('content', ''),
                    'source': result.get('source', ''),
                    'page': result.get('page', 0),
                    'type': result.get('type', 'text')
                })
            
            # 执行重排序
            reranked_docs = rerank_model.rerank(query, documents, top_k=len(results))
            
            # 更新分数
            reranked_results = []
            for i, doc in enumerate(reranked_docs):
                # 找到对应的原始结果
                for result in results:
                    if (result.get('content', '') == doc.get('content', '') and
                        result.get('source', '') == doc.get('source', '')):
                        result_copy = result.copy()
                        result_copy['rerank_score'] = doc.get('rerank_score', 0)
                        reranked_results.append(result_copy)
                        break
            
            self.logger.info(f"重排序完成: {len(reranked_results)} 个结果")
            return reranked_results
            
        except Exception as e:
            self.logger.warning(f"重排序失败: {e}")
            return results
    
    def get_company_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取公司文档统计信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 公司统计信息
        """
        company_stats = {}
        
        for doc in self.vector_store.metadata:
            document_info = doc.get("document_info", "")
            
            # 尝试匹配已知公司
            matched_company = None
            for short_name, full_name in self.company_mappings.items():
                if full_name in document_info:
                    matched_company = short_name
                    break
            
            if matched_company:
                if matched_company not in company_stats:
                    company_stats[matched_company] = {
                        'full_name': self.company_mappings[matched_company],
                        'document_count': 0,
                        'sample_documents': []
                    }
                
                company_stats[matched_company]['document_count'] += 1
                
                # 保存前3个文档作为样本
                if len(company_stats[matched_company]['sample_documents']) < 3:
                    company_stats[matched_company]['sample_documents'].append({
                        'chunk_id': doc.get('chunk_id', ''),
                        'content_preview': doc.get('content', '')[:100] + '...'
                    })
        
        return company_stats
