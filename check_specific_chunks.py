#!/usr/bin/env python3
"""
检查包含特定财务数据的chunks
"""

import json

def check_specific_chunks():
    """检查包含特定财务数据的chunks"""
    # 检查保存的chunks
    with open('parsing_results/final_chunks/内蒙古伊利实业集团股份有限公司 2025年第一季度报告_PDF_final_chunks.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f'总共 {data["total_chunks"]} 个片段')

    # 查找包含稀释每股收益的具体数值
    found_eps = False
    for i, chunk in enumerate(data['chunks']):
        content = chunk.get('content', '')
        if '0.77' in content and ('稀释每股收益' in content or '每股收益' in content):
            print(f'\n找到稀释每股收益chunk {i}:')
            print(content[:500] + '...' if len(content) > 500 else content)
            found_eps = True
            break

    if not found_eps:
        print('\n❌ 未找到包含稀释每股收益0.77的chunk')

    # 查找包含总资产的具体数值        
    found_assets = False
    for i, chunk in enumerate(data['chunks']):
        content = chunk.get('content', '')
        if '154,847,139,953.19' in content:
            print(f'\n找到总资产chunk {i}:')
            print(content[:500] + '...' if len(content) > 500 else content)
            found_assets = True
            break

    if not found_assets:
        print('\n❌ 未找到包含总资产154,847,139,953.19的chunk')

    # 查找包含表格数据的chunks
    print(f'\n查找包含主要财务数据表格的chunks:')
    for i, chunk in enumerate(data['chunks']):
        content = chunk.get('content', '')
        if '营业收入' in content and '32,938,299,808.43' in content:
            print(f'\nchunk {i} (包含营业收入表格):')
            print(content[:300] + '...' if len(content) > 300 else content)

if __name__ == "__main__":
    check_specific_chunks()
