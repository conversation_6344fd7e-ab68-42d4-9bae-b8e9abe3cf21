import os
from pathlib import Path

# 基础路径配置
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
CACHE_DIR = BASE_DIR / "cache"
FAISS_INDEX_PATH = BASE_DIR / "faiss_index"

# 嵌入模型配置
USE_LOCAL_EMBEDDING = False  
LOCAL_EMBEDDING_MODEL = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"  # 轻量级多语言模型
VECTOR_DIM = 1536  # 阿里云text-embedding-v1模型的向量维度

# 阿里云百炼API配置（备用）
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")
ALI_EMBEDDING_MODEL = "text-embedding-v2"  # 阿里云嵌入模型

# DeepSeek API配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_API_BASE = "https://api.deepseek.com/v1"

# 文本处理配置
MAX_LENGTH = 512
CHUNK_SIZE = 500
CHUNK_OVERLAP = 50

# 检索配置
TOP_K = 5  # 保证检索准确性
SCORE_THRESHOLD = 0.1  # 进一步降低阈值以获得更多结果

# 重排序配置
USE_RERANK = True   # 是否使用重排序
RERANK_TOP_K = 10   # 重排序前的候选数量
USE_LOCAL_RERANK = False  # 是否使用本地重排序模型
ALI_RERANK_MODEL = "gte-rerank-v2"  # 阿里云重排序模型

# OCR配置
USE_LOCAL_OCR = True  # True: 使用本地MinerU OCR, False: 使用Qwen-VL-Max API
QWEN_VL_API_KEY = os.getenv("DASHSCOPE_API_KEY")  # 通义千问VL API密钥（使用DASHSCOPE_API_KEY）
QWEN_VL_MODEL = "qwen-vl-max"  # 通义千问VL模型名称

# 支持的文件类型（移除图片支持）
SUPPORTED_EXTENSIONS = {
    "pdf": [".pdf"],
    "word": [".doc", ".docx"],
    "markdown": [".md", ".markdown"],
    "text": [".txt"]
}