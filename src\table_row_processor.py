#!/usr/bin/env python3
"""
表格行级处理器
将表格数据分解为行级向量，提高检索精度
"""

import re
from typing import List, Dict, Any, Tuple
from pathlib import Path
from .semantic_enhancer import SemanticEnhancer


class TableRowProcessor:
    """表格行级处理器"""
    
    def __init__(self):
        """初始化表格行级处理器"""
        self.financial_keywords = {
            '营业收入', '净利润', '归属于上市公司股东的净利润', '总资产', '股东权益',
            '基本每股收益', '稀释每股收益', '净资产收益率', '经营活动产生的现金流量净额',
            '营业成本', '销售费用', '管理费用', '财务费用', '研发费用', '所得税费用',
            '流动资产', '非流动资产', '流动负债', '非流动负债', '资本公积', '盈余公积'
        }

        self.period_keywords = {
            '本报告期', '上年同期', '本期', '上期', '期末', '期初', '年初至报告期末',
            '2025年', '2024年', '第一季度', '第二季度', '第三季度', '第四季度'
        }

        self.metric_keywords = {
            '增减变动幅度', '增长率', '同比增长', '环比增长', '变动率', '增减',
            '金额', '数量', '比例', '百分比', '%'
        }

        # 初始化语义增强器
        self.semantic_enhancer = SemanticEnhancer()

        print("✓ 表格行级处理器初始化成功（包含DeepSeek语义增强）")
    
    def process_table_chunk(self, table_chunk: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理表格片段，生成行级向量数据
        
        Args:
            table_chunk: 表格片段数据
            
        Returns:
            List[Dict[str, Any]]: 行级向量数据列表
        """
        try:
            content = table_chunk.get('content', '')
            table_info = table_chunk.get('table_info', {})
            
            if not content or table_chunk.get('type') != 'table':
                return []
            
            # 解析Markdown表格
            table_data = self._parse_markdown_table(content)
            if not table_data:
                return []
            
            # 生成行级向量数据
            row_chunks = self._generate_row_chunks(
                table_data, table_chunk, table_info
            )
            
            return row_chunks
            
        except Exception as e:
            print(f"❌ 表格行级处理失败: {e}")
            return []
    
    def _parse_markdown_table(self, markdown_content: str) -> List[List[str]]:
        """
        解析Markdown表格内容
        
        Args:
            markdown_content: Markdown表格内容
            
        Returns:
            List[List[str]]: 表格数据矩阵
        """
        try:
            lines = markdown_content.strip().split('\n')
            table_data = []
            
            for line in lines:
                line = line.strip()
                
                # 跳过表格标题行和分隔符行
                if line.startswith('**表格') or line.startswith('|:') or not line:
                    continue
                
                # 解析表格行
                if line.startswith('|') and line.endswith('|'):
                    # 移除首尾的 |，然后分割
                    cells = [cell.strip() for cell in line[1:-1].split('|')]
                    if cells and any(cell for cell in cells):  # 确保不是空行
                        table_data.append(cells)
            
            return table_data
            
        except Exception as e:
            print(f"❌ Markdown表格解析失败: {e}")
            return []
    
    def _generate_row_chunks(self, table_data: List[List[str]], 
                           original_chunk: Dict[str, Any], 
                           table_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成行级向量数据
        
        Args:
            table_data: 表格数据矩阵
            original_chunk: 原始表格片段
            table_info: 表格信息
            
        Returns:
            List[Dict[str, Any]]: 行级向量数据列表
        """
        row_chunks = []
        
        if not table_data:
            return row_chunks
        
        try:
            # 假设第一行是表头
            headers = table_data[0] if table_data else []
            
            # 处理数据行
            for row_idx, row_data in enumerate(table_data[1:], 1):
                # 确保行数据长度与表头匹配
                while len(row_data) < len(headers):
                    row_data.append('')
                
                # 使用DeepSeek语义增强生成行级文本描述
                table_context = {
                    'source': original_chunk.get('source', ''),
                    'page': original_chunk.get('page', ''),
                    'table_id': table_info.get('table_id', ''),
                    'markdown_content': original_chunk.get('content', '')
                }

                enhanced_descriptions = self.semantic_enhancer.enhance_table_row(
                    headers, row_data, table_context
                )

                # 同时生成基础描述作为备用
                basic_descriptions = self._generate_row_descriptions(headers, row_data)

                # 合并描述（优先使用增强描述）
                all_descriptions = enhanced_descriptions + basic_descriptions

                for desc_idx, description in enumerate(all_descriptions):
                    if description.strip():
                        # 判断是否为增强描述
                        is_enhanced = desc_idx < len(enhanced_descriptions)

                        # 增强表格信息
                        enhanced_table_info = self.semantic_enhancer.enhance_table_context(
                            original_chunk.get('content', ''), table_info
                        ) if is_enhanced else table_info

                        # 创建行级片段
                        row_chunk = {
                            "content": description,
                            "page": original_chunk.get('page'),
                            "source": original_chunk.get('source'),
                            "type": "table_row_enhanced" if is_enhanced else "table_row",
                            "chunk_id": f"{original_chunk.get('chunk_id', 0)}_row_{row_idx}_{desc_idx}",
                            "table_processed": True,
                            "semantic_enhanced": is_enhanced,
                            "traditional_converted": original_chunk.get('traditional_converted', False),
                            "original_content": original_chunk.get('original_content'),
                            "markdown_table": original_chunk.get('content', ''),  # 保存完整Markdown
                            "table_info": {
                                **enhanced_table_info,
                                "row_index": row_idx,
                                "description_index": desc_idx,
                                "headers": headers,
                                "row_data": row_data,
                                "enhancement_type": "deepseek_llm" if is_enhanced else "basic"
                            },
                            "financial_relevance": self._calculate_financial_relevance(description),
                            "parent_table_id": table_info.get('table_id', 'unknown')
                        }

                        row_chunks.append(row_chunk)
            
            return row_chunks
            
        except Exception as e:
            print(f"❌ 行级数据生成失败: {e}")
            return []
    
    def _generate_row_descriptions(self, headers: List[str], row_data: List[str]) -> List[str]:
        """
        生成行的多种描述方式 - 优化版，确保每行每列都被覆盖

        Args:
            headers: 表头列表
            row_data: 行数据列表

        Returns:
            List[str]: 行描述列表
        """
        descriptions = []

        try:
            if not row_data:
                return descriptions

            item_name = row_data[0] if row_data[0] else "未知项目"

            # 1. 完整行描述（确保所有列都被包含）
            full_desc_parts = [f"项目: {item_name}"]
            for i, (header, value) in enumerate(zip(headers[1:], row_data[1:]), 1):
                if value and value.strip():
                    full_desc_parts.append(f"{header}: {value}")

            if len(full_desc_parts) > 1:
                descriptions.append(", ".join(full_desc_parts))

            # 2. 每列单独描述（确保每列都有独立的主谓语句子）
            column_descriptions = self._generate_column_descriptions(item_name, headers, row_data)
            descriptions.extend(column_descriptions)

            # 3. 财务指标专用描述
            if self._is_financial_item(item_name):
                financial_descs = self._generate_financial_descriptions(
                    item_name, headers, row_data
                )
                descriptions.extend(financial_descs)

            # 4. 数值对比描述
            numeric_descs = self._generate_numeric_descriptions(
                item_name, headers, row_data
            )
            descriptions.extend(numeric_descs)

            # 5. 关系描述（显性化隐含关系）
            relationship_descs = self._generate_relationship_descriptions(
                item_name, headers, row_data
            )
            descriptions.extend(relationship_descs)

            return descriptions

        except Exception as e:
            print(f"❌ 行描述生成失败: {e}")
            return []

    def _generate_column_descriptions(self, item_name: str, headers: List[str],
                                    row_data: List[str]) -> List[str]:
        """
        为每列生成独立的主谓语描述，确保每列都被覆盖

        Args:
            item_name: 项目名称
            headers: 表头列表
            row_data: 行数据列表

        Returns:
            List[str]: 每列的独立描述
        """
        descriptions = []

        try:
            # 为每个非空列生成独立的主谓语句子
            for i, (header, value) in enumerate(zip(headers[1:], row_data[1:]), 1):
                if value and value.strip():
                    # 基本主谓语句子
                    descriptions.append(f"{item_name}的{header}是{value}")
                    descriptions.append(f"{item_name}在{header}方面的数值为{value}")

                    # 如果是数值，添加更多描述
                    if self._is_numeric_value(value):
                        descriptions.append(f"{item_name}的{header}达到{value}")

                        # 如果包含货币单位
                        if any(unit in value for unit in ['元', '亿', '万', '千']):
                            descriptions.append(f"{item_name}的{header}金额为{value}")

                        # 如果包含百分比
                        if '%' in value or '％' in value:
                            descriptions.append(f"{item_name}的{header}比率为{value}")
                            descriptions.append(f"{item_name}在{header}指标上表现为{value}")

            return descriptions

        except Exception as e:
            print(f"❌ 列描述生成失败: {e}")
            return []

    def _generate_relationship_descriptions(self, item_name: str, headers: List[str],
                                          row_data: List[str]) -> List[str]:
        """
        生成关系描述，显性化表格中的隐含关系

        Args:
            item_name: 项目名称
            headers: 表头列表
            row_data: 行数据列表

        Returns:
            List[str]: 关系描述列表
        """
        descriptions = []

        try:
            # 查找时间对比关系
            current_period = None
            previous_period = None
            growth_rate = None

            for i, (header, value) in enumerate(zip(headers, row_data)):
                if not value or not value.strip():
                    continue

                header_lower = header.lower()

                # 识别当期数据
                if any(keyword in header for keyword in ['本报告期', '本期', '2025年', '当期']):
                    current_period = {'header': header, 'value': value, 'index': i}

                # 识别上期数据
                elif any(keyword in header for keyword in ['上年同期', '上期', '2024年', '去年同期']):
                    previous_period = {'header': header, 'value': value, 'index': i}

                # 识别增长率
                elif any(keyword in header for keyword in ['增减变动幅度', '增长率', '变动率', '%']):
                    growth_rate = {'header': header, 'value': value, 'index': i}

            # 生成时间对比关系描述
            if current_period and previous_period:
                descriptions.append(
                    f"{item_name}从{previous_period['header']}的{previous_period['value']}变化到{current_period['header']}的{current_period['value']}"
                )
                descriptions.append(
                    f"{item_name}在{current_period['header']}为{current_period['value']}，而在{previous_period['header']}为{previous_period['value']}"
                )

            # 生成增长关系描述
            if current_period and growth_rate:
                descriptions.append(
                    f"{item_name}在{current_period['header']}的数值为{current_period['value']}，增长幅度为{growth_rate['value']}"
                )

            if previous_period and growth_rate:
                descriptions.append(
                    f"{item_name}相比{previous_period['header']}的{previous_period['value']}，增长了{growth_rate['value']}"
                )

            # 生成完整的三元关系描述
            if current_period and previous_period and growth_rate:
                descriptions.append(
                    f"{item_name}从{previous_period['header']}的{previous_period['value']}增长到{current_period['header']}的{current_period['value']}，增长幅度为{growth_rate['value']}"
                )

            return descriptions

        except Exception as e:
            print(f"❌ 关系描述生成失败: {e}")
            return []
    
    def _generate_financial_descriptions(self, item_name: str, 
                                       headers: List[str], 
                                       row_data: List[str]) -> List[str]:
        """
        生成财务指标专用描述
        
        Args:
            item_name: 项目名称
            headers: 表头列表
            row_data: 行数据列表
            
        Returns:
            List[str]: 财务描述列表
        """
        descriptions = []
        
        try:
            # 查找关键财务数据列
            for i, (header, value) in enumerate(zip(headers, row_data)):
                if not value or not value.strip():
                    continue
                
                # 当期数据描述
                if any(keyword in header for keyword in ['本报告期', '本期', '2025年']):
                    descriptions.append(f"{item_name}在{header}为{value}")
                
                # 同期数据描述
                if any(keyword in header for keyword in ['上年同期', '上期', '2024年']):
                    descriptions.append(f"{item_name}在{header}为{value}")
                
                # 增长率描述
                if any(keyword in header for keyword in ['增减变动幅度', '增长率', '%']):
                    descriptions.append(f"{item_name}的{header}为{value}")
                    descriptions.append(f"{item_name}增长率{value}")
                    descriptions.append(f"{item_name}同比变动{value}")
            
            return descriptions
            
        except Exception as e:
            print(f"❌ 财务描述生成失败: {e}")
            return []
    
    def _generate_numeric_descriptions(self, item_name: str, 
                                     headers: List[str], 
                                     row_data: List[str]) -> List[str]:
        """
        生成数值对比描述
        
        Args:
            item_name: 项目名称
            headers: 表头列表
            row_data: 行数据列表
            
        Returns:
            List[str]: 数值描述列表
        """
        descriptions = []
        
        try:
            # 提取数值数据
            numeric_data = []
            for header, value in zip(headers, row_data):
                if value and self._is_numeric_value(value):
                    numeric_data.append((header, value))
            
            # 生成数值对比描述
            if len(numeric_data) >= 2:
                current_period = None
                previous_period = None
                
                for header, value in numeric_data:
                    if any(keyword in header for keyword in ['本报告期', '本期', '2025年']):
                        current_period = (header, value)
                    elif any(keyword in header for keyword in ['上年同期', '上期', '2024年']):
                        previous_period = (header, value)
                
                if current_period and previous_period:
                    descriptions.append(
                        f"{item_name}: {current_period[0]}{current_period[1]}, "
                        f"{previous_period[0]}{previous_period[1]}"
                    )
            
            return descriptions
            
        except Exception as e:
            print(f"❌ 数值描述生成失败: {e}")
            return []
    
    def _is_financial_item(self, item_name: str) -> bool:
        """判断是否为财务指标"""
        return any(keyword in item_name for keyword in self.financial_keywords)
    
    def _is_numeric_value(self, value: str) -> bool:
        """判断是否为数值"""
        if not value:
            return False
        
        # 移除常见的数值格式字符
        clean_value = re.sub(r'[,，\s%％元万亿]', '', value)
        
        try:
            float(clean_value)
            return True
        except ValueError:
            return False
    
    def _calculate_financial_relevance(self, description: str) -> float:
        """
        计算财务相关性得分
        
        Args:
            description: 描述文本
            
        Returns:
            float: 相关性得分 (0-1)
        """
        score = 0.0
        
        # 财务关键词权重
        financial_matches = sum(1 for keyword in self.financial_keywords 
                              if keyword in description)
        score += financial_matches * 0.3
        
        # 时期关键词权重
        period_matches = sum(1 for keyword in self.period_keywords 
                           if keyword in description)
        score += period_matches * 0.2
        
        # 指标关键词权重
        metric_matches = sum(1 for keyword in self.metric_keywords 
                           if keyword in description)
        score += metric_matches * 0.2
        
        # 数值存在权重
        if re.search(r'\d+', description):
            score += 0.3
        
        return min(score, 1.0)
