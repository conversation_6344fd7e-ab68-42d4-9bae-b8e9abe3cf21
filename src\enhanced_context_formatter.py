#!/usr/bin/env python3
"""
增强的上下文格式化器
优化发送给LLM的内容质量，提升检索准确性
"""

import re
from typing import List, Dict, Any, Optional
from .logger import get_logger


class EnhancedContextFormatter:
    """增强的上下文格式化器"""
    
    def __init__(self):
        self.logger = get_logger()
        
        # 定义需要过滤的无用内容模式
        self.noise_patterns = [
            r'本公司及董事会全体成员保证.*?大遗漏。',
            r'董事会、监事会及董事.*?法律责任。',
            r'法定代表人、主管会计工作负责人.*?完整。',
            r'第一季度报告是否经过审计.*?否',
            r'重要内容提示：',
            r'□\s*是\s*√\s*否',
            r'√\s*是\s*□\s*否',
            r'本报告.*?中文版本为准',
            r'特此公告',
            r'董事长：.*?\d{4}年\d{1,2}月\d{1,2}日'
        ]
        
        # 定义重要信息的关键词
        self.important_keywords = [
            '营业收入', '净利润', '总资产', '净资产', '每股收益',
            '毛利率', '净利率', '资产负债率', '现金流',
            '研发费用', '销售费用', '管理费用',
            '同比增长', '环比增长', '增长率',
            '主营业务', '业务收入', '分部收入'
        ]
    
    def clean_chunk_content(self, content: str) -> str:
        """
        清理chunk内容，移除无用信息
        
        Args:
            content: 原始内容
            
        Returns:
            str: 清理后的内容
        """
        cleaned_content = content
        
        # 移除重复的公司名称
        lines = content.split('\n')
        seen_lines = set()
        unique_lines = []
        
        for line in lines:
            line = line.strip()
            if line and line not in seen_lines:
                # 跳过明显重复的公司名称行
                if not self._is_repetitive_company_name(line, seen_lines):
                    unique_lines.append(line)
                    seen_lines.add(line)
        
        cleaned_content = '\n'.join(unique_lines)
        
        # 移除标准声明和无用模式
        for pattern in self.noise_patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.DOTALL | re.IGNORECASE)
        
        # 清理多余的空行和空格
        cleaned_content = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_content)
        cleaned_content = cleaned_content.strip()
        
        return cleaned_content
    
    def _is_repetitive_company_name(self, line: str, seen_lines: set) -> bool:
        """检查是否是重复的公司名称行"""
        # 如果行只包含公司名称且已经出现过类似的
        if '股份有限公司' in line or '集团' in line:
            for seen_line in seen_lines:
                if self._lines_similar(line, seen_line):
                    return True
        return False
    
    def _lines_similar(self, line1: str, line2: str) -> bool:
        """检查两行是否相似"""
        # 简单的相似度检查
        words1 = set(line1.split())
        words2 = set(line2.split())
        
        if not words1 or not words2:
            return False
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        similarity = len(intersection) / len(union)
        return similarity > 0.8
    
    def extract_key_information(self, content: str) -> Dict[str, List[str]]:
        """
        从内容中提取关键信息
        
        Args:
            content: 文档内容
            
        Returns:
            Dict[str, List[str]]: 按类别分组的关键信息
        """
        key_info = {
            'financial_data': [],
            'business_data': [],
            'growth_data': [],
            'other_important': []
        }
        
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否包含财务数据
            if any(keyword in line for keyword in ['营业收入', '净利润', '总资产', '净资产', '每股收益']):
                key_info['financial_data'].append(line)
            
            # 检查是否包含业务数据
            elif any(keyword in line for keyword in ['主营业务', '业务收入', '分部收入', '产品销售']):
                key_info['business_data'].append(line)
            
            # 检查是否包含增长数据
            elif any(keyword in line for keyword in ['同比', '环比', '增长', '下降', '变动']):
                key_info['growth_data'].append(line)
            
            # 检查其他重要信息
            elif any(keyword in line for keyword in self.important_keywords):
                key_info['other_important'].append(line)
        
        return key_info
    
    def format_enhanced_context(self, documents: List[Dict[str, Any]], max_context_length: int = 4000) -> str:
        """
        格式化增强的上下文
        
        Args:
            documents: 检索到的文档列表
            max_context_length: 最大上下文长度
            
        Returns:
            str: 格式化后的上下文
        """
        if not documents:
            return "未找到相关信息。"
        
        context_parts = []
        current_length = 0
        
        self.logger.info(f"开始格式化上下文，文档数量: {len(documents)}")
        
        for i, doc in enumerate(documents, 1):
            if current_length >= max_context_length:
                self.logger.info(f"达到最大上下文长度限制，停止添加文档")
                break
            
            # 获取基本信息
            source = doc.get("source", "未知来源")
            page = doc.get("page", 0)
            content = doc.get("content", "")
            doc_type = doc.get("type", "text")
            score = doc.get("rerank_score", doc.get("score", 0))
            
            # 清理内容
            cleaned_content = self.clean_chunk_content(content)
            
            if not cleaned_content or len(cleaned_content) < 20:
                self.logger.debug(f"跳过内容过短的文档 {i}")
                continue
            
            # 提取关键信息
            key_info = self.extract_key_information(cleaned_content)
            
            # 构建文档部分
            doc_part = f"[文档 {i}] 来源: {source}"
            if page > 0:
                doc_part += f", 页码: {page}"
            doc_part += f", 相关性: {score:.3f}\n"
            
            # 根据文档类型格式化内容
            if doc_type == "table":
                doc_part += f"📊 表格数据:\n{cleaned_content}\n"
            else:
                # 优先显示关键信息
                if any(key_info.values()):
                    doc_part += "🔍 关键信息:\n"
                    
                    if key_info['financial_data']:
                        doc_part += "💰 财务数据:\n"
                        for item in key_info['financial_data'][:3]:  # 最多3条
                            doc_part += f"  • {item}\n"
                    
                    if key_info['business_data']:
                        doc_part += "🏢 业务数据:\n"
                        for item in key_info['business_data'][:3]:
                            doc_part += f"  • {item}\n"
                    
                    if key_info['growth_data']:
                        doc_part += "📈 增长数据:\n"
                        for item in key_info['growth_data'][:2]:
                            doc_part += f"  • {item}\n"
                    
                    # 如果关键信息不足，补充原始内容
                    total_key_items = sum(len(items) for items in key_info.values())
                    if total_key_items < 3:
                        doc_part += f"📄 补充内容:\n{cleaned_content[:300]}...\n"
                else:
                    # 没有关键信息时，使用清理后的内容
                    doc_part += f"📄 内容:\n{cleaned_content[:400]}...\n"
            
            # 检查长度限制
            if current_length + len(doc_part) > max_context_length:
                # 截断当前文档以适应长度限制
                remaining_length = max_context_length - current_length
                if remaining_length > 100:  # 至少保留100字符
                    doc_part = doc_part[:remaining_length] + "...\n"
                    context_parts.append(doc_part)
                break
            
            context_parts.append(doc_part)
            current_length += len(doc_part)
        
        final_context = "\n".join(context_parts)
        
        self.logger.info(f"上下文格式化完成，最终长度: {len(final_context)} 字符")
        
        return final_context
    
    def format_simple_context(self, documents: List[Dict[str, Any]]) -> str:
        """
        简单格式化上下文（向后兼容）
        
        Args:
            documents: 文档列表
            
        Returns:
            str: 格式化后的上下文
        """
        context_parts = []
        for i, doc in enumerate(documents, 1):
            source = doc.get("source", "未知")
            page = doc.get("page", 0)
            content = doc.get("content", "")
            doc_type = doc.get("type", "text")
            
            if doc_type == "table":
                content = f"表格内容：{content}"
            
            context_parts.append(
                f"[文档 {i}] 来源: {source}, 页码: {page}\n"
                f"内容: {content}\n"
            )
        
        return "\n".join(context_parts)
