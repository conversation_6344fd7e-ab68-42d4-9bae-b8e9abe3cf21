#!/usr/bin/env python3
"""
配置管理工具
用于管理OCR模式和其他配置选项
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.config import USE_LOCAL_OCR, QWEN_VL_API_KEY


def show_current_config():
    """显示当前配置"""
    print("🔧 当前配置状态")
    print("=" * 40)
    
    # OCR配置
    ocr_mode = "本地MinerU" if USE_LOCAL_OCR else "Qwen-VL-Max API"
    print(f"📄 OCR模式: {ocr_mode}")
    
    # API密钥状态
    qwen_key_status = "已设置" if QWEN_VL_API_KEY else "未设置"
    print(f"🔑 Qwen API密钥: {qwen_key_status}")

    # 环境变量检查
    print(f"\n🌍 环境变量状态:")
    env_vars = [
        ("DASHSCOPE_API_KEY", "阿里云百炼API（也用于Qwen-VL）"),
        ("DEEPSEEK_API_KEY", "DeepSeek API")
    ]
    
    for var_name, description in env_vars:
        status = "✅ 已设置" if os.getenv(var_name) else "❌ 未设置"
        print(f"  {description}: {status}")


def set_ocr_mode():
    """设置OCR模式"""
    print("🔧 设置OCR模式")
    print("=" * 40)
    print("1. 本地MinerU OCR (需要安装MinerU)")
    print("2. Qwen-VL-Max API (需要API密钥)")
    
    choice = input("\n请选择OCR模式 (1/2): ").strip()
    
    if choice == "1":
        use_local = True
        mode_name = "本地MinerU"
    elif choice == "2":
        use_local = False
        mode_name = "Qwen-VL-Max API"
        
        # 检查API密钥
        if not QWEN_VL_API_KEY:
            print("⚠️  警告: 未设置DASHSCOPE_API_KEY环境变量")
            print("请设置环境变量: set DASHSCOPE_API_KEY=your_api_key")
            return
    else:
        print("❌ 无效选择")
        return
    
    # 更新配置文件
    config_file = Path(__file__).parent.parent / "config" / "config.py"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换USE_LOCAL_OCR的值
        if use_local:
            new_content = content.replace(
                "USE_LOCAL_OCR = False", 
                "USE_LOCAL_OCR = True"
            ).replace(
                "USE_LOCAL_OCR = True", 
                "USE_LOCAL_OCR = True"
            )
        else:
            new_content = content.replace(
                "USE_LOCAL_OCR = True", 
                "USE_LOCAL_OCR = False"
            ).replace(
                "USE_LOCAL_OCR = False", 
                "USE_LOCAL_OCR = False"
            )
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ OCR模式已设置为: {mode_name}")
        print("💡 重启应用程序以使配置生效")
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项")
    print("=" * 40)
    
    # 检查MinerU
    try:
        import subprocess
        result = subprocess.run(['mineru', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ MinerU: 已安装并可用")
        else:
            print("❌ MinerU: 已安装但不可用")
    except Exception:
        print("❌ MinerU: 未安装或不可用")
        print("   安装命令: pip install mineru")
    
    # 检查其他关键依赖
    dependencies = [
        ("faiss-cpu", "FAISS向量搜索"),
        ("requests", "HTTP请求"),
        ("numpy", "数值计算"),
        ("pandas", "数据处理")
    ]
    
    for package, description in dependencies:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {description}: 已安装")
        except ImportError:
            print(f"❌ {description}: 未安装")
            print(f"   安装命令: pip install {package}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🔧 MyRAG配置管理工具")
        print("=" * 40)
        print("用法:")
        print("  python tools/config_manager.py show     - 显示当前配置")
        print("  python tools/config_manager.py set-ocr  - 设置OCR模式")
        print("  python tools/config_manager.py check    - 检查依赖项")
        return
    
    command = sys.argv[1].lower()
    
    if command == "show":
        show_current_config()
    elif command == "set-ocr":
        set_ocr_mode()
    elif command == "check":
        check_dependencies()
    else:
        print(f"❌ 未知命令: {command}")
        print("可用命令: show, set-ocr, check")


if __name__ == "__main__":
    main()
