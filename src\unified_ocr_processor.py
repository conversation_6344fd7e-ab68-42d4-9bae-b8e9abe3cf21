#!/usr/bin/env python3
"""
统一OCR处理器
支持本地MinerU OCR和Qwen-VL-Max API两种模式
"""

import os
import json
import time
import base64
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests

from config.config import USE_LOCAL_OCR, QWEN_VL_API_KEY, QWEN_VL_MODEL


class UnifiedOCRProcessor:
    """统一OCR处理器"""
    
    def __init__(self, use_local: Optional[bool] = None):
        """
        初始化OCR处理器
        
        Args:
            use_local: 是否使用本地OCR，None时使用配置文件设置
        """
        self.use_local = use_local if use_local is not None else USE_LOCAL_OCR
        self.qwen_api_key = QWEN_VL_API_KEY
        self.qwen_model = QWEN_VL_MODEL
        
        print(f"🔧 OCR处理器初始化: {'本地MinerU' if self.use_local else 'Qwen-VL-Max API'}")
        
        if not self.use_local and not self.qwen_api_key:
            print("⚠️  警告: 未设置DASHSCOPE_API_KEY，将回退到本地OCR")
            self.use_local = True
    
    def is_available(self) -> bool:
        """检查OCR是否可用"""
        if self.use_local:
            return self._check_mineru_available()
        else:
            return self._check_qwen_api_available()
    
    def _check_mineru_available(self) -> bool:
        """检查MinerU是否可用"""
        try:
            result = subprocess.run(['mineru', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False
    
    def _check_qwen_api_available(self) -> bool:
        """检查Qwen API是否可用"""
        return bool(self.qwen_api_key)
    
    def process_document(self, file_path: str) -> List[Dict[str, Any]]:
        """
        处理文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            List[Dict[str, Any]]: OCR结果列表
        """
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return []
        
        if self.use_local:
            return self._process_with_mineru(file_path)
        else:
            return self._process_with_qwen_api(file_path)
    
    def _process_with_mineru(self, file_path: str) -> List[Dict[str, Any]]:
        """使用MinerU处理文档"""
        if not self.is_available():
            print("❌ MinerU不可用")
            return []
        
        try:
            print(f"🔍 开始MinerU处理: {Path(file_path).name}")
            start_time = time.time()
            
            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_dir = Path(temp_dir) / "mineru_output"
                output_dir.mkdir(exist_ok=True)
                
                # 构建MinerU命令
                abs_file_path = os.path.abspath(file_path)
                cmd = [
                    'mineru',
                    '-p', abs_file_path,
                    '-o', str(output_dir)
                ]
                
                # 执行MinerU命令
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=1800,  # 30分钟超时
                    cwd=temp_dir
                )
                
                processing_time = time.time() - start_time
                
                if result.returncode != 0:
                    print(f"❌ MinerU处理失败: {result.stderr}")
                    return []
                
                # 查找输出文件
                auto_dir = output_dir / Path(file_path).stem / "auto"
                if auto_dir.exists():
                    markdown_files = list(auto_dir.glob("*.md"))
                    json_files = list(auto_dir.glob("*_content_list.json"))
                else:
                    markdown_files = list(output_dir.glob("**/*.md"))
                    json_files = list(output_dir.glob("**/*.json"))
                
                if not markdown_files:
                    print("❌ 未找到MinerU输出文件")
                    return []
                
                # 读取输出文件
                with open(markdown_files[0], 'r', encoding='utf-8') as f:
                    markdown_content = f.read()
                
                # 读取内容列表（如果存在）
                content_list = []
                if json_files:
                    try:
                        with open(json_files[0], 'r', encoding='utf-8') as f:
                            content_list = json.load(f)
                    except Exception as e:
                        print(f"⚠️  读取内容列表失败: {e}")
                
                print(f"✅ MinerU处理完成 ({processing_time:.2f}秒)")
                
                return [{
                    'ocr_text': markdown_content,
                    'confidence': 0.95,
                    'processing_method': 'mineru_local',
                    'processing_time': processing_time,
                    'char_count': len(markdown_content),
                    'word_count': len(markdown_content.split()),
                    'content_list': content_list,
                    'device': 'local'
                }]
                
        except Exception as e:
            print(f"❌ MinerU处理异常: {e}")
            return []
    
    def _process_with_qwen_api(self, file_path: str) -> List[Dict[str, Any]]:
        """使用Qwen-VL-Max API处理文档"""
        if not self.is_available():
            print("❌ Qwen API不可用")
            return []
        
        try:
            print(f"🌐 开始Qwen-VL-Max API处理: {Path(file_path).name}")
            start_time = time.time()
            
            # 将文件转换为base64
            with open(file_path, 'rb') as f:
                file_content = base64.b64encode(f.read()).decode('utf-8')
            
            # 构建API请求
            headers = {
                'Authorization': f'Bearer {self.qwen_api_key}',
                'Content-Type': 'application/json'
            }
            
            # 根据文件类型构建不同的请求
            file_ext = Path(file_path).suffix.lower()
            if file_ext == '.pdf':
                # PDF文档OCR请求
                data = {
                    'model': self.qwen_model,
                    'messages': [
                        {
                            'role': 'user',
                            'content': [
                                {
                                    'type': 'text',
                                    'text': '请对这个PDF文档进行OCR识别，提取所有文字内容，保持原有的格式和结构，以Markdown格式输出。'
                                },
                                {
                                    'type': 'file',
                                    'file': {
                                        'content': file_content,
                                        'content_type': 'application/pdf'
                                    }
                                }
                            ]
                        }
                    ],
                    'temperature': 0.1
                }
            else:
                # 图片OCR请求
                data = {
                    'model': self.qwen_model,
                    'messages': [
                        {
                            'role': 'user',
                            'content': [
                                {
                                    'type': 'text',
                                    'text': '请对这张图片进行OCR识别，提取所有文字内容，保持原有的格式和结构。'
                                },
                                {
                                    'type': 'image_url',
                                    'image_url': {
                                        'url': f'data:image/{file_ext[1:]};base64,{file_content}'
                                    }
                                }
                            ]
                        }
                    ],
                    'temperature': 0.1
                }
            
            # 发送API请求
            response = requests.post(
                'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
                headers=headers,
                json=data,
                timeout=300  # 5分钟超时
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code != 200:
                print(f"❌ Qwen API请求失败: {response.status_code} - {response.text}")
                return []
            
            result = response.json()
            
            if 'output' not in result or 'choices' not in result['output']:
                print(f"❌ Qwen API响应格式错误: {result}")
                return []
            
            # 提取OCR结果
            ocr_text = result['output']['choices'][0]['message']['content']
            
            print(f"✅ Qwen-VL-Max处理完成 ({processing_time:.2f}秒)")
            
            return [{
                'ocr_text': ocr_text,
                'confidence': 0.90,
                'processing_method': 'qwen_vl_max_api',
                'processing_time': processing_time,
                'char_count': len(ocr_text),
                'word_count': len(ocr_text.split()),
                'device': 'cloud_api'
            }]
            
        except Exception as e:
            print(f"❌ Qwen API处理异常: {e}")
            return []
    
    def format_ocr_results_to_markdown(self, ocr_results: List[Dict[str, Any]], 
                                     file_name: str) -> str:
        """
        将OCR结果格式化为Markdown
        
        Args:
            ocr_results: OCR结果列表
            file_name: 文件名
            
        Returns:
            str: Markdown格式的内容
        """
        if not ocr_results:
            return f"# {Path(file_name).stem}\n\n无法处理此文件。"
        
        result = ocr_results[0]
        markdown_content = result.get('ocr_text', '')
        
        # 添加处理信息头部
        method = "本地MinerU" if self.use_local else "Qwen-VL-Max API"
        header_lines = [
            f"# {Path(file_name).stem} ({method}解析结果)\n",
            f"**处理方法**: {result.get('processing_method', 'unknown')}",
            f"**处理时间**: {result.get('processing_time', 0):.2f}秒",
            f"**置信度**: {result.get('confidence', 0):.2f}",
            f"**字符数**: {result.get('char_count', 0)}",
            f"**单词数**: {result.get('word_count', 0)}",
            f"**设备**: {result.get('device', 'unknown')}\n",
            "---\n"
        ]
        
        return "\n".join(header_lines) + markdown_content
